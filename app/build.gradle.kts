plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace = "com.alrbea.androidapp"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.alrbea.androidapp"
        minSdk = 24
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildFeatures {
        viewBinding = true
        dataBinding = true

    }


    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
}

dependencies {

    implementation(libs.appcompat)
    implementation(libs.material)
    implementation(libs.activity)
    implementation(libs.constraintlayout)

    // ViewModel and LiveData
    implementation("androidx.lifecycle:lifecycle-viewmodel:2.7.0")
    implementation("androidx.lifecycle:lifecycle-livedata:2.7.0")
    implementation("androidx.lifecycle:lifecycle-common-java8:2.7.0")

    // GIF support
    implementation("pl.droidsonroids.gif:android-gif-drawable:1.2.29")

    // Fragment
    implementation("androidx.fragment:fragment:1.6.2")

    // RecyclerView
    implementation("androidx.recyclerview:recyclerview:1.3.2")

    // CardView
    implementation("androidx.cardview:cardview:1.0.0")



    // Permissions
    implementation("androidx.activity:activity-ktx:1.8.2")

    // SharedPreferences
    implementation("androidx.preference:preference:1.2.1")

    // Responsive Design Libraries
    implementation("com.intuit.sdp:sdp-android:1.1.0") // Scalable DP
    implementation("com.intuit.ssp:ssp-android:1.1.0") // Scalable SP for text

    // ConstraintLayout for responsive layouts
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")

    // FlexboxLayout for flexible layouts
    implementation("com.google.android.flexbox:flexbox:3.0.0")

    // WindowManager for screen size detection
    implementation("androidx.window:window:1.2.0")

    // Retrofit for API calls
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")

    // Gson
    implementation("com.google.code.gson:gson:2.10.1")

    // Room Database
    implementation("androidx.room:room-runtime:2.6.1")
    implementation("androidx.room:room-ktx:2.6.1")
    implementation(libs.circularprogressbar)
    annotationProcessor("androidx.room:room-compiler:2.6.1")

    testImplementation(libs.junit)
    androidTestImplementation(libs.ext.junit)
    androidTestImplementation(libs.espresso.core)
}