<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:name=".MyApp"
        android:theme="@style/Theme.AndroidApp"
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <!-- Splash Activity - Main Launcher -->
        <activity
            android:name=".ui.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.AndroidApp.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Login Activity -->
        <activity
            android:name=".ui.LoginActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- Enhanced Main Activity -->
        <activity
            android:name=".EnhancedMainActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- Location Setup Activity -->
        <activity
            android:name=".ui.LocationSetupActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- Orientation Setup Activity -->
        <activity
            android:name=".ui.OrientationSetupActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="false" />

        <!-- Settings Activity -->
        <activity
            android:name=".ui.SettingsActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity" />

        <!-- Advertisements Settings Activity -->
        <activity
            android:name=".ui.AdvertisementsSettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.SettingsActivity" />

        <!-- News Banner Settings Activity -->
        <activity
            android:name=".ui.NewsBannerSettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.AdvertisementsSettingsActivity" />

        <!-- Events Settings Activity -->
        <activity
            android:name=".ui.EventsSettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.AdvertisementsSettingsActivity" />

        <!-- Add/Edit Event Activity -->
        <activity
            android:name=".ui.AddEditEventActivity"
            android:exported="false"
            android:parentActivityName=".ui.EventsSettingsActivity" />

        <!-- Sermon Title Settings Activity -->
        <activity
            android:name=".ui.SermonTitleSettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.AdvertisementsSettingsActivity" />

        <!-- Funeral Prayer Settings Activity -->
        <activity
            android:name=".ui.FuneralPrayerSettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.AdvertisementsSettingsActivity" />

        <!-- Morning Evening Azkar Activity -->
        <activity
            android:name=".ui.MorningEveningAzkarActivity"
            android:exported="false"
            android:parentActivityName=".ui.AzkarSettingsActivity" />

        <!-- Post Prayer Azkar Activity -->
        <activity
            android:name=".ui.PostPrayerAzkarActivity"
            android:exported="false"
            android:parentActivityName=".ui.AzkarSettingsActivity" />

        <!-- Photo Gallery Settings Activity -->
        <activity
            android:name=".ui.PhotoGallerySettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.SettingsActivity" />

        <!-- Add/Edit Gallery Activity -->
        <activity
            android:name=".ui.AddEditGalleryActivity"
            android:exported="false"
            android:parentActivityName=".ui.PhotoGallerySettingsActivity" />

        <!-- Logo Design Activity -->
        <activity
            android:name=".ui.LogoDesignActivity"
            android:exported="false"
            android:parentActivityName=".ui.MainActivity"
            android:screenOrientation="landscape" />

        <!-- About Activity -->
        <activity
            android:name=".ui.AboutActivity"
            android:exported="false"
            android:parentActivityName=".ui.SettingsActivity" />

        <!-- Remote Control Activity -->
        <activity
            android:name=".ui.RemoteControlActivity"
            android:exported="false"
            android:parentActivityName=".ui.SettingsActivity" />

        <!-- Data Sync Activity -->
        <activity
            android:name=".ui.DataSyncActivity"
            android:exported="false"
            android:parentActivityName=".ui.SettingsActivity" />

        <!-- App Settings Activity -->
        <activity
            android:name=".ui.AppSettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.SettingsActivity" />

        <!-- Design Options Activity -->
        <activity
            android:name=".ui.DesignOptionsActivity"
            android:exported="false"
            android:parentActivityName=".ui.AppSettingsActivity" />

        <!-- Notification Settings Activity -->
        <activity
            android:name=".ui.NotificationSettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.AppSettingsActivity" />

        <!-- Device Settings Activity -->
        <activity
            android:name=".ui.DeviceSettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.AppSettingsActivity" />

        <!-- Sound Selector Activity -->
        <activity
            android:name=".ui.SoundSelectorActivity"
            android:exported="false"
            android:parentActivityName=".ui.NotificationSettingsActivity" />

        <!-- Azkar Templates Activity -->
        <activity
            android:name=".ui.AzkarTemplatesActivity"
            android:exported="false"
            android:parentActivityName=".ui.DesignOptionsActivity" />

        <!-- Theme Templates Activity -->
        <activity
            android:name=".ui.ThemeTemplatesActivity"
            android:exported="false"
            android:parentActivityName=".ui.DesignOptionsActivity" />

        <!-- Azkar Settings Activity -->
        <activity
            android:name=".ui.AzkarSettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.SettingsActivity" />

        <!-- Prayer Times Settings Activity -->
        <activity
            android:name=".ui.PrayerTimesSettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.SettingsActivity" />

        <!-- Prayer List Activity -->
        <activity
            android:name=".ui.PrayerListActivity"
            android:exported="false"
            android:parentActivityName=".ui.PrayerTimesSettingsActivity" />

        <!-- Prayer Details Activity -->
        <activity
            android:name=".ui.PrayerDetailsActivity"
            android:exported="false"
            android:parentActivityName=".ui.PrayerListActivity" />

        <!-- App Update Activity -->
        <activity
            android:name=".ui.AppUpdateActivity"
            android:exported="false"
            android:parentActivityName=".ui.SettingsActivity" />

        <!-- Funeral Announcement Activity -->
        <activity
            android:name=".ui.FuneralAnnouncementActivity"
            android:exported="false"
            android:parentActivityName=".ui.SettingsActivity" />

        <!-- File Provider for APK installation -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>