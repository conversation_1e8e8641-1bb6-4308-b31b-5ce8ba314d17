<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_12sdp"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="@dimen/_16sdp"
    app:cardElevation="@dimen/_8sdp"
    app:cardBackgroundColor="@color/card_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/_24sdp">

        <!-- Date Header -->
        <TextView
            android:id="@+id/tv_date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="التاريخ"
            android:textSize="@dimen/_20ssp"
            android:textStyle="bold"
            android:gravity="center"
            android:textColor="@color/primary"
            android:layout_marginBottom="@dimen/_16sdp"
            android:background="@drawable/prayer_time_background"
            android:padding="@dimen/_12sdp" />

        <!-- Prayer Times Grid for TV -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Row 1: Fajr, Sunrise, Dhuhr -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="3"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="@dimen/_8sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الفجر"
                        android:textSize="@dimen/_14ssp"
                        android:textColor="@color/fajr_color"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_fajr"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="05:30"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginTop="@dimen/_4sdp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="@dimen/_8sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الشروق"
                        android:textSize="@dimen/_14ssp"
                        android:textColor="@color/sunrise_color"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_sunrise"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="06:45"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginTop="@dimen/_4sdp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="@dimen/_8sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الظهر"
                        android:textSize="@dimen/_14ssp"
                        android:textColor="@color/dhuhr_color"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_dhuhr"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="12:15"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginTop="@dimen/_4sdp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Row 2: Asr, Maghrib, Isha -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="3">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="@dimen/_8sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="العصر"
                        android:textSize="@dimen/_14ssp"
                        android:textColor="@color/asr_color"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_asr"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="15:30"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginTop="@dimen/_4sdp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="@dimen/_8sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="المغرب"
                        android:textSize="@dimen/_14ssp"
                        android:textColor="@color/maghrib_color"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_maghrib"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="18:00"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginTop="@dimen/_4sdp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="@dimen/_8sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="العشاء"
                        android:textSize="@dimen/_14ssp"
                        android:textColor="@color/isha_color"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_isha"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="19:30"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginTop="@dimen/_4sdp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
