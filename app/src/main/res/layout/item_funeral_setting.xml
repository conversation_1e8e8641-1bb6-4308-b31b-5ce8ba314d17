<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_setting"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_8sdp"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="@dimen/_16sdp"
    app:cardElevation="@dimen/_8sdp"
    app:cardBackgroundColor="@color/card_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_20sdp"
        android:minHeight="100dp">

        <!-- Setting Icon -->
        <ImageView
            android:id="@+id/iv_setting_icon"
            android:layout_width="@dimen/_40sdp"
            android:layout_height="@dimen/_40sdp"
            android:scaleType="centerInside"
            android:background="@drawable/icon_background"
            android:padding="@dimen/_8sdp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:src="@drawable/ic_funeral" />

        <!-- Setting Title -->
        <TextView
            android:id="@+id/tv_setting_title"
            style="@style/PrayerApp.Text.Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginStart="@dimen/_16sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="@id/iv_setting_icon"
            app:layout_constraintStart_toEndOf="@id/iv_setting_icon"
            app:layout_constraintEnd_toStartOf="@id/switch_setting"
            tools:text="تفعيل إعلانات الجنازة" />

        <!-- Setting Description -->
        <TextView
            android:id="@+id/tv_setting_description"
            style="@style/PrayerApp.Text.Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_secondary"
            android:layout_marginStart="@dimen/_16sdp"
            android:layout_marginTop="@dimen/_4sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toBottomOf="@id/tv_setting_title"
            app:layout_constraintStart_toEndOf="@id/iv_setting_icon"
            app:layout_constraintEnd_toStartOf="@id/switch_setting"
            tools:text="تفعيل عرض إعلانات الصلاة على الميت" />

        <!-- Setting Value -->
        <TextView
            android:id="@+id/tv_setting_value"
            style="@style/PrayerApp.Text.Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/primary"
            android:textStyle="bold"
            android:layout_marginStart="@dimen/_16sdp"
            android:layout_marginTop="@dimen/_4sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tv_setting_description"
            app:layout_constraintStart_toEndOf="@id/iv_setting_icon"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="نص الإعلان"
            tools:visibility="visible" />

        <!-- Toggle Switch -->
        <Switch
            android:id="@+id/switch_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_8sdp"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="@id/iv_setting_icon"
            app:layout_constraintBottom_toBottomOf="@id/iv_setting_icon"
            app:layout_constraintEnd_toEndOf="parent"
            tools:checked="true"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
