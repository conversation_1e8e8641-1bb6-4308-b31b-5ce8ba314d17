<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_8sdp"
    app:cardCornerRadius="@dimen/_16sdp"
    app:cardElevation="@dimen/_4sdp">

    <ImageView
        android:id="@+id/img_theme_thumbnail"
        android:layout_width="match_parent"
        android:layout_height="180dp"
        android:scaleType="centerCrop"
        android:background="@color/card_background" />

</androidx.cardview.widget.CardView>