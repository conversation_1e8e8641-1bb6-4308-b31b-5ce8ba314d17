<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_24sdp">

        <!-- Header -->
        <ImageView
            android:id="@+id/iv_location_icon"
            android:layout_width="@dimen/_80sdp"
            android:layout_height="@dimen/_80sdp"
            android:src="@drawable/ic_location"
            android:contentDescription="@string/location_setup"
            android:layout_marginTop="@dimen/_32sdp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/PrayerApp.Text.Headline2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/location_setup_title"
            android:layout_marginTop="@dimen/_24sdp"
            app:layout_constraintTop_toBottomOf="@id/iv_location_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/tv_subtitle"
            style="@style/PrayerApp.Text.Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/location_setup_subtitle"
            android:textAlignment="center"
            android:layout_marginTop="@dimen/_8sdp"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Country Selection Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_country"
            style="@style/PrayerApp.Card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_32sdp"
            app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_20sdp">

                <TextView
                    style="@style/PrayerApp.Text.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/select_country"
                    android:layout_marginBottom="@dimen/_16sdp"
                    android:drawableStart="@drawable/ic_flag"
                    android:drawablePadding="@dimen/_8sdp" />

                <Spinner
                    android:id="@+id/spinner_country"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_48sdp"
                    android:background="@drawable/spinner_background"
                    android:padding="@dimen/_12sdp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- City Selection Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_city"
            style="@style/PrayerApp.Card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            app:layout_constraintTop_toBottomOf="@id/card_country"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_20sdp">

                <TextView
                    style="@style/PrayerApp.Text.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/select_city"
                    android:layout_marginBottom="@dimen/_16sdp"
                    android:drawableStart="@drawable/ic_city"
                    android:drawablePadding="@dimen/_8sdp" />

                <Spinner
                    android:id="@+id/spinner_city"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_48sdp"
                    android:background="@drawable/spinner_background"
                    android:padding="@dimen/_12sdp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Calculation Method Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_calculation_method"
            style="@style/PrayerApp.Card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            app:layout_constraintTop_toBottomOf="@id/card_city"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_20sdp">

                <TextView
                    style="@style/PrayerApp.Text.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/select_calculation_method"
                    android:layout_marginBottom="@dimen/_16sdp"
                    android:drawableStart="@drawable/ic_calculation"
                    android:drawablePadding="@dimen/_8sdp" />

                <Spinner
                    android:id="@+id/spinner_calculation_method"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_48sdp"
                    android:background="@drawable/spinner_background"
                    android:padding="@dimen/_12sdp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Selected Info Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_selected_info"
            style="@style/PrayerApp.Card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            app:layout_constraintTop_toBottomOf="@id/card_calculation_method"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <TextView
                    style="@style/PrayerApp.Text.Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/selected_settings"
                    android:layout_marginBottom="@dimen/_8sdp" />

                <TextView
                    android:id="@+id/tv_selected_info"
                    style="@style/PrayerApp.Text.Body1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/surface_variant"
                    android:padding="@dimen/_12sdp"
                    android:text="@string/no_selections_made"
                    android:minHeight="@dimen/_60sdp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Action Buttons -->
        <LinearLayout
            android:id="@+id/layout_buttons"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/_32sdp"
            android:layout_marginBottom="@dimen/_24sdp"
            app:layout_constraintTop_toBottomOf="@id/card_selected_info"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <Button
                android:id="@+id/btn_skip"
                style="@style/PrayerApp.Button.Outlined"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/skip"
                android:layout_marginEnd="@dimen/_8sdp" />

            <Button
                android:id="@+id/btn_continue"
                style="@style/PrayerApp.Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/continue_text"
                android:layout_marginStart="@dimen/_8sdp"
                android:enabled="false" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
