<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="@string/funeral_prayer_settings"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Header Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_16sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_20sdp">

                    <TextView
                        style="@style/PrayerApp.Text.Headline2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/funeral_prayer_settings"
                        android:textColor="@color/primary"
                        android:drawableStart="@drawable/ic_events"
                        android:drawablePadding="@dimen/_12sdp"
                        android:gravity="center_vertical" />

                    <TextView
                        style="@style/PrayerApp.Text.Body2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/funeral_prayer_settings_subtitle"
                        android:layout_marginTop="@dimen/_8sdp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Main Settings Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_20sdp">

                    <!-- Enable Switch -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_20sdp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/enable_funeral_messages"
                            android:textSize="@dimen/_16sdp"
                            android:textColor="@color/text_primary" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_enable_funeral"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false"
                            app:buttonTint="@color/primary"
                            app:thumbTint="@color/white"
                            app:trackTint="@color/primary" />

                    </LinearLayout>

                    <!-- Settings Container -->
                    <LinearLayout
                        android:id="@+id/layout_settings_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <!-- Template Messages Section -->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_8sdp"
                            android:text="@string/template_messages"
                            android:textColor="@color/text_primary"
                            android:textSize="@dimen/_16sdp"
                            android:textStyle="bold" />

                        <Spinner
                            android:id="@+id/spinner_template_messages"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_20sdp"
                            android:background="@drawable/card_background"
                            android:padding="@dimen/_12sdp"
                            android:spinnerMode="dropdown" />

                        <!-- Prayer Selection -->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_8sdp"
                            android:text="اختيار الصلاة"
                            android:textColor="@color/text_primary"
                            android:textSize="@dimen/_16sdp"
                            android:textStyle="bold" />

                        <Spinner
                            android:id="@+id/spinner_prayer_selection"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_20sdp"
                            android:background="@drawable/card_background"
                            android:padding="@dimen/_12sdp"
                            android:spinnerMode="dropdown" />

                        <!-- Save Button -->
                        <Button
                            android:id="@+id/btn_save"
                            style="@style/PrayerApp.Button"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/button_primary_background"
                            android:text="@string/save"
                            android:textColor="@color/text_on_primary"
                            android:textSize="@dimen/_16sdp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
