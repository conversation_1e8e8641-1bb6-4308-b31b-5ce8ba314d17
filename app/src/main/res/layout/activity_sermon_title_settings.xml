<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="@string/sermon_title_settings"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Header Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_16sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_20sdp">

                    <TextView
                        style="@style/PrayerApp.Text.Headline2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/sermon_title_settings"
                        android:textColor="@color/primary"
                        android:drawableStart="@drawable/ic_news"
                        android:drawablePadding="@dimen/_12sdp"
                        android:gravity="center_vertical" />

                    <TextView
                        style="@style/PrayerApp.Text.Body2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/sermon_title_settings_subtitle"
                        android:layout_marginTop="@dimen/_8sdp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Main Settings Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_20sdp">

                    <!-- Enable Switch -->
                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/switch_enable_sermon_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_20sdp"
                        android:checked="false"
                        android:text="@string/enable_sermon_title"
                        android:textSize="@dimen/_16sdp"
                        android:textColor="@color/text_primary"
                        app:buttonTint="@color/primary"
                        app:thumbTint="@color/white"
                        app:trackTint="@color/primary" />

                    <!-- Settings Container -->
                    <LinearLayout
                        android:id="@+id/layout_settings_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <!-- Info Text -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_16sdp"
                            android:gravity="center"
                            android:text="سيتم عرض عنوان الخطبة يوم الجمعة في مكان شريط التاريخ"
                            android:textColor="@color/text_secondary"
                            android:textSize="@dimen/_14sdp" />

                        <!-- Sermon Title Input -->
                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/PrayerApp.EditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_20sdp"
                            android:hint="@string/sermon_title">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_sermon_title"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="start|top"
                                android:inputType="textMultiLine"
                                android:lines="4"
                                android:maxLines="6"
                                android:textColor="@color/text_primary"
                                android:textSize="@dimen/_14sdp" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Save Button -->
                        <Button
                            android:id="@+id/btn_save"
                            style="@style/PrayerApp.Button"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/button_primary_background"
                            android:text="@string/save"
                            android:textColor="@color/text_on_primary"
                            android:textSize="@dimen/_16sdp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>