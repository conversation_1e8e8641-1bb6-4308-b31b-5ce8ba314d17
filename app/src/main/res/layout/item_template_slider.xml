<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_template"
    style="@style/PrayerApp.Card"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginStart="@dimen/_8sdp"
    android:layout_marginEnd="@dimen/_8sdp"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardElevation="@dimen/_4sdp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Template Thumbnail -->
        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/gif_template_thumbnail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:background="@color/card_background" />

        <!-- Loading Indicator -->
        <ProgressBar
            android:id="@+id/progress_template_loading"
            android:layout_width="@dimen/_24sdp"
            android:layout_height="@dimen/_24sdp"
            android:layout_gravity="center"
            android:indeterminateTint="@color/primary"
            android:visibility="gone" />

        <!-- Download Status Overlay -->
        <LinearLayout
            android:id="@+id/layout_template_download_status"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:background="#80000000"
            android:visibility="gone">

            <ImageView
                android:id="@+id/icon_template_download"
                android:layout_width="@dimen/_32sdp"
                android:layout_height="@dimen/_32sdp"
                android:src="@drawable/ic_download"
                android:tint="@color/white"
                android:layout_marginBottom="@dimen/_4sdp" />

            <TextView
                android:id="@+id/text_template_download"
                style="@style/PrayerApp.Text.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="تحميل"
                android:textColor="@color/white"
                android:gravity="center" />

        </LinearLayout>

        <!-- Template Info Overlay -->
        <LinearLayout
            android:id="@+id/layout_template_info_overlay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@drawable/gradient_overlay_small"
            android:orientation="vertical"
            android:padding="@dimen/_8sdp">

            <TextView
                android:id="@+id/text_template_title"
                style="@style/PrayerApp.Text.Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="قالب 1"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:shadowColor="@color/black"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="2"
                android:maxLines="1"
                android:ellipsize="end" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/_2sdp">

                <TextView
                    android:id="@+id/text_template_type"
                    style="@style/PrayerApp.Text.Caption"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="محلي"
                    android:textColor="@color/white"
                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="2" />

                <!-- Status Icon -->
                <ImageView
                    android:id="@+id/icon_template_status_small"
                    android:layout_width="@dimen/_16sdp"
                    android:layout_height="@dimen/_16sdp"
                    android:src="@drawable/ic_check"
                    android:tint="@color/white"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

        <!-- Selection Indicator -->
        <View
            android:id="@+id/view_selection_indicator"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_4sdp"
            android:layout_gravity="top"
            android:background="@color/primary"
            android:visibility="gone" />

    </FrameLayout>

</androidx.cardview.widget.CardView>
