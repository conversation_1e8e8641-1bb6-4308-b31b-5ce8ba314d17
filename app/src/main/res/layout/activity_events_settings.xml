<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="@string/events_settings_title"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Header Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_16sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_20sdp">

                    <TextView
                        style="@style/PrayerApp.Text.Headline2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/events_settings_title"
                        android:textColor="@color/primary"
                        android:drawableStart="@drawable/ic_events"
                        android:drawablePadding="@dimen/_12sdp"
                        android:gravity="center_vertical" />

                    <TextView
                        style="@style/PrayerApp.Text.Body2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/events_settings_subtitle"
                        android:layout_marginTop="@dimen/_8sdp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Main Settings Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_20sdp">

                    <!-- Enable Switch -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_20sdp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/enable_events"
                            android:textSize="@dimen/_16sdp"
                            android:textColor="@color/text_primary" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_enable_events"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false"
                            app:buttonTint="@color/primary"
                            app:thumbTint="@color/white"
                            app:trackTint="@color/primary" />

                    </LinearLayout>

                    <!-- Settings Container -->
                    <LinearLayout
                        android:id="@+id/layout_settings_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <!-- Add Event Button -->
                        <Button
                            android:id="@+id/btn_add_event"
                            style="@style/PrayerApp.Button.Outlined"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_16sdp"
                            android:text="@string/add_event"
                            android:drawableStart="@drawable/ic_add_photo"
                            android:drawablePadding="@dimen/_8sdp" />

                        <!-- Events List -->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycler_view_events"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:nestedScrollingEnabled="false"
                            android:clipToPadding="false" />

                        <!-- Empty State -->
                        <LinearLayout
                            android:id="@+id/layout_empty_state"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_32sdp"
                            android:layout_marginBottom="@dimen/_32sdp"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:visibility="gone">

                            <ImageView
                                android:layout_width="@dimen/_60sdp"
                                android:layout_height="@dimen/_60sdp"
                                android:layout_marginBottom="@dimen/_16sdp"
                                android:src="@drawable/ic_events"
                                android:alpha="0.5" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/no_events_added"
                                android:textColor="@color/text_secondary"
                                android:textSize="@dimen/_16sdp"
                                android:gravity="center" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
