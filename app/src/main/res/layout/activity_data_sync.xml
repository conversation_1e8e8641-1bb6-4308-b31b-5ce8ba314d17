<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Sync Status Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Status Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:id="@+id/icon_sync_status"
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_sync"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="حالة المزامنة"
                                android:textColor="@color/text_primary" />

                            <TextView
                                android:id="@+id/text_sync_status"
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="جاهز للمزامنة"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/text_last_sync"
                            style="@style/PrayerApp.Text.Caption"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="لم تتم المزامنة"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <!-- Progress Bar -->
                    <ProgressBar
                        android:id="@+id/progress_sync"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_4sdp"
                        android:max="100"
                        android:progress="0"
                        android:progressTint="@color/primary"
                        android:progressBackgroundTint="@color/divider"
                        android:visibility="gone" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Upload Settings Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Upload Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_upload"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Headline3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="رفع الإعدادات"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Client ID Input -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_12sdp"
                        android:hint="معرف العميل"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_client_id"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:textDirection="ltr" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Backup Name Input -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_16sdp"
                        android:hint="اسم النسخة الاحتياطية"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_backup_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Upload Button -->
                    <Button
                        android:id="@+id/btn_upload_settings"
                        style="@style/Widget.AppCompat.Button.Borderless"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_48sdp"
                        android:background="@drawable/button_primary_background"
                        android:text="رفع جميع الإعدادات"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_14sdp"
                        android:drawableStart="@drawable/ic_upload"
                        android:drawablePadding="@dimen/_8sdp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Download Settings Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Download Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_download"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Headline3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="استرجاع الإعدادات"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Client ID for Download -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_12sdp"
                        android:hint="معرف العميل"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_download_client_id"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:textDirection="ltr" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Load Backups Button -->
                    <Button
                        android:id="@+id/btn_load_backups"
                        style="@style/Widget.AppCompat.Button.Borderless"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_48sdp"
                        android:layout_marginBottom="@dimen/_12sdp"
                        android:background="@drawable/button_secondary_background"
                        android:text="تحميل قائمة النسخ الاحتياطية"
                        android:textColor="@color/primary"
                        android:textSize="@dimen/_14sdp"
                        android:drawableStart="@drawable/ic_refresh"
                        android:drawablePadding="@dimen/_8sdp" />

                    <!-- Backups List -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_backups"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:visibility="gone" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Settings Categories Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Categories Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_settings"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Headline3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="فئات الإعدادات"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Settings Categories List -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_settings_categories"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
