<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <!-- Header -->
        <ImageView
            android:id="@+id/iv_orientation_icon"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@drawable/ic_screen_rotation"
            android:contentDescription="@string/screen_orientation"
            android:layout_marginTop="32dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/PrayerApp.Text.Headline2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/orientation_setup_title"
            android:layout_marginTop="24dp"
            app:layout_constraintTop_toBottomOf="@id/iv_orientation_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/tv_subtitle"
            style="@style/PrayerApp.Text.Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/orientation_setup_subtitle"
            android:textAlignment="center"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Orientation Options Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_orientation_options"
            style="@style/PrayerApp.Card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    style="@style/PrayerApp.Text.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/choose_orientation"
                    android:layout_marginBottom="16dp" />

                <RadioGroup
                    android:id="@+id/rg_orientation"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <RadioButton
                        android:id="@+id/rb_portrait"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/portrait_mode"
                        android:textSize="16sp"
                        android:padding="12dp"
                        android:drawableStart="@drawable/ic_portrait"
                        android:drawablePadding="12dp"
                        android:background="?android:attr/selectableItemBackground"
                        android:layout_marginBottom="8dp" />

                    <RadioButton
                        android:id="@+id/rb_landscape"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/landscape_mode"
                        android:textSize="16sp"
                        android:padding="12dp"
                        android:drawableStart="@drawable/ic_landscape"
                        android:drawablePadding="12dp"
                        android:background="?android:attr/selectableItemBackground"
                        android:layout_marginBottom="8dp" />

                    <RadioButton
                        android:id="@+id/rb_reverse_landscape"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/reverse_landscape_mode"
                        android:textSize="16sp"
                        android:padding="12dp"
                        android:drawableStart="@drawable/ic_landscape_reverse"
                        android:drawablePadding="12dp"
                        android:background="?android:attr/selectableItemBackground"
                        android:layout_marginBottom="8dp" />

                    <RadioButton
                        android:id="@+id/rb_sensor_portrait"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/sensor_portrait_mode"
                        android:textSize="16sp"
                        android:padding="12dp"
                        android:drawableStart="@drawable/ic_screen_rotation"
                        android:drawablePadding="12dp"
                        android:background="?android:attr/selectableItemBackground" />

                </RadioGroup>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Preview Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_preview"
            style="@style/PrayerApp.Card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@id/card_orientation_options"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="@style/PrayerApp.Text.Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/preview"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_preview"
                    style="@style/PrayerApp.Text.Body1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/surface_variant"
                    android:padding="12dp"
                    android:text="@string/select_orientation_to_preview" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Action Buttons -->
        <LinearLayout
            android:id="@+id/layout_buttons"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="32dp"
            android:layout_marginBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/card_preview"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <Button
                android:id="@+id/btn_skip"
                style="@style/PrayerApp.Button.Outlined"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/skip"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_continue"
                style="@style/PrayerApp.Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/continue_text"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
