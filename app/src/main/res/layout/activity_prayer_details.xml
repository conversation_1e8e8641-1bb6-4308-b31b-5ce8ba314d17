<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Prayer Announcement Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_settings"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Headline3"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="إعلان موعد الصلاة"
                            android:textColor="@color/text_primary" />

                        <Switch
                            android:id="@+id/switch_announcement"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            app:buttonTint="@color/primary"
                            app:thumbTint="@color/white"
                            app:trackTint="@color/primary" />

                    </LinearLayout>

                    <TextView
                        style="@style/PrayerApp.Text.Body2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="إظهار إعلان دخول وقت الصلاة لمدة أربع دقائق بعد الأذان"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Timing Settings Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Timing between Adhan and Iqama -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_time"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="توقيت ما بين الأذان و الإقامة"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Iqama Time Setting -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="@drawable/card_background"
                        android:padding="@dimen/_12sdp"
                        android:layout_marginBottom="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_time"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="وقت إقامة الصلاة نايت"
                            android:textColor="@color/text_secondary" />

                        <TextView
                            android:id="@+id/text_iqama_time"
                            style="@style/PrayerApp.Text.Body1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="12:00 AM"
                            android:textColor="@color/text_primary"
                            android:background="@drawable/time_background"
                            android:padding="@dimen/_8sdp"
                            android:layout_marginStart="@dimen/_8sdp" />

                    </LinearLayout>

                    <!-- Iqama Announcement Duration -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_duration"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="مدة إعلان الإقامة"
                            android:textColor="@color/text_secondary" />

                        <!-- Duration Counter -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:background="@drawable/card_background"
                            android:padding="@dimen/_4sdp">

                            <ImageButton
                                android:id="@+id/btn_decrease_iqama"
                                android:layout_width="@dimen/_32sdp"
                                android:layout_height="@dimen/_32sdp"
                                android:background="?attr/selectableItemBackgroundBorderless"
                                android:src="@android:drawable/ic_menu_delete"
                                android:tint="@color/primary"
                                android:contentDescription="تقليل المدة" />

                            <TextView
                                android:id="@+id/text_iqama_duration"
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="@dimen/_40sdp"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:text="10"
                                android:textColor="@color/primary"
                                android:textStyle="bold" />

                            <ImageButton
                                android:id="@+id/btn_increase_iqama"
                                android:layout_width="@dimen/_32sdp"
                                android:layout_height="@dimen/_32sdp"
                                android:background="?attr/selectableItemBackgroundBorderless"
                                android:src="@android:drawable/ic_menu_add"
                                android:tint="@color/primary"
                                android:contentDescription="زيادة المدة" />

                        </LinearLayout>

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="دقيقة"
                            android:textColor="@color/text_secondary"
                            android:layout_marginStart="@dimen/_8sdp" />

                    </LinearLayout>

                    <!-- Prayer Duration -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_duration"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="الوقت المستغرق لأداء الصلاة"
                            android:textColor="@color/text_secondary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="قبل انتهاء أداء الصلاة"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <!-- Prayer Performance Duration -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_duration"
                            android:tint="@color/primary" />

                        <TextView
                            android:id="@+id/text_prayer_performance_label"
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="الوقت المستغرق لأداء صلاة الفجر يوم الجمعة"
                            android:textColor="@color/text_secondary" />

                        <!-- Duration Counter -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:background="@drawable/card_background"
                            android:padding="@dimen/_4sdp">

                            <ImageButton
                                android:id="@+id/btn_decrease_prayer"
                                android:layout_width="@dimen/_32sdp"
                                android:layout_height="@dimen/_32sdp"
                                android:background="?attr/selectableItemBackgroundBorderless"
                                android:src="@android:drawable/ic_menu_delete"
                                android:tint="@color/primary"
                                android:contentDescription="تقليل المدة" />

                            <TextView
                                android:id="@+id/text_prayer_duration"
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="@dimen/_40sdp"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:text="10"
                                android:textColor="@color/primary"
                                android:textStyle="bold" />

                            <ImageButton
                                android:id="@+id/btn_increase_prayer"
                                android:layout_width="@dimen/_32sdp"
                                android:layout_height="@dimen/_32sdp"
                                android:background="?attr/selectableItemBackgroundBorderless"
                                android:src="@android:drawable/ic_menu_add"
                                android:tint="@color/primary"
                                android:contentDescription="زيادة المدة" />

                        </LinearLayout>

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="دقيقة"
                            android:textColor="@color/text_secondary"
                            android:layout_marginStart="@dimen/_8sdp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Dhuha Prayer Announcement Card (for specific prayers) -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_dhuha_announcement"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_sun_moon"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Headline3"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="إعلان موعد صلاة الضحى"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <TextView
                        style="@style/PrayerApp.Text.Body2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="إعلان وقت صلاة الضحى بعد الشروق بربع ساعة لمدة 5 دقائق"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="@dimen/_12sdp" />

                    <!-- Duration Counter -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="مدة الإعلان"
                            android:textColor="@color/text_secondary" />

                        <!-- Duration Counter -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:background="@drawable/card_background"
                            android:padding="@dimen/_4sdp">

                            <ImageButton
                                android:id="@+id/btn_decrease_dhuha"
                                android:layout_width="@dimen/_32sdp"
                                android:layout_height="@dimen/_32sdp"
                                android:background="?attr/selectableItemBackgroundBorderless"
                                android:src="@android:drawable/ic_menu_delete"
                                android:tint="@color/primary"
                                android:contentDescription="تقليل المدة" />

                            <TextView
                                android:id="@+id/text_dhuha_duration"
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="@dimen/_40sdp"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:text="15"
                                android:textColor="@color/primary"
                                android:textStyle="bold" />

                            <ImageButton
                                android:id="@+id/btn_increase_dhuha"
                                android:layout_width="@dimen/_32sdp"
                                android:layout_height="@dimen/_32sdp"
                                android:background="?attr/selectableItemBackgroundBorderless"
                                android:src="@android:drawable/ic_menu_add"
                                android:tint="@color/primary"
                                android:contentDescription="زيادة المدة" />

                        </LinearLayout>

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="دقيقة"
                            android:textColor="@color/text_secondary"
                            android:layout_marginStart="@dimen/_8sdp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
