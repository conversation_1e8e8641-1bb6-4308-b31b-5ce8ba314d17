<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/PrayerApp.Card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/_12sdp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/_16sdp">

        <!-- Prayer Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/icon_prayer"
                android:layout_width="@dimen/_32sdp"
                android:layout_height="@dimen/_32sdp"
                android:layout_marginEnd="@dimen/_12sdp"
                android:src="@drawable/ic_prayer_times"
                android:tint="@color/primary" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/text_prayer_name"
                    style="@style/PrayerApp.Text.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/text_prayer_time"
                    style="@style/PrayerApp.Text.Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="@dimen/_4sdp" />

            </LinearLayout>

            <ImageView
                android:layout_width="@dimen/_24sdp"
                android:layout_height="@dimen/_24sdp"
                android:src="@drawable/ic_arrow_forward"
                android:tint="@color/text_secondary" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
