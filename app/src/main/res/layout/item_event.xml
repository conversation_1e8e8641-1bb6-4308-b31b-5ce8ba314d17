<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_event"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_8sdp"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="@dimen/_16sdp"
    app:cardElevation="@dimen/_8sdp"
    app:cardBackgroundColor="@color/card_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_16sdp">

        <!-- Event Icon -->
        <ImageView
            android:id="@+id/iv_event_icon"
            android:layout_width="@dimen/_40sdp"
            android:layout_height="@dimen/_40sdp"
            android:scaleType="centerInside"
            android:background="@drawable/icon_background"
            android:padding="@dimen/_8sdp"
            android:src="@drawable/ic_events"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- Event Title -->
        <TextView
            android:id="@+id/tv_event_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:textSize="@dimen/_16sdp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="@id/iv_event_icon"
            app:layout_constraintStart_toEndOf="@id/iv_event_icon"
            app:layout_constraintEnd_toStartOf="@id/switch_event_enabled"
            tools:text="رمضان كريم" />

        <!-- Event Message -->
        <TextView
            android:id="@+id/tv_event_message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12sdp"
            android:layout_marginTop="@dimen/_4sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:textSize="@dimen/_14sdp"
            android:textColor="@color/text_secondary"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toBottomOf="@id/tv_event_title"
            app:layout_constraintStart_toEndOf="@id/iv_event_icon"
            app:layout_constraintEnd_toStartOf="@id/switch_event_enabled"
            tools:text="رمضان كريم وكل عام وأنتم بخير" />

        <!-- Prayer Info -->
        <TextView
            android:id="@+id/tv_event_prayer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12sdp"
            android:layout_marginTop="@dimen/_4sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:textSize="@dimen/_12sdp"
            android:textColor="@color/primary"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintTop_toBottomOf="@id/tv_event_message"
            app:layout_constraintStart_toEndOf="@id/iv_event_icon"
            app:layout_constraintEnd_toStartOf="@id/switch_event_enabled"
            tools:text="الفجر" />

        <!-- Enable Switch -->
        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_event_enabled"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            app:buttonTint="@color/primary"
            app:thumbTint="@color/white"
            app:trackTint="@color/primary"
            app:layout_constraintTop_toTopOf="@id/iv_event_icon"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Action Buttons -->
        <LinearLayout
            android:id="@+id/layout_actions"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12sdp"
            android:orientation="horizontal"
            android:gravity="end"
            app:layout_constraintTop_toBottomOf="@id/tv_event_prayer"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <Button
                android:id="@+id/btn_edit_event"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_8sdp"
                android:text="@string/edit_event"
                android:textColor="@color/primary"
                android:textSize="@dimen/_12sdp" />

            <Button
                android:id="@+id/btn_delete_event"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/delete_event"
                android:textColor="@color/error"
                android:textSize="@dimen/_12sdp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
