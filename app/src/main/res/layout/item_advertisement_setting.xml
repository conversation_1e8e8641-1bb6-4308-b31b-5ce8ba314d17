<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_setting"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_6sdp"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="@dimen/_20sdp"
    app:cardElevation="@dimen/_10sdp"
    app:cardBackgroundColor="@color/card_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_16sdp"
        android:minHeight="140dp">

        <!-- Setting Icon -->
        <ImageView
            android:id="@+id/iv_setting_icon"
            android:layout_width="@dimen/_56sdp"
            android:layout_height="@dimen/_56sdp"
            android:layout_marginTop="@dimen/_12sdp"
            android:scaleType="centerInside"
            android:background="@drawable/icon_background"
            android:padding="@dimen/_14sdp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:src="@drawable/ic_news" />

        <!-- Setting Title -->
        <TextView
            android:id="@+id/tv_setting_title"
            style="@style/PrayerApp.Text.Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:layout_marginTop="@dimen/_12sdp"
            android:layout_marginBottom="@dimen/_12sdp"
            android:textAlignment="center"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:maxLines="2"
            android:ellipsize="end"
            android:gravity="center"
            android:textSize="14sp"
            app:layout_constraintTop_toBottomOf="@id/iv_setting_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="الشريط الإخباري" />

        <!-- Setting Badge -->
        <TextView
            android:id="@+id/tv_setting_badge"
            android:layout_width="@dimen/_22sdp"
            android:layout_height="@dimen/_22sdp"
            android:background="@drawable/badge_background"
            android:text="!"
            android:textColor="@color/white"
            android:textSize="@dimen/_11ssp"
            android:textStyle="bold"
            android:gravity="center"
            android:visibility="gone"
            android:layout_marginTop="@dimen/_6sdp"
            android:layout_marginEnd="@dimen/_6sdp"
            app:layout_constraintTop_toTopOf="@id/iv_setting_icon"
            app:layout_constraintEnd_toEndOf="@id/iv_setting_icon"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
