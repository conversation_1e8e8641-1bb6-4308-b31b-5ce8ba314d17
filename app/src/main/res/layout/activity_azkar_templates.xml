<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Single Full Page Card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_azkar_main"
        style="@style/PrayerApp.Card"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="@dimen/_16sdp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        app:cardElevation="@dimen/_8sdp">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Azkar Preview Image -->
            <pl.droidsonroids.gif.GifImageView
                android:id="@+id/img_azkar_preview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/card_background" />

            <!-- Loading Indicator -->
            <ProgressBar
                android:id="@+id/progress_loading"
                android:layout_width="@dimen/_48sdp"
                android:layout_height="@dimen/_48sdp"
                android:layout_gravity="center"
                android:indeterminateTint="@color/primary"
                android:visibility="gone" />

            <!-- Download Status Overlay -->
            <LinearLayout
                android:id="@+id/layout_download_status"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:background="#80000000"
                android:visibility="gone"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:id="@+id/icon_download_status"
                    android:layout_width="@dimen/_64sdp"
                    android:layout_height="@dimen/_64sdp"
                    android:src="@drawable/ic_download"
                    android:tint="@color/white"
                    android:layout_marginBottom="@dimen/_16sdp" />

                <TextView
                    android:id="@+id/text_download_status"
                    style="@style/PrayerApp.Text.Headline4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="اضغط للتحميل"
                    android:textColor="@color/white"
                    android:gravity="center" />

                <ProgressBar
                    android:id="@+id/progress_download"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16sdp"
                    android:progressTint="@color/primary"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- Top Azkar Info Overlay -->
            <LinearLayout
                android:id="@+id/layout_azkar_info_top"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:background="@drawable/gradient_overlay_top"
                android:orientation="horizontal"
                android:padding="@dimen/_16sdp"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/text_azkar_name"
                        style="@style/PrayerApp.Text.Headline3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="قالب أذكار الصباح"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        android:shadowColor="@color/black"
                        android:shadowDx="2"
                        android:shadowDy="2"
                        android:shadowRadius="4" />

                    <TextView
                        android:id="@+id/text_azkar_description"
                        style="@style/PrayerApp.Text.Body1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="قالب جميل لعرض أذكار الصباح والمساء"
                        android:textColor="@color/white"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="2"
                        android:layout_marginTop="@dimen/_4sdp" />

                </LinearLayout>

                <!-- Download/Status Icon -->
                <ImageView
                    android:id="@+id/icon_azkar_status"
                    android:layout_width="@dimen/_40sdp"
                    android:layout_height="@dimen/_40sdp"
                    android:src="@drawable/ic_download"
                    android:tint="@color/white"
                    android:background="@drawable/circle_background"
                    android:padding="@dimen/_8sdp"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- Bottom Controls Overlay -->
            <LinearLayout
                android:id="@+id/layout_bottom_controls"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:background="@drawable/gradient_overlay"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <!-- Azkar Features -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="@dimen/_16sdp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/text_azkar_type"
                            style="@style/PrayerApp.Text.Caption"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="أذكار الصباح"
                            android:textColor="@color/white"
                            android:background="@drawable/badge_background"
                            android:padding="@dimen/_6sdp"
                            android:layout_marginEnd="@dimen/_8sdp" />

                        <TextView
                            android:id="@+id/text_azkar_category"
                            style="@style/PrayerApp.Text.Caption"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="محلي"
                            android:textColor="@color/white"
                            android:background="@drawable/badge_background"
                            android:padding="@dimen/_6sdp" />

                    </LinearLayout>

                    <!-- Orientation Toggle -->
                    <com.google.android.material.button.MaterialButtonToggleGroup
                        android:id="@+id/toggle_orientation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:singleSelection="true"
                        app:selectionRequired="true">

                        <Button
                            android:id="@+id/btn_portrait"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="عمودي"
                            android:textSize="10sp"
                            android:textColor="@color/white"
                            android:minWidth="0dp"
                            android:paddingStart="@dimen/_8sdp"
                            android:paddingEnd="@dimen/_8sdp" />

                        <Button
                            android:id="@+id/btn_landscape"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="أفقي"
                            android:textSize="10sp"
                            android:textColor="@color/white"
                            android:minWidth="0dp"
                            android:paddingStart="@dimen/_8sdp"
                            android:paddingEnd="@dimen/_8sdp" />

                    </com.google.android.material.button.MaterialButtonToggleGroup>

                </LinearLayout>

                <!-- 3D ViewPager2 for Azkar Templates -->
                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/viewpager_azkar"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="@dimen/_12sdp" />

                <!-- Azkar Indicators -->
                <LinearLayout
                    android:id="@+id/layout_indicators"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_marginBottom="@dimen/_16sdp" />

                <!-- Action Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/btn_download_azkar"
                        style="@style/Widget.AppCompat.Button.Borderless"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="@dimen/_4sdp"
                        android:background="@drawable/button_secondary_background"
                        android:text="تحميل"
                        android:textColor="@color/primary"
                        android:textSize="12sp"
                        android:drawableStart="@drawable/ic_download"
                        android:drawablePadding="@dimen/_4sdp"
                        android:visibility="gone" />

                    <Button
                        android:id="@+id/btn_preview_azkar"
                        style="@style/Widget.AppCompat.Button.Borderless"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="@dimen/_2sdp"
                        android:layout_marginEnd="@dimen/_2sdp"
                        android:background="@drawable/button_secondary_background"
                        android:text="معاينة"
                        android:textColor="@color/primary"
                        android:textSize="12sp"
                        android:drawableStart="@drawable/ic_preview"
                        android:drawablePadding="@dimen/_4sdp" />

                    <Button
                        android:id="@+id/btn_apply_azkar"
                        style="@style/Widget.AppCompat.Button.Borderless"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="@dimen/_4sdp"
                        android:background="@drawable/button_primary_background"
                        android:text="تطبيق"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:drawableStart="@drawable/ic_check"
                        android:drawablePadding="@dimen/_4sdp" />

                </LinearLayout>

            </LinearLayout>

        </FrameLayout>

    </androidx.cardview.widget.CardView>

    <!-- Floating Action Button for Refresh -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_refresh"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="@dimen/_16sdp"
        android:src="@drawable/ic_refresh"
        app:tint="@color/white"
        app:backgroundTint="@color/primary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
