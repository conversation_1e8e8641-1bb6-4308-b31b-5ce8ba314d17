<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="@string/news_banner_settings_title"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Header Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_16sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_20sdp">

                    <TextView
                        style="@style/PrayerApp.Text.Headline2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/news_banner_settings_title"
                        android:textColor="@color/primary"
                        android:drawableStart="@drawable/ic_news"
                        android:drawablePadding="@dimen/_12sdp"
                        android:gravity="center_vertical" />

                    <TextView
                        style="@style/PrayerApp.Text.Body2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/news_banner_settings_subtitle"
                        android:layout_marginTop="@dimen/_8sdp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Main Settings Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_20sdp">

                    <!-- Enable Switch -->
                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/switch_enable_news"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_20sdp"
                        android:checked="false"
                        android:text="@string/enable_news_ticker"
                        android:textSize="@dimen/_16sdp"
                        android:textColor="@color/text_primary"
                        app:buttonTint="@color/primary"
                        app:thumbTint="@color/white"
                        app:trackTint="@color/primary" />

                    <!-- Settings Container -->
                    <LinearLayout
                        android:id="@+id/layout_settings_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <!-- Ready Texts Section -->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_12sdp"
                            android:includeFontPadding="false"
                            android:text="@string/ready_texts"
                            android:textColor="@color/text_primary"
                            android:textSize="@dimen/_16sdp"
                            android:textStyle="bold" />

                        <Spinner
                            android:id="@+id/spinner_ready_texts"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_20sdp"
                            android:background="@drawable/card_background"
                            android:padding="@dimen/_12sdp"
                            android:spinnerMode="dropdown" />

                        <!-- Info Text -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_16sdp"
                            android:gravity="center"
                            android:text="@string/the_message_will_be_displayed_inplace_of_date_bar_you_can_back_to_disable_it_in_any_time"
                            android:textColor="@color/text_secondary"
                            android:textSize="@dimen/_14sdp" />

                        <!-- Message Input -->
                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/PrayerApp.EditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_20sdp"
                            android:hint="@string/body_of_message">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_message"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="start|top"
                                android:inputType="textMultiLine"
                                android:lines="4"
                                android:maxLines="6"
                                android:textColor="@color/text_primary"
                                android:textSize="@dimen/_14sdp" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Date Selection -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/_20sdp"
                            android:orientation="horizontal"
                            android:weightSum="2">

                            <com.google.android.material.textfield.TextInputLayout
                                style="@style/PrayerApp.EditText"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/_8sdp"
                                android:layout_weight="1"
                                android:hint="@string/start_date">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/edit_start_date"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableEnd="@drawable/ic_calendar"
                                    android:focusable="false"
                                    android:focusableInTouchMode="false"
                                    android:inputType="none"
                                    android:textColor="@color/text_primary"
                                    android:textSize="@dimen/_14sdp" />

                            </com.google.android.material.textfield.TextInputLayout>

                            <com.google.android.material.textfield.TextInputLayout
                                style="@style/PrayerApp.EditText"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/_8sdp"
                                android:layout_weight="1"
                                android:hint="@string/end_date">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/edit_end_date"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableEnd="@drawable/ic_calendar"
                                    android:focusable="false"
                                    android:focusableInTouchMode="false"
                                    android:inputType="none"
                                    android:textColor="@color/text_primary"
                                    android:textSize="@dimen/_14sdp" />

                            </com.google.android.material.textfield.TextInputLayout>

                        </LinearLayout>

                        <!-- Save Button -->
                        <Button
                            android:id="@+id/btn_save"
                            style="@style/PrayerApp.Button"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/button_primary_background"
                            android:text="@string/save"
                            android:textColor="@color/text_on_primary"
                            android:textSize="@dimen/_16sdp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
