<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_sound"
    style="@style/PrayerApp.Card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/_8sdp"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/_16sdp"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/icon_play"
            android:layout_width="@dimen/_40sdp"
            android:layout_height="@dimen/_40sdp"
            android:layout_marginEnd="@dimen/_16sdp"
            android:src="@drawable/ic_play"
            android:background="@drawable/icon_background"
            android:padding="@dimen/_8sdp"
            android:tint="@color/primary"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackgroundBorderless" />

        <TextView
            android:id="@+id/text_sound_name"
            style="@style/PrayerApp.Text.Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="الشيخ محمد رفعت"
            android:textColor="@color/text_primary" />

        <ImageView
            android:id="@+id/icon_selected"
            android:layout_width="@dimen/_24sdp"
            android:layout_height="@dimen/_24sdp"
            android:src="@drawable/ic_check"
            android:tint="@color/primary"
            android:visibility="gone" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
