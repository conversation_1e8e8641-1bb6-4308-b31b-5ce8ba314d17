<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <!-- Logo -->
        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:src="@drawable/ic_splash_logo"
            android:contentDescription="@string/app_logo"
            android:layout_marginTop="32dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Title -->
        <TextView
            android:id="@+id/tv_title"
            style="@style/PrayerApp.Text.Headline2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login_title"
            android:layout_marginTop="24dp"
            app:layout_constraintTop_toBottomOf="@id/iv_logo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Subtitle -->
        <TextView
            android:id="@+id/tv_subtitle"
            style="@style/PrayerApp.Text.Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/login_subtitle"
            android:textAlignment="center"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Activation Code Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_activation"
            style="@style/PrayerApp.Card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    style="@style/PrayerApp.Text.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/activation_code"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_activation_code"
                    style="@style/PrayerApp.EditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/enter_activation_code">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_activation_code"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="10"
                        android:textAlignment="center"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                </com.google.android.material.textfield.TextInputLayout>

                <Button
                    android:id="@+id/btn_activate"
                    style="@style/PrayerApp.Button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/activate"
                    android:layout_marginTop="20dp" />

                <ProgressBar
                    android:id="@+id/progress_bar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="16dp"
                    android:visibility="gone" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Contact Admin Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_contact"
            style="@style/PrayerApp.Card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            app:layout_constraintTop_toBottomOf="@id/card_activation"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    style="@style/PrayerApp.Text.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/contact_admin"
                    android:layout_marginBottom="16dp" />

                <TextView
                    style="@style/PrayerApp.Text.Body2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/contact_admin_subtitle"
                    android:layout_marginBottom="16dp" />

                <!-- Contact Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center">

                    <Button
                        android:id="@+id/btn_contact_whatsapp"
                        style="@style/PrayerApp.Button.Secondary"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/contact_whatsapp"
                        android:drawableStart="@drawable/ic_whatsapp"
                        android:drawablePadding="8dp"
                        android:layout_marginBottom="8dp" />

                    <Button
                        android:id="@+id/btn_contact_telegram"
                        style="@style/PrayerApp.Button.Outlined"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/contact_telegram"
                        android:drawableStart="@drawable/ic_telegram"
                        android:drawablePadding="8dp"
                        android:layout_marginBottom="8dp" />

                    <Button
                        android:id="@+id/btn_contact_email"
                        style="@style/PrayerApp.Button.Outlined"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/contact_email"
                        android:drawableStart="@drawable/ic_email"
                        android:drawablePadding="8dp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Device Info Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_device_info"
            style="@style/PrayerApp.Card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/card_contact"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="@style/PrayerApp.Text.Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/device_info"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_device_info"
                    style="@style/PrayerApp.Text.Caption"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/surface_variant"
                    android:padding="12dp"
                    android:fontFamily="monospace" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
