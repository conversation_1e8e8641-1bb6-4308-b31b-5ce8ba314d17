<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Header Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_16sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_20sdp">

                    <TextView
                        android:id="@+id/text_header_title"
                        style="@style/PrayerApp.Text.Headline2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/primary"
                        android:drawableStart="@drawable/ic_prayer_times"
                        android:drawablePadding="@dimen/_12sdp"
                        android:gravity="center_vertical" />

                    <TextView
                        android:id="@+id/text_header_subtitle"
                        style="@style/PrayerApp.Text.Body2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8sdp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Prayers List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_prayers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                android:clipToPadding="false" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
