<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- App Info Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp"
                    android:gravity="center">

                    <!-- App Icon -->
                    <ImageView
                        android:layout_width="@dimen/_80sdp"
                        android:layout_height="@dimen/_80sdp"
                        android:src="@mipmap/ic_launcher"
                        android:layout_marginBottom="@dimen/_16sdp" />

                    <!-- App Name -->
                    <TextView
                        style="@style/PrayerApp.Text.Headline2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/app_name"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="@dimen/_8sdp" />

                    <!-- App Version -->
                    <TextView
                        android:id="@+id/text_app_version"
                        style="@style/PrayerApp.Text.Body1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_16sdp"
                        android:text="الإصدار 1.0.0"
                        android:textColor="@color/text_secondary" />

                    <!-- App Description -->
                    <TextView
                        style="@style/PrayerApp.Text.Body2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="تطبيق شامل لأوقات الصلاة والأذان مع إمكانيات متقدمة لإدارة المسجد والتحكم عن بُعد"
                        android:textColor="@color/text_secondary"
                        android:gravity="center"
                        android:lineSpacingExtra="@dimen/_4sdp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Features Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Features Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_features"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Headline3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="المميزات الرئيسية"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Feature Items -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- Prayer Times Feature -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="@dimen/_12sdp">

                            <ImageView
                                android:layout_width="@dimen/_24sdp"
                                android:layout_height="@dimen/_24sdp"
                                android:layout_marginEnd="@dimen/_12sdp"
                                android:src="@drawable/ic_prayer_times"
                                android:tint="@color/primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="أوقات الصلاة الدقيقة والأذان"
                                android:textColor="@color/text_primary" />

                        </LinearLayout>

                        <!-- Gallery Feature -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="@dimen/_12sdp">

                            <ImageView
                                android:layout_width="@dimen/_24sdp"
                                android:layout_height="@dimen/_24sdp"
                                android:layout_marginEnd="@dimen/_12sdp"
                                android:src="@drawable/ic_gallery"
                                android:tint="@color/primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="معرض الصور والعروض التقديمية"
                                android:textColor="@color/text_primary" />

                        </LinearLayout>

                        <!-- Text Design Feature -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="@dimen/_12sdp">

                            <ImageView
                                android:layout_width="@dimen/_24sdp"
                                android:layout_height="@dimen/_24sdp"
                                android:layout_marginEnd="@dimen/_12sdp"
                                android:src="@drawable/ic_design"
                                android:tint="@color/primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تصميم النصوص والشعارات"
                                android:textColor="@color/text_primary" />

                        </LinearLayout>

                        <!-- Remote Control Feature -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <ImageView
                                android:layout_width="@dimen/_24sdp"
                                android:layout_height="@dimen/_24sdp"
                                android:layout_marginEnd="@dimen/_12sdp"
                                android:src="@drawable/ic_remote"
                                android:tint="@color/primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="التحكم عن بُعد والإدارة"
                                android:textColor="@color/text_primary" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Developer Info Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Developer Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_developer"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Headline3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="معلومات المطور"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Developer Info -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="@drawable/card_background"
                        android:padding="@dimen/_12sdp">

                        <TextView
                            style="@style/PrayerApp.Text.Body1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تم تطوير هذا التطبيق بواسطة:"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="@dimen/_8sdp" />

                        <TextView
                            style="@style/PrayerApp.Text.Headline4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="فريق الربع للتطوير"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="@dimen/_4sdp" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="متخصصون في تطوير التطبيقات الإسلامية"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Contact Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Contact Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_contact"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Headline3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تواصل معنا"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Contact Actions -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btn_email"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="@dimen/_8sdp"
                            android:background="@drawable/button_primary_background"
                            android:text="البريد الإلكتروني"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_12sdp"
                            android:drawableStart="@drawable/ic_email"
                            android:drawablePadding="@dimen/_8sdp"
                            android:padding="@dimen/_12sdp" />

                        <Button
                            android:id="@+id/btn_website"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="@dimen/_8sdp"
                            android:background="@drawable/button_secondary_background"
                            android:text="الموقع الإلكتروني"
                            android:textColor="@color/primary"
                            android:textSize="@dimen/_12sdp"
                            android:drawableStart="@drawable/ic_web"
                            android:drawablePadding="@dimen/_8sdp"
                            android:padding="@dimen/_12sdp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
