<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- تنبيهات الأذان -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_azan_notifications"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp"
                android:foreground="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_48sdp"
                            android:layout_height="@dimen/_48sdp"
                            android:layout_marginEnd="@dimen/_16sdp"
                            android:src="@drawable/ic_volume"
                            android:background="@drawable/icon_background"
                            android:padding="@dimen/_12sdp"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Headline4"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تنبيهات الأذان"
                                android:textColor="@color/text_primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="اختيار نغمة الأذان والمؤذن"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                        <Switch
                            android:id="@+id/switch_azan_enabled"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                    <!-- Azan Sound Selection -->
                    <LinearLayout
                        android:id="@+id/layout_azan_sound"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="@dimen/_8sdp"
                        android:background="@drawable/selector_item_background"
                        android:clickable="true"
                        android:focusable="true">

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_music"
                            android:tint="@color/text_secondary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Body1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="نغمة الأذان"
                                android:textColor="@color/text_primary" />

                            <TextView
                                android:id="@+id/text_selected_azan"
                                style="@style/PrayerApp.Text.Caption"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="الشيخ محمد رفعت"
                                android:textColor="@color/text_secondary" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="@dimen/_20sdp"
                            android:layout_height="@dimen/_20sdp"
                            android:src="@drawable/ic_arrow_forward"
                            android:tint="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- تنبيهات الإقامة -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_iqama_notifications"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:foreground="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_48sdp"
                            android:layout_height="@dimen/_48sdp"
                            android:layout_marginEnd="@dimen/_16sdp"
                            android:src="@drawable/ic_iqama"
                            android:background="@drawable/icon_background"
                            android:padding="@dimen/_12sdp"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Headline4"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تنبيهات الإقامة"
                                android:textColor="@color/text_primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="اختيار نغمة الإقامة والتنبيهات"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                        <Switch
                            android:id="@+id/switch_iqama_enabled"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                    <!-- Iqama Sound Selection -->
                    <LinearLayout
                        android:id="@+id/layout_iqama_sound"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="@dimen/_8sdp"
                        android:background="@drawable/selector_item_background"
                        android:clickable="true"
                        android:focusable="true">

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_music"
                            android:tint="@color/text_secondary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Body1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="نغمة الإقامة"
                                android:textColor="@color/text_primary" />

                            <TextView
                                android:id="@+id/text_selected_iqama"
                                style="@style/PrayerApp.Text.Caption"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="النغمة الافتراضية"
                                android:textColor="@color/text_secondary" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="@dimen/_20sdp"
                            android:layout_height="@dimen/_20sdp"
                            android:src="@drawable/ic_arrow_forward"
                            android:tint="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
