<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="إعدادات أوقات الصلاة"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Header -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_16sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_20sdp">

                    <TextView
                        style="@style/PrayerApp.Text.Headline2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="إعدادات أوقات الصلاة"
                        android:textColor="@color/primary"
                        android:drawableStart="@drawable/ic_prayer_times"
                        android:drawablePadding="@dimen/_12sdp"
                        android:gravity="center_vertical" />

                    <TextView
                        style="@style/PrayerApp.Text.Body2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="قم بإعداد أوقات الصلاة والإقامة لكل صلاة"
                        android:layout_marginTop="@dimen/_8sdp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Ramadan Settings Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_ramadan_settings"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Ramadan Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_ramadan"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="إعدادات رمضان"
                                android:textColor="@color/text_primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="إعدادات خاصة بشهر رمضان المبارك"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:src="@drawable/ic_arrow_forward"
                            android:tint="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Normal Days Settings Card -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_normal_settings"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Normal Days Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_calendar"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="إعدادات الأيام العادية"
                                android:textColor="@color/text_primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="إعدادات الأيام العادية خارج رمضان"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:src="@drawable/ic_arrow_forward"
                            android:tint="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
