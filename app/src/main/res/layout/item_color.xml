<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_color"
    android:layout_width="@dimen/_40sdp"
    android:layout_height="@dimen/_40sdp"
    app:cardCornerRadius="@dimen/_20sdp"
    app:cardElevation="@dimen/_4sdp"
    android:layout_margin="@dimen/_4sdp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <View
            android:id="@+id/view_color"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <View
            android:id="@+id/view_selection"
            android:layout_width="@dimen/_8sdp"
            android:layout_height="@dimen/_8sdp"
            android:layout_gravity="center"
            android:background="@drawable/selection_indicator"
            android:visibility="gone" />

    </FrameLayout>

</androidx.cardview.widget.CardView>
