<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- القوالب -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_templates"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp"
                android:foreground="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/_16sdp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="@dimen/_48sdp"
                        android:layout_height="@dimen/_48sdp"
                        android:layout_marginEnd="@dimen/_16sdp"
                        android:src="@drawable/ic_template"
                        android:background="@drawable/icon_background"
                        android:padding="@dimen/_12sdp"
                        android:tint="@color/primary" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/PrayerApp.Text.Headline4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="القوالب"
                            android:textColor="@color/text_primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="قوالب تصميم الواجهة الرئيسية"
                            android:textColor="@color/text_secondary"
                            android:layout_marginTop="@dimen/_4sdp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="@dimen/_24sdp"
                        android:layout_height="@dimen/_24sdp"
                        android:src="@drawable/ic_arrow_forward"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- قوالب الأذكار -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_azkar_templates"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp"
                android:foreground="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/_16sdp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="@dimen/_48sdp"
                        android:layout_height="@dimen/_48sdp"
                        android:layout_marginEnd="@dimen/_16sdp"
                        android:src="@drawable/ic_azkar"
                        android:background="@drawable/icon_background"
                        android:padding="@dimen/_12sdp"
                        android:tint="@color/primary" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/PrayerApp.Text.Headline4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="قوالب الأذكار"
                            android:textColor="@color/text_primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تصميم وتخطيط عرض الأذكار"
                            android:textColor="@color/text_secondary"
                            android:layout_marginTop="@dimen/_4sdp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="@dimen/_24sdp"
                        android:layout_height="@dimen/_24sdp"
                        android:src="@drawable/ic_arrow_forward"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- الخلفيات -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_backgrounds"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp"
                android:foreground="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/_16sdp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="@dimen/_48sdp"
                        android:layout_height="@dimen/_48sdp"
                        android:layout_marginEnd="@dimen/_16sdp"
                        android:src="@drawable/ic_wallpaper"
                        android:background="@drawable/icon_background"
                        android:padding="@dimen/_12sdp"
                        android:tint="@color/primary" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/PrayerApp.Text.Headline4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="الخلفيات"
                            android:textColor="@color/text_primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="خلفيات التطبيق والشاشات"
                            android:textColor="@color/text_secondary"
                            android:layout_marginTop="@dimen/_4sdp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="@dimen/_24sdp"
                        android:layout_height="@dimen/_24sdp"
                        android:src="@drawable/ic_arrow_forward"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- قوالب الإقامة -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_iqama_templates"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:foreground="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/_16sdp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="@dimen/_48sdp"
                        android:layout_height="@dimen/_48sdp"
                        android:layout_marginEnd="@dimen/_16sdp"
                        android:src="@drawable/ic_iqama"
                        android:background="@drawable/icon_background"
                        android:padding="@dimen/_12sdp"
                        android:tint="@color/primary" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/PrayerApp.Text.Headline4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="قوالب الإقامة"
                            android:textColor="@color/text_primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تصميم شاشة الإقامة والعرض"
                            android:textColor="@color/text_secondary"
                            android:layout_marginTop="@dimen/_4sdp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="@dimen/_24sdp"
                        android:layout_height="@dimen/_24sdp"
                        android:src="@drawable/ic_arrow_forward"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
