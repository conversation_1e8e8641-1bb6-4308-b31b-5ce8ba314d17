<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/PrayerApp.Card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/_12sdp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/_16sdp">

        <!-- Gallery Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="@dimen/_12sdp">

            <ImageView
                android:id="@+id/image_gallery_preview"
                android:layout_width="@dimen/_48sdp"
                android:layout_height="@dimen/_48sdp"
                android:layout_marginEnd="@dimen/_12sdp"
                android:src="@drawable/ic_gallery"
                android:scaleType="centerCrop"
                android:background="@drawable/card_background" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/text_gallery_name"
                    style="@style/PrayerApp.Text.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/text_gallery_info"
                    style="@style/PrayerApp.Text.Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="@dimen/_4sdp" />

            </LinearLayout>

            <Switch
                android:id="@+id/switch_gallery_enabled"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:buttonTint="@color/primary"
                app:thumbTint="@color/white"
                app:trackTint="@color/primary" />

        </LinearLayout>

        <!-- Gallery Actions -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <Button
                android:id="@+id/btn_edit_gallery"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="@dimen/_8sdp"
                android:background="@drawable/button_primary_background"
                android:text="@string/edit"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp"
                android:drawableStart="@drawable/ic_settings"
                android:drawablePadding="@dimen/_8sdp"
                android:padding="@dimen/_8sdp" />

            <Button
                android:id="@+id/btn_delete_gallery"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/_8sdp"
                android:background="@drawable/button_danger_background"
                android:text="@string/delete"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp"
                android:drawableStart="@android:drawable/ic_menu_delete"
                android:drawablePadding="@dimen/_8sdp"
                android:padding="@dimen/_8sdp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
