<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/PrayerApp.Card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/_8sdp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/_12sdp">

        <!-- Backup Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="@dimen/_8sdp">

            <ImageView
                android:layout_width="@dimen/_24sdp"
                android:layout_height="@dimen/_24sdp"
                android:layout_marginEnd="@dimen/_8sdp"
                android:src="@drawable/ic_backup"
                android:tint="@color/primary" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/text_backup_name"
                    style="@style/PrayerApp.Text.Headline4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="نسخة احتياطية 1"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/text_backup_date"
                    style="@style/PrayerApp.Text.Caption"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2024/01/15 - 10:30 ص"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="@dimen/_2sdp" />

            </LinearLayout>

            <TextView
                android:id="@+id/text_backup_size"
                style="@style/PrayerApp.Text.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2.5 MB"
                android:textColor="@color/text_secondary"
                android:background="@drawable/badge_background"
                android:padding="@dimen/_4sdp" />

        </LinearLayout>

        <!-- Backup Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="@dimen/_8sdp">

            <TextView
                android:id="@+id/text_backup_items"
                style="@style/PrayerApp.Text.Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="12 فئة إعدادات"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/text_backup_version"
                style="@style/PrayerApp.Text.Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="v1.0"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_restore_backup"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="@dimen/_4sdp"
                android:background="@drawable/button_primary_background"
                android:text="استرجاع"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp"
                android:drawableStart="@drawable/ic_restore"
                android:drawablePadding="@dimen/_4sdp"
                android:padding="@dimen/_6sdp" />

            <Button
                android:id="@+id/btn_preview_backup"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/_4sdp"
                android:layout_marginEnd="@dimen/_4sdp"
                android:background="@drawable/button_secondary_background"
                android:text="معاينة"
                android:textColor="@color/primary"
                android:textSize="@dimen/_12sdp"
                android:drawableStart="@drawable/ic_preview"
                android:drawablePadding="@dimen/_4sdp"
                android:padding="@dimen/_6sdp" />

            <Button
                android:id="@+id/btn_delete_backup"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/_4sdp"
                android:background="@drawable/button_danger_background"
                android:text="حذف"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp"
                android:drawableStart="@android:drawable/ic_menu_delete"
                android:drawablePadding="@dimen/_4sdp"
                android:padding="@dimen/_6sdp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
