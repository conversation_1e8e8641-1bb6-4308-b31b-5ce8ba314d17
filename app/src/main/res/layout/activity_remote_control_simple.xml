<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Header Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_remote_control"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تطبيقات التحكم عن بُعد"
                                android:textColor="@color/text_primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="حمّل واستخدم التطبيقات للتحكم في المسجد"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- App 1: تحكم الصلاة -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- App Info -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_48sdp"
                            android:layout_height="@dimen/_48sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_prayer"
                            android:background="@drawable/card_background"
                            android:padding="@dimen/_8sdp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تحكم الصلاة"
                                android:textColor="@color/text_primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="التحكم في أوقات الصلاة والأذان"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Download Progress -->
                    <LinearLayout
                        android:id="@+id/layout_progress_app1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="@dimen/_8sdp"
                        android:visibility="gone">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="@dimen/_4sdp">

                            <TextView
                                android:id="@+id/text_progress_app1"
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="جاري التحميل..."
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/text_progress_percent_app1"
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0%"
                                android:textColor="@color/primary" />

                        </LinearLayout>

                        <ProgressBar
                            android:id="@+id/progress_app1"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/_4sdp"
                            android:max="100"
                            android:progress="0"
                            android:progressTint="@color/primary"
                            android:progressBackgroundTint="@color/divider" />

                    </LinearLayout>

                    <!-- App Actions -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btn_download_app1"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="@dimen/_4sdp"
                            android:background="@drawable/button_primary_background"
                            android:text="تحميل"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_12sdp"
                            android:padding="@dimen/_8sdp" />

                        <Button
                            android:id="@+id/btn_install_app1"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="@dimen/_4sdp"
                            android:layout_marginEnd="@dimen/_4sdp"
                            android:background="@drawable/button_secondary_background"
                            android:text="تثبيت"
                            android:textColor="@color/primary"
                            android:textSize="@dimen/_12sdp"
                            android:padding="@dimen/_8sdp" />

                        <Button
                            android:id="@+id/btn_open_app1"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="@dimen/_4sdp"
                            android:background="@drawable/button_secondary_background"
                            android:text="فتح"
                            android:textColor="@color/primary"
                            android:textSize="@dimen/_12sdp"
                            android:padding="@dimen/_8sdp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- App 2: تحكم المعرض -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- App Info -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_48sdp"
                            android:layout_height="@dimen/_48sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_gallery"
                            android:background="@drawable/card_background"
                            android:padding="@dimen/_8sdp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تحكم المعرض"
                                android:textColor="@color/text_primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="إدارة معرض الصور والعروض"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Download Progress -->
                    <LinearLayout
                        android:id="@+id/layout_progress_app2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="@dimen/_8sdp"
                        android:visibility="gone">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="@dimen/_4sdp">

                            <TextView
                                android:id="@+id/text_progress_app2"
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="جاري التحميل..."
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/text_progress_percent_app2"
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0%"
                                android:textColor="@color/primary" />

                        </LinearLayout>

                        <ProgressBar
                            android:id="@+id/progress_app2"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/_4sdp"
                            android:max="100"
                            android:progress="0"
                            android:progressTint="@color/primary"
                            android:progressBackgroundTint="@color/divider" />

                    </LinearLayout>

                    <!-- App Actions -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btn_download_app2"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="@dimen/_4sdp"
                            android:background="@drawable/button_primary_background"
                            android:text="تحميل"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_12sdp"
                            android:padding="@dimen/_8sdp" />

                        <Button
                            android:id="@+id/btn_install_app2"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="@dimen/_4sdp"
                            android:layout_marginEnd="@dimen/_4sdp"
                            android:background="@drawable/button_secondary_background"
                            android:text="تثبيت"
                            android:textColor="@color/primary"
                            android:textSize="@dimen/_12sdp"
                            android:padding="@dimen/_8sdp" />

                        <Button
                            android:id="@+id/btn_open_app2"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="@dimen/_4sdp"
                            android:background="@drawable/button_secondary_background"
                            android:text="فتح"
                            android:textColor="@color/primary"
                            android:textSize="@dimen/_12sdp"
                            android:padding="@dimen/_8sdp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- App 3: تحكم النصوص -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- App Info -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_48sdp"
                            android:layout_height="@dimen/_48sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_typeface"
                            android:background="@drawable/card_background"
                            android:padding="@dimen/_8sdp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تحكم النصوص"
                                android:textColor="@color/text_primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="إدارة النصوص والتصاميم"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Download Progress -->
                    <LinearLayout
                        android:id="@+id/layout_progress_app3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="@dimen/_8sdp"
                        android:visibility="gone">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="@dimen/_4sdp">

                            <TextView
                                android:id="@+id/text_progress_app3"
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="جاري التحميل..."
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/text_progress_percent_app3"
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0%"
                                android:textColor="@color/primary" />

                        </LinearLayout>

                        <ProgressBar
                            android:id="@+id/progress_app3"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/_4sdp"
                            android:max="100"
                            android:progress="0"
                            android:progressTint="@color/primary"
                            android:progressBackgroundTint="@color/divider" />

                    </LinearLayout>

                    <!-- App Actions -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btn_download_app3"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="@dimen/_4sdp"
                            android:background="@drawable/button_primary_background"
                            android:text="تحميل"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_12sdp"
                            android:padding="@dimen/_8sdp" />

                        <Button
                            android:id="@+id/btn_install_app3"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="@dimen/_4sdp"
                            android:layout_marginEnd="@dimen/_4sdp"
                            android:background="@drawable/button_secondary_background"
                            android:text="تثبيت"
                            android:textColor="@color/primary"
                            android:textSize="@dimen/_12sdp"
                            android:padding="@dimen/_8sdp" />

                        <Button
                            android:id="@+id/btn_open_app3"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="@dimen/_4sdp"
                            android:background="@drawable/button_secondary_background"
                            android:text="فتح"
                            android:textColor="@color/primary"
                            android:textSize="@dimen/_12sdp"
                            android:padding="@dimen/_8sdp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
