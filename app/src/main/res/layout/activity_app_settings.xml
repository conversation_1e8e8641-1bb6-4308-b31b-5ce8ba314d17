<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- الإعدادات الرئيسية -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_main_settings"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp"
                android:foreground="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/_16sdp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="@dimen/_48sdp"
                        android:layout_height="@dimen/_48sdp"
                        android:layout_marginEnd="@dimen/_16sdp"
                        android:src="@drawable/ic_settings"
                        android:background="@drawable/icon_background"
                        android:padding="@dimen/_12sdp"
                        android:tint="@color/primary" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/PrayerApp.Text.Headline4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="الإعدادات الرئيسية"
                            android:textColor="@color/text_primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="إعدادات النظام والتطبيق الأساسية"
                            android:textColor="@color/text_secondary"
                            android:layout_marginTop="@dimen/_4sdp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="@dimen/_24sdp"
                        android:layout_height="@dimen/_24sdp"
                        android:src="@drawable/ic_arrow_forward"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- خيارات التصميم -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_design_options"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp"
                android:foreground="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/_16sdp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="@dimen/_48sdp"
                        android:layout_height="@dimen/_48sdp"
                        android:layout_marginEnd="@dimen/_16sdp"
                        android:src="@drawable/ic_design"
                        android:background="@drawable/icon_background"
                        android:padding="@dimen/_12sdp"
                        android:tint="@color/primary" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/PrayerApp.Text.Headline4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="خيارات التصميم"
                            android:textColor="@color/text_primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="القوالب والخلفيات وتصميم الواجهة"
                            android:textColor="@color/text_secondary"
                            android:layout_marginTop="@dimen/_4sdp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="@dimen/_24sdp"
                        android:layout_height="@dimen/_24sdp"
                        android:src="@drawable/ic_arrow_forward"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- التنبيهات -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_notifications"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp"
                android:foreground="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/_16sdp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="@dimen/_48sdp"
                        android:layout_height="@dimen/_48sdp"
                        android:layout_marginEnd="@dimen/_16sdp"
                        android:src="@drawable/ic_notifications"
                        android:background="@drawable/icon_background"
                        android:padding="@dimen/_12sdp"
                        android:tint="@color/primary" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/PrayerApp.Text.Headline4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="التنبيهات"
                            android:textColor="@color/text_primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تنبيهات الأذان والإقامة والنغمات"
                            android:textColor="@color/text_secondary"
                            android:layout_marginTop="@dimen/_4sdp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="@dimen/_24sdp"
                        android:layout_height="@dimen/_24sdp"
                        android:src="@drawable/ic_arrow_forward"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- تحديد الموقع -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_location"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp"
                android:foreground="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/_16sdp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="@dimen/_48sdp"
                        android:layout_height="@dimen/_48sdp"
                        android:layout_marginEnd="@dimen/_16sdp"
                        android:src="@drawable/ic_location"
                        android:background="@drawable/icon_background"
                        android:padding="@dimen/_12sdp"
                        android:tint="@color/primary" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/PrayerApp.Text.Headline4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تحديد الموقع"
                            android:textColor="@color/text_primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="اختيار البلد والمدينة لأوقات الصلاة"
                            android:textColor="@color/text_secondary"
                            android:layout_marginTop="@dimen/_4sdp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="@dimen/_24sdp"
                        android:layout_height="@dimen/_24sdp"
                        android:src="@drawable/ic_arrow_forward"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- إعدادات الجهاز -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_device_settings"
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:foreground="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/_16sdp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="@dimen/_48sdp"
                        android:layout_height="@dimen/_48sdp"
                        android:layout_marginEnd="@dimen/_16sdp"
                        android:src="@drawable/ic_device"
                        android:background="@drawable/icon_background"
                        android:padding="@dimen/_12sdp"
                        android:tint="@color/primary" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/PrayerApp.Text.Headline4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="إعدادات الجهاز"
                            android:textColor="@color/text_primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="إعدادات الشبكة والتاريخ والوقت"
                            android:textColor="@color/text_secondary"
                            android:layout_marginTop="@dimen/_4sdp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="@dimen/_24sdp"
                        android:layout_height="@dimen/_24sdp"
                        android:src="@drawable/ic_arrow_forward"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
