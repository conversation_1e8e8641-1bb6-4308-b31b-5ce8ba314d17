<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_category"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_1sdp"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="@dimen/_20sdp"
    app:cardElevation="@dimen/_10sdp"
    app:cardBackgroundColor="@color/card_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="140dp"
        android:padding="@dimen/_16sdp">

        <!-- Category Icon -->
        <ImageView
            android:id="@+id/iv_category_icon"
            android:layout_width="@dimen/_56sdp"
            android:layout_height="@dimen/_56sdp"
            android:layout_marginTop="@dimen/_12sdp"
            android:scaleType="centerInside"
            android:background="@drawable/icon_background"
            android:padding="@dimen/_14sdp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:src="@drawable/ic_settings" />

        <!-- Category Title -->
        <TextView
            android:id="@+id/tv_category_title"
            style="@style/PrayerApp.Text.Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:layout_marginTop="@dimen/_12sdp"
            android:layout_marginBottom="@dimen/_12sdp"
            android:textAlignment="center"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:maxLines="2"
            android:ellipsize="end"
            android:gravity="center"
            android:textSize="14sp"
            app:layout_constraintTop_toBottomOf="@id/iv_category_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="إعدادات التطبيق" />

        <!-- Item Count Badge -->
        <TextView
            android:id="@+id/tv_category_count"
            android:layout_width="@dimen/_22sdp"
            android:layout_height="@dimen/_22sdp"
            android:background="@drawable/badge_background"
            android:text="5"
            android:textColor="@color/white"
            android:textSize="@dimen/_11ssp"
            android:textStyle="bold"
            android:gravity="center"
            android:visibility="gone"
            android:layout_marginTop="@dimen/_6sdp"
            android:layout_marginEnd="@dimen/_6sdp"
            app:layout_constraintTop_toTopOf="@id/iv_category_icon"
            app:layout_constraintEnd_toEndOf="@id/iv_category_icon"
            tools:visibility="visible" />

        <!-- Ripple Effect Overlay -->
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="false"
            android:focusable="false" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
