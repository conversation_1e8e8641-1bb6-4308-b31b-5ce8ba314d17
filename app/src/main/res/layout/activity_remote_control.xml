<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Remote Control Apps Header -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_remote"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تطبيقات التحكم عن بُعد"
                                android:textColor="@color/text_primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="حمّل واستخدم التطبيقات للتحكم في المسجد"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- App 1: Prayer Remote Control -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- App Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <!-- App Icon -->
                        <ImageView
                            android:layout_width="@dimen/_48sdp"
                            android:layout_height="@dimen/_48sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_prayer_remote"
                            android:background="@drawable/card_background"
                            android:padding="@dimen/_8sdp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تحكم الصلاة"
                                android:textColor="@color/text_primary" />

                            <TextView
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="التحكم في أوقات الصلاة والأذان"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                        <!-- Status Badge -->
                        <TextView
                            android:id="@+id/badge_app1_status"
                            style="@style/PrayerApp.Text.Caption"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="غير مثبت"
                            android:textColor="@color/white"
                            android:background="@drawable/badge_background"
                            android:padding="@dimen/_6sdp" />

                    </LinearLayout>

                    <!-- App Description -->
                    <TextView
                        style="@style/PrayerApp.Text.Body2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="تطبيق متخصص للتحكم في أوقات الصلاة والأذان عن بُعد. يمكنك تشغيل وإيقاف الأذان، وضبط الأوقات، وإدارة الإعدادات من هاتفك."
                        android:textColor="@color/text_secondary"
                        android:lineSpacingExtra="@dimen/_4sdp"
                        android:layout_marginBottom="@dimen/_12sdp" />

                    <!-- App Actions -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btn_download_app1"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="@dimen/_4sdp"
                            android:background="@drawable/button_primary_background"
                            android:text="تحميل"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_12sdp"
                            android:drawableStart="@drawable/ic_download"
                            android:drawablePadding="@dimen/_8sdp"
                            android:padding="@dimen/_8sdp" />

                        <Button
                            android:id="@+id/btn_install_app1"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="@dimen/_4sdp"
                            android:layout_marginEnd="@dimen/_4sdp"
                            android:background="@drawable/button_secondary_background"
                            android:text="تثبيت"
                            android:textColor="@color/primary"
                            android:textSize="@dimen/_12sdp"
                            android:drawableStart="@drawable/ic_install"
                            android:drawablePadding="@dimen/_8sdp"
                            android:padding="@dimen/_8sdp"
                            android:enabled="false"
                            android:alpha="0.6" />

                        <Button
                            android:id="@+id/btn_open_app1"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="@dimen/_4sdp"
                            android:background="@drawable/button_secondary_background"
                            android:text="فتح"
                            android:textColor="@color/primary"
                            android:textSize="@dimen/_12sdp"
                            android:drawableStart="@drawable/ic_open"
                            android:drawablePadding="@dimen/_8sdp"
                            android:padding="@dimen/_8sdp"
                            android:enabled="false"
                            android:alpha="0.6" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Volume Control Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Volume Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_volume"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Headline3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="التحكم في الصوت"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Volume Controls -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:src="@drawable/ic_volume_down"
                            android:tint="@color/text_secondary" />

                        <SeekBar
                            android:id="@+id/seekbar_volume"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="@dimen/_12sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:max="100"
                            android:progress="50"
                            android:progressTint="@color/primary"
                            android:thumbTint="@color/primary" />

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:src="@drawable/ic_volume_up"
                            android:tint="@color/text_secondary" />

                        <TextView
                            android:id="@+id/text_volume_level"
                            style="@style/PrayerApp.Text.Body1"
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="wrap_content"
                            android:text="50%"
                            android:textColor="@color/text_primary"
                            android:gravity="center"
                            android:layout_marginStart="@dimen/_8sdp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Settings Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Settings Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_settings"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Headline3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="إعدادات التحكم عن بُعد"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Settings Options -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- Auto Connect -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="@dimen/_12sdp">

                            <TextView
                                style="@style/PrayerApp.Text.Body1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="الاتصال التلقائي"
                                android:textColor="@color/text_primary" />

                            <Switch
                                android:id="@+id/switch_auto_connect"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:checked="true"
                                app:buttonTint="@color/primary"
                                app:thumbTint="@color/white"
                                app:trackTint="@color/primary" />

                        </LinearLayout>

                        <!-- Connection Settings Button -->
                        <Button
                            android:id="@+id/btn_connection_settings"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/_48sdp"
                            android:background="@drawable/button_primary_background"
                            android:text="إعدادات الاتصال"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_14sdp"
                            android:drawableStart="@drawable/ic_settings"
                            android:drawablePadding="@dimen/_8sdp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
