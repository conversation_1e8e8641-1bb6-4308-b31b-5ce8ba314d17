<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="@string/add_event"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Main Form Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_20sdp">

                    <!-- Event Title Input -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/PrayerApp.EditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_16sdp"
                        android:hint="@string/event_title">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_event_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1"
                            android:textColor="@color/text_primary"
                            android:textSize="@dimen/_16sdp" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Event Message Input -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/PrayerApp.EditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_20sdp"
                        android:hint="@string/event_message">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_event_message"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:lines="4"
                            android:maxLines="6"
                            android:gravity="start|top"
                            android:textColor="@color/text_primary"
                            android:textSize="@dimen/_16sdp" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Template Messages Section -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_8sdp"
                        android:text="@string/template_messages"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/_16sdp"
                        android:textStyle="bold" />

                    <Spinner
                        android:id="@+id/spinner_template_messages"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_20sdp"
                        android:background="@drawable/card_background"
                        android:padding="@dimen/_12sdp"
                        android:spinnerMode="dropdown" />

                    <!-- Prayer Selection -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_8sdp"
                        android:text="اختيار الصلاة"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/_16sdp"
                        android:textStyle="bold" />

                    <Spinner
                        android:id="@+id/spinner_prayer_selection"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_20sdp"
                        android:background="@drawable/card_background"
                        android:padding="@dimen/_12sdp"
                        android:spinnerMode="dropdown" />

                    <!-- Date Selection -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_8sdp"
                        android:text="فترة العرض (اختياري)"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/_16sdp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_20sdp"
                        android:orientation="horizontal"
                        android:weightSum="2">

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/PrayerApp.EditText"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/_8sdp"
                            android:layout_weight="1"
                            android:hint="@string/start_date">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_start_date"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:drawableEnd="@drawable/ic_calendar"
                                android:focusable="false"
                                android:focusableInTouchMode="false"
                                android:inputType="none"
                                android:textColor="@color/text_primary"
                                android:textSize="@dimen/_14sdp" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/PrayerApp.EditText"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_8sdp"
                            android:layout_weight="1"
                            android:hint="@string/end_date">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_end_date"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:drawableEnd="@drawable/ic_calendar"
                                android:focusable="false"
                                android:focusableInTouchMode="false"
                                android:inputType="none"
                                android:textColor="@color/text_primary"
                                android:textSize="@dimen/_14sdp" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <!-- Save Button -->
                    <Button
                        android:id="@+id/btn_save"
                        style="@style/PrayerApp.Button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/button_primary_background"
                        android:text="@string/save"
                        android:textColor="@color/text_on_primary"
                        android:textSize="@dimen/_16sdp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
