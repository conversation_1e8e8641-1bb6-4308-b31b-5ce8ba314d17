<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="التاريخ الميلادي"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tv_hijri_date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="التاريخ الهجري"
            android:textSize="16sp"
            android:textStyle="normal"
            android:gravity="center"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="2">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_fajr"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="الفجر: 05:30"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_sunrise"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="الشروق: 06:45"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_dhuhr"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="الظهر: 12:15"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_asr"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="العصر: 15:30"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_maghrib"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="المغرب: 18:00"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_isha"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="العشاء: 19:30"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
