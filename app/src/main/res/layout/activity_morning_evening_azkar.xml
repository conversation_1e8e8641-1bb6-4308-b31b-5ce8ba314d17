<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="@string/morning_evening_azkar"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Header Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_16sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_20sdp">

                    <TextView
                        style="@style/PrayerApp.Text.Headline2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/morning_evening_azkar"
                        android:textColor="@color/primary"
                        android:drawableStart="@drawable/ic_prayer_beads"
                        android:drawablePadding="@dimen/_12sdp"
                        android:gravity="center_vertical" />

                    <TextView
                        style="@style/PrayerApp.Text.Body2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/morning_evening_azkar_subtitle"
                        android:layout_marginTop="@dimen/_8sdp" />

                    <TextView
                        style="@style/PrayerApp.Text.Body2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/display_duration_minutes"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:textStyle="bold"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Morning Azkar Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Morning Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_sun_moon"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/citationForMorning_TextView_AthkarFragment"
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/citation_for_morning"
                                android:textColor="@color/text_primary" />

                            <TextView
                                android:id="@+id/citationForMorningDescribe_TextView_AthkarFragment"
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/after_fajr_until_sunrise"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                        <Switch
                            android:id="@+id/citationForMorning_CheckBox_AthkarFragment"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            app:buttonTint="@color/primary"
                            app:thumbTint="@color/white"
                            app:trackTint="@color/primary" />

                    </LinearLayout>

                    <!-- Time Controls -->
                    <LinearLayout
                        android:id="@+id/morning_time_controls"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="@drawable/card_background"
                        android:padding="@dimen/_12sdp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/athkar_time_after_or_before_sunrise"
                                android:textColor="@color/text_secondary"
                                android:layout_marginEnd="@dimen/_16sdp" />

                            <View
                                android:layout_width="0dp"
                                android:layout_height="1dp"
                                android:layout_weight="1" />

                            <Button
                                android:id="@+id/b1"
                                style="@style/Widget.AppCompat.Button.Borderless"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/button_primary_background"
                                android:text="@string/edit"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_12sdp"
                                android:padding="@dimen/_8sdp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/citationForMorningTime_TextView_AthkarFragment"
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/athkar_time_after_or_before_sunrise"
                            android:textColor="@color/text_primary"
                            android:gravity="center"
                            android:layout_marginTop="@dimen/_8sdp" />

                        <SeekBar
                            android:id="@+id/citationForMorningTime_SeekBar_AthkarFragment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_8sdp"
                            android:progress="50"
                            android:progressTint="@color/primary"
                            android:visibility="gone" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/settingItem_RecyclerView_PrayerTimesSettingFragment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_8sdp"
                            android:nestedScrollingEnabled="false"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Evening Azkar Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Evening Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:src="@drawable/ic_sun_moon"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/citationForEvening_TextView_AthkarFragment"
                                style="@style/PrayerApp.Text.Headline3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/citation_for_evening"
                                android:textColor="@color/text_primary" />

                            <TextView
                                android:id="@+id/citationForEveningDescribe_TextView_AthkarFragment"
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/after_asr_until_sunset"
                                android:textColor="@color/text_secondary"
                                android:layout_marginTop="@dimen/_4sdp" />

                        </LinearLayout>

                        <Switch
                            android:id="@+id/citationForEvening_CheckBox_AthkarFragment"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            app:buttonTint="@color/primary"
                            app:thumbTint="@color/white"
                            app:trackTint="@color/primary" />

                    </LinearLayout>

                    <!-- Time Controls -->
                    <LinearLayout
                        android:id="@+id/evening_time_controls"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="@drawable/card_background"
                        android:padding="@dimen/_12sdp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <TextView
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/athkar_time_after_or_before_sunset"
                                android:textColor="@color/text_secondary"
                                android:layout_marginEnd="@dimen/_16sdp" />

                            <View
                                android:layout_width="0dp"
                                android:layout_height="1dp"
                                android:layout_weight="1" />

                            <Button
                                android:id="@+id/b2"
                                style="@style/Widget.AppCompat.Button.Borderless"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/button_primary_background"
                                android:text="@string/edit"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_12sdp"
                                android:padding="@dimen/_8sdp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/citationForEveningTime_TextView_AthkarFragment"
                            style="@style/PrayerApp.Text.Body2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/athkar_time_after_or_before_sunset"
                            android:textColor="@color/text_primary"
                            android:gravity="center"
                            android:layout_marginTop="@dimen/_8sdp" />

                        <SeekBar
                            android:id="@+id/citationForEveningTime_SeekBar_AthkarFragment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_8sdp"
                            android:progress="50"
                            android:progressTint="@color/primary"
                            android:visibility="gone" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/settingItem_RecyclerView_PrayerTimesSettingFragmentE"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_8sdp"
                            android:nestedScrollingEnabled="false"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
