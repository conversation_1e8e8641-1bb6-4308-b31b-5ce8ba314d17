<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/card_theme_main"
        style="@style/PrayerApp.Card"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="@dimen/_16sdp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        app:cardElevation="@dimen/_8sdp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_themes"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingTop="@dimen/_4sdp"
            android:paddingBottom="@dimen/_4sdp" />

    </androidx.cardview.widget.CardView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
