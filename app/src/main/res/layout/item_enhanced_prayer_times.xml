<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_prayer_time"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/card_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with prayer name and badges -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_prayer_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="الفجر"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:fontFamily="@font/alkalami" />

            <TextView
                android:id="@+id/tv_next_prayer_badge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="الصلاة القادمة"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                android:background="@drawable/badge_background"
                android:padding="4dp"
                android:layout_marginStart="8dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_current_prayer_badge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="الصلاة الحالية"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                android:background="@drawable/badge_background"
                android:padding="4dp"
                android:layout_marginStart="8dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Prayer times section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="وقت الصلاة:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="@font/alkalami" />

                <TextView
                    android:id="@+id/tv_prayer_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="04:30"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:fontFamily="@font/alkalami" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="وقت الإقامة:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="@font/alkalami" />

                <TextView
                    android:id="@+id/tv_iqama_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="04:40"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:fontFamily="@font/alkalami" />

            </LinearLayout>

        </LinearLayout>

        <!-- Time remaining section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_time_until_prayer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="متبقي: 2 ساعة 15 دقيقة 30 ثانية"
                android:textSize="14sp"
                android:textColor="@color/accent"
                android:fontFamily="@font/alkalami"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_time_until_iqama"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="متبقي للإقامة: 10 دقيقة 30 ثانية"
                android:textSize="14sp"
                android:textColor="@color/accent"
                android:fontFamily="@font/alkalami"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Azkar section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="الأذكار:"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:fontFamily="@font/alkalami"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tv_azkar_at_adhan"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="أذكار عند الأذان: اللَّهُمَّ صَلِّ وَسَلِّمْ عَلَى مُحَمَّدٍ"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:fontFamily="@font/alkalami"
                android:layout_marginBottom="4dp"
                android:background="@drawable/azkar_background"
                android:padding="8dp" />

            <TextView
                android:id="@+id/tv_azkar_after_prayer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="أذكار بعد الصلاة: سبحان الله (٣٣ مرة)"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:fontFamily="@font/alkalami"
                android:background="@drawable/azkar_background"
                android:padding="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView> 