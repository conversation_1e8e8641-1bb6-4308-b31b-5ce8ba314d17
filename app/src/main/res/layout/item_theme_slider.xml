<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_theme"
    style="@style/PrayerApp.Card"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginStart="@dimen/_12sdp"
    android:layout_marginEnd="@dimen/_12sdp"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardElevation="@dimen/_8sdp"
    app:cardCornerRadius="@dimen/_16sdp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Theme Preview Image -->
        <ImageView
            android:id="@+id/img_theme_thumbnail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:background="@color/card_background" />

        <!-- Loading Indicator -->
        <ProgressBar
            android:id="@+id/progress_theme_loading"
            android:layout_width="@dimen/_32sdp"
            android:layout_height="@dimen/_32sdp"
            android:layout_gravity="center"
            android:indeterminateTint="@color/primary"
            android:visibility="gone" />

        <!-- Download Status Overlay -->
        <LinearLayout
            android:id="@+id/layout_theme_download_status"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:background="#80000000"
            android:visibility="gone">

            <ImageView
                android:id="@+id/icon_theme_download"
                android:layout_width="@dimen/_40sdp"
                android:layout_height="@dimen/_40sdp"
                android:src="@drawable/ic_download"
                android:tint="@color/white"
                android:layout_marginBottom="@dimen/_8sdp" />

            <TextView
                android:id="@+id/text_theme_download"
                style="@style/PrayerApp.Text.Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="تحميل"
                android:textColor="@color/white"
                android:gravity="center" />

        </LinearLayout>

        <!-- Theme Info Overlay (Similar to the image style) -->
        <LinearLayout
            android:id="@+id/layout_theme_info_overlay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@drawable/gradient_overlay"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Top section with location-style info -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="@dimen/_12sdp">

                <ImageView
                    android:layout_width="@dimen/_16sdp"
                    android:layout_height="@dimen/_16sdp"
                    android:src="@drawable/ic_location"
                    android:tint="@color/white"
                    android:layout_marginEnd="@dimen/_4sdp" />

                <TextView
                    android:id="@+id/text_theme_location"
                    style="@style/PrayerApp.Text.Caption"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="محلي"
                    android:textColor="@color/white"
                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="2" />

            </LinearLayout>

            <!-- Main title -->
            <TextView
                android:id="@+id/text_theme_title"
                style="@style/PrayerApp.Text.Headline3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ثيم الطبيعة"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:shadowColor="@color/black"
                android:shadowDx="2"
                android:shadowDy="2"
                android:shadowRadius="4"
                android:layout_marginBottom="@dimen/_4sdp" />

            <!-- Subtitle -->
            <TextView
                android:id="@+id/text_theme_subtitle"
                style="@style/PrayerApp.Text.Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="خلفيات طبيعية جميلة"
                android:textColor="@color/white"
                android:shadowColor="@color/black"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="2"
                android:layout_marginBottom="@dimen/_12sdp" />

            <!-- Bottom section with features -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <!-- Features badges -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/badge_iqama_support"
                        style="@style/PrayerApp.Text.Caption"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="إقامة"
                        android:textColor="@color/white"
                        android:background="@drawable/badge_small_background"
                        android:padding="@dimen/_4sdp"
                        android:layout_marginEnd="@dimen/_6sdp"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/badge_custom_type"
                        style="@style/PrayerApp.Text.Caption"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="مخصص"
                        android:textColor="@color/white"
                        android:background="@drawable/badge_small_background"
                        android:padding="@dimen/_4sdp"
                        android:visibility="gone" />

                </LinearLayout>

                <!-- Status Icon -->
                <ImageView
                    android:id="@+id/icon_theme_status_small"
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:src="@drawable/ic_check"
                    android:tint="@color/white"
                    android:background="@drawable/circle_background"
                    android:padding="@dimen/_4sdp"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

        <!-- Selection Indicator -->
        <View
            android:id="@+id/view_selection_indicator"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_4sdp"
            android:layout_gravity="top"
            android:background="@color/primary"
            android:visibility="gone" />

        <!-- Selection Border -->
        <View
            android:id="@+id/view_selection_border"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/selection_border"
            android:visibility="gone" />

    </FrameLayout>

</androidx.cardview.widget.CardView>
