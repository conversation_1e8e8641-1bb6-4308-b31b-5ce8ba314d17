<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Template Preview Container -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_template_preview"
            style="@style/PrayerApp.Card"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="@dimen/_16sdp"
            android:layout_weight="1"
            android:visibility="visible"
            app:cardElevation="@dimen/_8sdp">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <!-- Template GIF Display -->
                <pl.droidsonroids.gif.GifImageView
                    android:id="@+id/gif_template_preview"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/card_background"
                    android:scaleType="center" />

                <!-- Loading Indicator -->
                <ProgressBar
                    android:id="@+id/progress_loading"
                    android:layout_width="@dimen/_48sdp"
                    android:layout_height="@dimen/_48sdp"
                    android:layout_gravity="center"
                    android:indeterminateTint="@color/primary"
                    android:visibility="gone" />

                <!-- Download Status Overlay -->
                <LinearLayout
                    android:id="@+id/layout_download_status"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#80000000"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/icon_download_status"
                        android:layout_width="@dimen/_64sdp"
                        android:layout_height="@dimen/_64sdp"
                        android:layout_marginBottom="@dimen/_16sdp"
                        android:src="@drawable/ic_download"
                        android:tint="@color/white" />

                    <TextView
                        android:id="@+id/text_download_status"
                        style="@style/PrayerApp.Text.Headline4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="اضغط للتحميل"
                        android:textColor="@color/white" />

                    <ProgressBar
                        android:id="@+id/progress_download"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="200dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16sdp"
                        android:progressTint="@color/primary"
                        android:visibility="gone" />

                </LinearLayout>

                <!-- Template Info Overlay -->
                <LinearLayout
                    android:id="@+id/layout_template_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:background="@drawable/gradient_overlay"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/text_template_name"
                                style="@style/PrayerApp.Text.Headline4"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:shadowColor="@color/black"
                                android:shadowDx="1"
                                android:shadowDy="1"
                                android:shadowRadius="2"
                                android:text="قالب الأذكار الأول"
                                android:textColor="@color/white" />

                            <TextView
                                android:id="@+id/text_template_description"
                                style="@style/PrayerApp.Text.Body2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/_4sdp"
                                android:shadowColor="@color/black"
                                android:shadowDx="1"
                                android:shadowDy="1"
                                android:shadowRadius="2"
                                android:text="قالب كلاسيكي لعرض الأذكار"
                                android:textColor="@color/white" />

                        </LinearLayout>

                        <!-- Download/Status Icon -->
                        <ImageView
                            android:id="@+id/icon_template_status"
                            android:layout_width="@dimen/_32sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:background="@drawable/icon_background"
                            android:padding="@dimen/_6sdp"
                            android:src="@drawable/ic_download"
                            android:tint="@color/white"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

            </FrameLayout>

        </androidx.cardview.widget.CardView>

        <!-- 3D Slider Container -->
        <androidx.cardview.widget.CardView
            style="@style/PrayerApp.Card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16sdp"
            android:layout_marginEnd="@dimen/_16sdp"
            android:layout_marginBottom="@dimen/_16sdp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <!-- Slider Title -->
                <TextView
                    style="@style/PrayerApp.Text.Headline4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="اختيار القالب"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="@dimen/_12sdp" />

                <!-- 3D ViewPager2 for Templates -->
                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/viewpager_templates"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="@dimen/_16sdp" />

                <!-- Template Indicators -->
                <LinearLayout
                    android:id="@+id/layout_indicators"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_marginBottom="@dimen/_16sdp" />

                <!-- Action Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/btn_download_template"
                        style="@style/Widget.AppCompat.Button.Borderless"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="@dimen/_8sdp"
                        android:background="@drawable/button_secondary_background"
                        android:text="تحميل"
                        android:textColor="@color/primary"
                        android:drawableStart="@drawable/ic_download"
                        android:drawablePadding="@dimen/_8sdp"
                        android:visibility="gone" />

                    <Button
                        android:id="@+id/btn_preview_template"
                        style="@style/Widget.AppCompat.Button.Borderless"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="@dimen/_8sdp"
                        android:background="@drawable/button_secondary_background"
                        android:text="معاينة"
                        android:textColor="@color/primary"
                        android:drawableStart="@drawable/ic_preview"
                        android:drawablePadding="@dimen/_8sdp" />

                    <Button
                        android:id="@+id/btn_apply_template"
                        style="@style/Widget.AppCompat.Button.Borderless"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="@dimen/_8sdp"
                        android:background="@drawable/button_primary_background"
                        android:text="تطبيق"
                        android:textColor="@color/white"
                        android:drawableStart="@drawable/ic_check"
                        android:drawablePadding="@dimen/_8sdp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <!-- Floating Action Button for Refresh -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_refresh"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="@dimen/_16sdp"
        android:src="@drawable/ic_refresh"
        app:tint="@color/white"
        app:backgroundTint="@color/primary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
