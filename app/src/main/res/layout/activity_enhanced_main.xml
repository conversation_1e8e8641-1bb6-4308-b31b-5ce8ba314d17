<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- Header with title and settings -->
    <LinearLayout
        android:id="@+id/layout_header"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="مواقيت الصلاة"
            android:textSize="24sp"
            android:textStyle="bold"
            android:fontFamily="@font/alkalami" />

        <ImageButton
            android:id="@+id/btn_settings"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_settings"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/settings_title"
            android:padding="12dp"
            android:tint="@color/primary" />

    </LinearLayout>

    <!-- Live time and date section -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_live_time"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/card_background"
        app:layout_constraintTop_toBottomOf="@id/layout_header"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Current time -->
            <TextView
                android:id="@+id/tv_current_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="14:30:25"
                android:textSize="32sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:fontFamily="@font/alkalami"
                android:layout_gravity="center"
                android:layout_marginBottom="8dp" />

            <!-- Gregorian date -->
            <TextView
                android:id="@+id/tv_gregorian_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="الأحد، 15 يناير 2024"
                android:textSize="16sp"
                android:textColor="@color/text_primary"
                android:fontFamily="@font/alkalami"
                android:layout_gravity="center"
                android:layout_marginBottom="4dp" />

            <!-- Hijri date -->
            <TextView
                android:id="@+id/tv_hijri_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="الأحد 3 رجب 1445"
                android:textSize="16sp"
                android:textColor="@color/text_secondary"
                android:fontFamily="@font/alkalami"
                android:layout_gravity="center" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- News ticker section -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_news_ticker"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@color/news_ticker_background"
        app:layout_constraintTop_toBottomOf="@id/card_live_time"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="12dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_news"
                android:tint="@color/news_ticker_text"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/tv_news_ticker"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="مرحباً بكم في تطبيق مواقيت الصلاة"
                android:textSize="14sp"
                android:textColor="@color/news_ticker_text"
                android:fontFamily="@font/alkalami"
                android:singleLine="true"
                android:ellipsize="marquee"
                android:marqueeRepeatLimit="marquee_forever"
                android:scrollHorizontally="true"
                android:focusable="true"
                android:focusableInTouchMode="true" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Next prayer info card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_next_prayer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/next_prayer_background"
        app:layout_constraintTop_toBottomOf="@id/card_news_ticker"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="الصلاة القادمة"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:fontFamily="@font/alkalami"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_next_prayer_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الظهر"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/alkalami" />

                    <TextView
                        android:id="@+id/tv_next_prayer_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="12:30"
                        android:textSize="20sp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/alkalami" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="متبقي:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/alkalami" />

                    <TextView
                        android:id="@+id/tv_time_until_next_prayer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="2 ساعة 15 دقيقة"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/accent"
                        android:fontFamily="@font/alkalami" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Prayer times list -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_prayer_times"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card_next_prayer"
        tools:listitem="@layout/item_enhanced_prayer_times" />

    <!-- Refresh button -->
    <Button
        android:id="@+id/btn_refresh"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="تحديث مواقيت الصلاة"
        android:textSize="16sp"
        android:padding="12dp"
        android:background="@drawable/button_primary_background"
        android:textColor="@android:color/white"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Progress bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 