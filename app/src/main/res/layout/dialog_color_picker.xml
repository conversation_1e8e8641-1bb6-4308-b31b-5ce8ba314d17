<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/_16sdp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="اختر اللون المناسب"
        android:textSize="@dimen/_14sdp"
        android:textColor="@color/text_secondary"
        android:gravity="center"
        android:layout_marginBottom="@dimen/_16sdp" />

    <GridView
        android:id="@+id/grid_colors"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:numColumns="6"
        android:horizontalSpacing="@dimen/_8sdp"
        android:verticalSpacing="@dimen/_8sdp"
        android:stretchMode="columnWidth"
        android:gravity="center" />

</LinearLayout>
