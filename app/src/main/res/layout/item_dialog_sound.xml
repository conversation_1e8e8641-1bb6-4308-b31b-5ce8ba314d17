<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="@dimen/_12sdp"
    android:gravity="center_vertical"
    android:background="@drawable/selector_item_background"
    android:clickable="true"
    android:focusable="true">

    <!-- Radio Button -->
    <RadioButton
        android:id="@+id/radio_sound"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_12sdp"
        app:buttonTint="@color/primary" />

    <!-- Sound Name -->
    <TextView
        android:id="@+id/text_sound_name"
        style="@style/PrayerApp.Text.Body1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="الشيخ محمد رفعت"
        android:textColor="@color/text_primary" />

    <!-- Play Button -->
    <ImageView
        android:id="@+id/btn_play_sound"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="@dimen/_40sdp"
        android:src="@drawable/ic_play"
        android:background="@drawable/icon_background"
        android:padding="@dimen/_8sdp"
        android:tint="@color/primary"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?android:attr/selectableItemBackgroundBorderless" />

</LinearLayout>
