<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_4sdp"
    app:cardCornerRadius="@dimen/_8sdp"
    app:cardElevation="@dimen/_4sdp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/_80sdp">

        <ImageView
            android:id="@+id/image_photo"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_gallery" />

        <ImageButton
            android:id="@+id/btn_delete_photo"
            android:layout_width="@dimen/_24sdp"
            android:layout_height="@dimen/_24sdp"
            android:layout_gravity="top|end"
            android:layout_margin="@dimen/_4sdp"
            android:background="@drawable/button_danger_background"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="@color/white"
            android:contentDescription="حذف الصورة" />

    </FrameLayout>

</androidx.cardview.widget.CardView>
