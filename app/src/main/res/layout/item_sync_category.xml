<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="@dimen/_8sdp"
    android:background="?attr/selectableItemBackground">

    <CheckBox
        android:id="@+id/checkbox_category"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_12sdp"
        android:checked="true"
        app:buttonTint="@color/primary" />

    <ImageView
        android:id="@+id/icon_category"
        android:layout_width="@dimen/_24sdp"
        android:layout_height="@dimen/_24sdp"
        android:layout_marginEnd="@dimen/_12sdp"
        android:src="@drawable/ic_settings"
        android:tint="@color/primary" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/text_category_name"
            style="@style/PrayerApp.Text.Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="أوقات الصلاة"
            android:textColor="@color/text_primary" />

        <TextView
            android:id="@+id/text_category_description"
            style="@style/PrayerApp.Text.Caption"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="إعدادات أوقات الصلاة والأذان"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="@dimen/_2sdp" />

    </LinearLayout>

    <TextView
        android:id="@+id/text_category_size"
        style="@style/PrayerApp.Text.Caption"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="1.2 KB"
        android:textColor="@color/text_secondary"
        android:background="@drawable/badge_background"
        android:padding="@dimen/_4sdp" />

</LinearLayout>
