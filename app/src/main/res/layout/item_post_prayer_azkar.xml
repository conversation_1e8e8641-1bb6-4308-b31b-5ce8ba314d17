<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/PrayerApp.Card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/_12sdp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/_16sdp">

        <!-- Prayer Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="@dimen/_12sdp">

            <ImageView
                android:id="@+id/icon_prayer"
                android:layout_width="@dimen/_32sdp"
                android:layout_height="@dimen/_32sdp"
                android:layout_marginEnd="@dimen/_12sdp"
                android:src="@drawable/ic_prayer_times"
                android:tint="@color/primary" />

            <TextView
                android:id="@+id/text_prayer_name"
                style="@style/PrayerApp.Text.Headline3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="صلاة الفجر"
                android:textColor="@color/text_primary" />

            <Switch
                android:id="@+id/switch_enable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:buttonTint="@color/primary"
                app:thumbTint="@color/white"
                app:trackTint="@color/primary" />

        </LinearLayout>

        <!-- Duration Controls -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@drawable/card_background"
            android:padding="@dimen/_12sdp">

            <TextView
                android:id="@+id/text_duration_label"
                style="@style/PrayerApp.Text.Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/display_duration_minutes"
                android:textColor="@color/text_secondary"
                android:layout_marginEnd="@dimen/_16sdp" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <!-- Duration Counter -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/card_background"
                android:padding="@dimen/_4sdp">

                <ImageButton
                    android:id="@+id/btn_decrease"
                    android:layout_width="@dimen/_32sdp"
                    android:layout_height="@dimen/_32sdp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@android:drawable/ic_menu_delete"
                    android:tint="@color/primary"
                    android:contentDescription="تقليل المدة" />

                <TextView
                    android:id="@+id/text_duration"
                    style="@style/PrayerApp.Text.Headline3"
                    android:layout_width="@dimen/_40sdp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="5"
                    android:textColor="@color/primary"
                    android:textStyle="bold" />

                <ImageButton
                    android:id="@+id/btn_increase"
                    android:layout_width="@dimen/_32sdp"
                    android:layout_height="@dimen/_32sdp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@android:drawable/ic_menu_add"
                    android:tint="@color/primary"
                    android:contentDescription="زيادة المدة" />

            </LinearLayout>

            <TextView
                style="@style/PrayerApp.Text.Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="دقيقة"
                android:textColor="@color/text_secondary"
                android:layout_marginStart="@dimen/_8sdp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
