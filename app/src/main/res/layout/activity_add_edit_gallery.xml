<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16sdp">

            <!-- Gallery Name Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <TextView
                        style="@style/PrayerApp.Text.Headline3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/gallery_name"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="@dimen/_8sdp" />

                    <EditText
                        android:id="@+id/edit_gallery_name"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_48sdp"
                        android:background="@drawable/card_background"
                        android:hint="@string/gallery_name"
                        android:padding="@dimen/_12sdp"
                        android:textSize="@dimen/_14sdp"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Add Photos Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <TextView
                        style="@style/PrayerApp.Text.Headline3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/upload_a_photo"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="@dimen/_12sdp" />

                    <!-- Add Photo Button -->
                    <LinearLayout
                        android:id="@+id/btn_add_photos"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_48sdp"
                        android:background="@drawable/button_primary_background"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:src="@drawable/ic_add_photo"
                            android:tint="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_8sdp"
                            android:text="@string/add_photo"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_14sdp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- Photos Grid -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_view_photos"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Advanced Settings Card -->
            <androidx.cardview.widget.CardView
                style="@style/PrayerApp.Card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_12sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_16sdp">

                    <!-- Advanced Settings Header -->
                    <LinearLayout
                        android:id="@+id/btn_expand_settings"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="?attr/selectableItemBackground"
                        android:padding="@dimen/_8sdp"
                        android:layout_marginBottom="@dimen/_12sdp">

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:layout_marginEnd="@dimen/_8sdp"
                            android:src="@drawable/ic_settings"
                            android:tint="@color/primary" />

                        <TextView
                            style="@style/PrayerApp.Text.Headline3"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/advanced_setting"
                            android:textColor="@color/text_primary" />

                        <ImageView
                            android:id="@+id/icon_expand"
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:src="@drawable/ic_expand_more"
                            android:tint="@color/text_secondary" />

                    </LinearLayout>

                    <!-- Advanced Settings Content -->
                    <LinearLayout
                        android:id="@+id/layout_advanced_settings"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <!-- Gallery Duration -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="@dimen/_16sdp">

                            <TextView
                                style="@style/PrayerApp.Text.Body1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/duration_of_photo_gallery_minutes"
                                android:textColor="@color/text_primary" />

                            <Button
                                android:id="@+id/btn_gallery_duration"
                                style="@style/Widget.AppCompat.Button.Borderless"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/button_primary_background"
                                android:text="@string/edit"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_12sdp"
                                android:padding="@dimen/_8sdp" />

                        </LinearLayout>

                        <!-- Image Duration -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="@dimen/_16sdp">

                            <TextView
                                style="@style/PrayerApp.Text.Body1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/duration_of_prayer_times_minutes"
                                android:textColor="@color/text_primary" />

                            <Button
                                android:id="@+id/btn_image_duration"
                                style="@style/Widget.AppCompat.Button.Borderless"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/button_primary_background"
                                android:text="@string/edit"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_12sdp"
                                android:padding="@dimen/_8sdp" />

                        </LinearLayout>

                        <!-- Enable with Announcement -->
                        <Switch
                            android:id="@+id/switch_enable_with_announcement"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/enable_photo_gallery_with_announcement"
                            android:textSize="@dimen/_14sdp"
                            android:checked="true"
                            app:buttonTint="@color/primary"
                            app:thumbTint="@color/white"
                            app:trackTint="@color/primary"
                            android:layout_marginBottom="@dimen/_8sdp" />

                        <!-- Enable with Friday Announcement -->
                        <Switch
                            android:id="@+id/switch_enable_with_announcement_friday"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/enable_photo_gallery_with_announcement_jomaa"
                            android:textSize="@dimen/_14sdp"
                            android:checked="true"
                            app:buttonTint="@color/primary"
                            app:thumbTint="@color/white"
                            app:trackTint="@color/primary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <Button
                    android:id="@+id/btn_save_gallery"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/_48sdp"
                    android:layout_weight="1"
                    android:layout_marginEnd="@dimen/_8sdp"
                    android:background="@drawable/button_primary_background"
                    android:text="@string/save"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_14sdp"
                    android:textStyle="bold" />

                <Button
                    android:id="@+id/btn_delete_gallery"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/_48sdp"
                    android:layout_weight="1"
                    android:layout_marginStart="@dimen/_8sdp"
                    android:background="@drawable/button_danger_background"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_14sdp"
                    android:textStyle="bold"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
