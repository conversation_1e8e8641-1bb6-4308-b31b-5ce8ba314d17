<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Base PrayerApp Style -->
    <style name="PrayerApp" />

    <!-- <PERSON><PERSON> Styles -->
    <style name="PrayerApp.Button" parent="Widget.Material3.Button">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">12dp</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="android:textColor">@color/text_on_primary</item>
        <item name="android:elevation">4dp</item>
        <item name="android:stateListAnimator">@animator/button_state_animator</item>
    </style>
    
    <style name="PrayerApp.Button.Secondary" parent="PrayerApp.Button">
        <item name="backgroundTint">@color/secondary</item>
        <item name="android:textColor">@color/text_on_secondary</item>
    </style>
    
    <style name="PrayerApp.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">12dp</item>
        <item name="strokeColor">@color/primary</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:textColor">@color/primary</item>
    </style>
    

    
    <!-- Card Styles -->
    <style name="PrayerApp.Card" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">8dp</item>
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="android:layout_margin">8dp</item>
        <item name="rippleColor">@color/ripple</item>
    </style>

    <style name="PrayerApp.Card.Prayer" parent="PrayerApp.Card">
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">12dp</item>
        <item name="android:layout_margin">12dp</item>
    </style>
    
    <!-- EditText Styles -->
    <style name="PrayerApp.EditText" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/text_hint</item>
    </style>
    
    <!-- Dropdown/Spinner Styles -->
    <style name="PrayerApp.Dropdown" parent="Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="boxCornerRadiusTopStart">12dp</item>
        <item name="boxCornerRadiusTopEnd">12dp</item>
        <item name="boxCornerRadiusBottomStart">12dp</item>
        <item name="boxCornerRadiusBottomEnd">12dp</item>
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/text_hint</item>
    </style>
    
    <!-- Animation Styles -->
    <style name="WindowAnimationTransition">
        <item name="android:windowEnterAnimation">@anim/slide_in_right</item>
        <item name="android:windowExitAnimation">@anim/slide_out_left</item>
    </style>
    
    <!-- Prayer Time Specific Styles -->
    <style name="PrayerApp.PrayerTime" />

    <style name="PrayerApp.PrayerTime.Container">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">16dp</item>
        <item name="android:background">@drawable/prayer_time_background</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <style name="PrayerApp.PrayerTime.Name">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="PrayerApp.PrayerTime.Time">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/primary</item>
    </style>

    <!-- Base Text Style -->
    <style name="PrayerApp.Text" />

    <!-- Complete Text Styles -->
    <style name="PrayerApp.Text.Headline1" parent="PrayerApp.Text">
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="PrayerApp.Text.Headline2" parent="PrayerApp.Text">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="PrayerApp.Text.Headline3" parent="PrayerApp.Text">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="PrayerApp.Text.Headline4" parent="PrayerApp.Text">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="PrayerApp.Text.Body1" parent="PrayerApp.Text">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="PrayerApp.Text.Body2" parent="PrayerApp.Text">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>

    <style name="PrayerApp.Text.Caption" parent="PrayerApp.Text">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_hint</item>
    </style>

    <style name="AlrabeaIcon">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/_28sdp</item>
        <item name="android:src">@drawable/img_logo_large</item>
        <item name="android:paddingTop">@dimen/_4sdp</item>
        <item name="android:paddingBottom">@dimen/_4sdp</item>
    </style>

    <style name="MovingText" parent="PrayerApp">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginStart">@dimen/_10sdp</item>
        <item name="android:layout_marginEnd">@dimen/_10sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus5sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus5sdp</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:focusable">true</item>
        <item name="android:gravity">center</item>
        <item name="android:marqueeRepeatLimit">marquee_forever</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">#504e4e</item>
        <item name="android:textSize">@dimen/_14sdp</item>
    </style>



    <style name="LinearLayoutPrayerTimeRow">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <!--        <item name="android:background">@drawable/text_view_pray_white_new</item>-->
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center</item>
    </style>


    <style name="LinearLayoutPrayerTimeRow.blue" parent="LinearLayoutPrayerTimeRow">
        <item name="android:layout_height">wrap_content</item>
    </style>


    <style name="TimeTextView.blue2">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/_30sdp</item>
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:textStyle">bold</item>
        <item name="android:text">Testing</item>
        <item name="android:textColor">@color/colorBlueMain</item>
    </style>

    <style name="WrappedContentText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/colorblack</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="TimeTextView.NameAR" parent="TimeTextView">
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
        <item name="android:layout_gravity">right</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">@dimen/_26sdp</item>
        <item name="android:text">04:14</item>
    </style>
    <style name="TimeTextView.blue_new.TimeNameAR" parent="TimeTextView.NameAR">
        <item name="android:fontFamily">@font/droid_arabic_kufi_bold</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/_22sdp</item>
    </style>
    <style name="TimeTextView.blue.TimeNameAR" parent="TimeTextView.blue">
        <item name="android:layout_marginTop">@dimen/_minus13sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus16sdp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="TimeTextView.blue.TimeRemain" parent="TimeTextView.brown.TimeRemain">
        <item name="android:background">@drawable/without_corners_bottom_10_background_blue</item>
    </style>


    <style name="TimeTextView.white_new.TimeRemain" parent="TimeTextView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_weight">0</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:visibility">gone</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:focusable">true</item>
        <item name="android:state_selected">true</item>
        <item name="android:marqueeRepeatLimit">marquee_forever</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:fontFamily">@font/droid_arabic_kufi_bold</item>
        <item name="android:textSize">@dimen/_14sdp</item>
        <item name="android:text">متبقي ساعة و3 دقائق</item>
        <item name="android:textColor">#3c4754</item>
    </style>
    <style name="TimeTextView.brown.TimeRemain" parent="TimeTextView.white_new.TimeRemain">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/without_corners_bottom_10_background_brown</item>
        <item name="android:textSize">@dimen/_15sdp</item>
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:layout_marginLeft">@dimen/_20sdp</item>
        <item name="android:layout_marginRight">@dimen/_20sdp</item>
    </style>
    <style name="TimeTextView" parent="WrappedContentText">

        <!--        <item name="android:fontFamily">@font/droid_arabic_kufi_bold</item>-->
        <item name="android:includeFontPadding">false</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="TimeTextView.blue">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/_30sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:textStyle">bold</item>
        <item name="android:text">Testing</item>
        <item name="android:textColor">@color/colorBlueMain</item>
    </style>
    <style name="TimeTextView.blue.Time" parent="TimeTextView.blue">
        <item name="android:text">08:30</item>
    </style>

    <style name="TimeTextView.blue.TimeType" parent="TimeTextView.blue">
        <item name="android:textSize">@dimen/_16sdp</item>
        <item name="android:layout_gravity">left|center_vertical</item>
        <item name="android:text">AM</item>
    </style>

</resources>
