<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Base PrayerApp Style -->
    <style name="PrayerApp" />

    <!-- <PERSON><PERSON> Styles -->
    <style name="PrayerApp.Button" parent="Widget.Material3.Button">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">12dp</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="android:textColor">@color/text_on_primary</item>
        <item name="android:elevation">4dp</item>
        <item name="android:stateListAnimator">@animator/button_state_animator</item>
    </style>
    
    <style name="PrayerApp.Button.Secondary" parent="PrayerApp.Button">
        <item name="backgroundTint">@color/secondary</item>
        <item name="android:textColor">@color/text_on_secondary</item>
    </style>
    
    <style name="PrayerApp.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">12dp</item>
        <item name="strokeColor">@color/primary</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:textColor">@color/primary</item>
    </style>
    

    
    <!-- Card Styles -->
    <style name="PrayerApp.Card" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">8dp</item>
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="android:layout_margin">8dp</item>
        <item name="rippleColor">@color/ripple</item>
    </style>

    <style name="PrayerApp.Card.Prayer" parent="PrayerApp.Card">
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">12dp</item>
        <item name="android:layout_margin">12dp</item>
    </style>
    
    <!-- EditText Styles -->
    <style name="PrayerApp.EditText" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/text_hint</item>
    </style>
    
    <!-- Dropdown/Spinner Styles -->
    <style name="PrayerApp.Dropdown" parent="Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="boxCornerRadiusTopStart">12dp</item>
        <item name="boxCornerRadiusTopEnd">12dp</item>
        <item name="boxCornerRadiusBottomStart">12dp</item>
        <item name="boxCornerRadiusBottomEnd">12dp</item>
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/text_hint</item>
    </style>
    
    <!-- Animation Styles -->
    <style name="WindowAnimationTransition">
        <item name="android:windowEnterAnimation">@anim/slide_in_right</item>
        <item name="android:windowExitAnimation">@anim/slide_out_left</item>
    </style>
    
    <!-- Prayer Time Specific Styles -->
    <style name="PrayerApp.PrayerTime" />

    <style name="PrayerApp.PrayerTime.Container">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">16dp</item>
        <item name="android:background">@drawable/prayer_time_background</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <style name="PrayerApp.PrayerTime.Name">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="PrayerApp.PrayerTime.Time">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/primary</item>
    </style>

    <!-- Base Text Style -->
    <style name="PrayerApp.Text" />

    <!-- Complete Text Styles -->
    <style name="PrayerApp.Text.Headline1" parent="PrayerApp.Text">
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="PrayerApp.Text.Headline2" parent="PrayerApp.Text">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="PrayerApp.Text.Headline3" parent="PrayerApp.Text">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="PrayerApp.Text.Headline4" parent="PrayerApp.Text">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="PrayerApp.Text.Body1" parent="PrayerApp.Text">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="PrayerApp.Text.Body2" parent="PrayerApp.Text">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>

    <style name="PrayerApp.Text.Caption" parent="PrayerApp.Text">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_hint</item>
    </style>
    
</resources>
