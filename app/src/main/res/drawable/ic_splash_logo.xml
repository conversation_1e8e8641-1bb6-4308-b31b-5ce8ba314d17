<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="120dp"
    android:height="120dp"
    android:viewportWidth="120"
    android:viewportHeight="120">
    
    <!-- Background Circle -->
    <path
        android:fillColor="@color/white"
        android:pathData="M60,60m-50,0a50,50 0,1 1,100 0a50,50 0,1 1,-100 0" />
    
    <!-- <PERSON> Silhouette -->
    <path
        android:fillColor="@color/primary"
        android:pathData="M30,80 L30,70 Q30,65 35,65 L85,65 Q90,65 90,70 L90,80 Z" />
    
    <!-- <PERSON>ret Left -->
    <path
        android:fillColor="@color/primary"
        android:pathData="M35,65 L35,40 Q35,35 40,35 L45,35 Q50,35 50,40 L50,65" />
    
    <!-- <PERSON>ret Right -->
    <path
        android:fillColor="@color/primary"
        android:pathData="M70,65 L70,40 Q70,35 75,35 L80,35 Q85,35 85,40 L85,65" />
    
    <!-- Main Dome -->
    <path
        android:fillColor="@color/primary"
        android:pathData="M50,65 Q50,50 60,50 Q70,50 70,65" />
    
    <!-- Crescent Moon -->
    <path
        android:fillColor="@color/secondary"
        android:pathData="M58,45 Q55,40 60,42 Q65,40 62,45 Q60,47 58,45" />
    
    <!-- Stars -->
    <path
        android:fillColor="@color/secondary"
        android:pathData="M25,30 L27,35 L32,35 L28,38 L30,43 L25,40 L20,43 L22,38 L18,35 L23,35 Z" />
    
    <path
        android:fillColor="@color/secondary"
        android:pathData="M95,25 L97,30 L102,30 L98,33 L100,38 L95,35 L90,38 L92,33 L88,30 L93,30 Z" />
    
</vector>
