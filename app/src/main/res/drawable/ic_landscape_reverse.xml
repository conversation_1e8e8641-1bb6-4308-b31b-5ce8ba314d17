<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    
    <path
        android:fillColor="@color/secondary"
        android:pathData="M1,7L23,7C22.1,7 23,7.9 23,9L23,15C23,16.1 22.1,17 21,17L3,17C1.9,17 1,16.1 1,15L1,9C1,7.9 1.9,7 3,7L1,7ZM21,15L21,9L3,9L3,15L21,15Z" />
    
    <!-- Rotation indicator -->
    <path
        android:fillColor="@color/secondary"
        android:pathData="M19,11L17,9V10H15V12H17V13L19,11Z" />
    
</vector>
