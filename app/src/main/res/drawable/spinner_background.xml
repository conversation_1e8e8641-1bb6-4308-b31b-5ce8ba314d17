<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/surface_variant" />
            <corners android:radius="@dimen/_12sdp" />
            <stroke android:width="2dp" android:color="@color/primary" />
        </shape>
    </item>
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/surface" />
            <corners android:radius="@dimen/_12sdp" />
            <stroke android:width="2dp" android:color="@color/primary" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/surface" />
            <corners android:radius="@dimen/_12sdp" />
            <stroke android:width="1dp" android:color="@color/divider" />
        </shape>
    </item>
</selector>
