<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/primary_dark" />
            <corners android:radius="12dp" />
            <stroke android:width="0dp" />
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/text_hint" />
            <corners android:radius="12dp" />
            <stroke android:width="0dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/gradient_start"
                android:endColor="@color/gradient_end"
                android:angle="45" />
            <corners android:radius="12dp" />
            <stroke android:width="0dp" />
        </shape>
    </item>
</selector>
