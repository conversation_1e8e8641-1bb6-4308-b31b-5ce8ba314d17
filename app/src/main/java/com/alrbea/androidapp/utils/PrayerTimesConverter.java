package com.alrbea.androidapp.utils;

import com.alrbea.androidapp.data.model.PrayerTimes;
import com.alrbea.androidapp.data.model.PrayerTimesEntity;
import java.util.ArrayList;
import java.util.List;

public class PrayerTimesConverter {
    
    public static PrayerTimesEntity apiToEntity(PrayerTimes prayerTimes, String country, String method) {
        if (prayerTimes == null) return null;
        
        return new PrayerTimesEntity(
            country,
            prayerTimes.getCity(),
            method,
            prayerTimes.getDate(),
            prayerTimes.getFajr(),
            prayerTimes.getSunrise(),
            prayerTimes.getDhuhr(),
            prayerTimes.getAsr(),
            prayerTimes.getMaghrib(),
            prayerTimes.getIsha()
        );
    }
    
    public static PrayerTimes entityToApi(PrayerTimesEntity entity) {
        if (entity == null) return null;
        
        PrayerTimes prayerTimes = new PrayerTimes();
        prayerTimes.setFajr(entity.getFajr());
        prayerTimes.setSunrise(entity.getSunrise());
        prayerTimes.setDhuhr(entity.getDhuhr());
        prayerTimes.setAsr(entity.getAsr());
        prayerTimes.setMaghrib(entity.getMaghrib());
        prayerTimes.setIsha(entity.getIsha());
        prayerTimes.setDate(entity.getDate());
        prayerTimes.setCity(entity.getCity());
        
        return prayerTimes;
    }
    
    public static List<PrayerTimesEntity> apiListToEntityList(List<PrayerTimes> prayerTimesList, String country, String method) {
        if (prayerTimesList == null) return new ArrayList<>();
        
        List<PrayerTimesEntity> entityList = new ArrayList<>();
        for (PrayerTimes prayerTimes : prayerTimesList) {
            PrayerTimesEntity entity = apiToEntity(prayerTimes, country, method);
            if (entity != null) {
                entityList.add(entity);
            }
        }
        return entityList;
    }
    
    public static List<PrayerTimes> entityListToApiList(List<PrayerTimesEntity> entityList) {
        if (entityList == null) return new ArrayList<>();
        
        List<PrayerTimes> prayerTimesList = new ArrayList<>();
        for (PrayerTimesEntity entity : entityList) {
            PrayerTimes prayerTimes = entityToApi(entity);
            if (prayerTimes != null) {
                prayerTimesList.add(prayerTimes);
            }
        }
        return prayerTimesList;
    }
} 