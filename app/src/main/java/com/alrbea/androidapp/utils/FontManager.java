package com.alrbea.androidapp.utils;

import android.content.Context;
import android.graphics.Typeface;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FontManager {
    
    private Context context;
    private Map<String, Typeface> fontCache;
    private List<String> availableFonts;
    
    public FontManager(Context context) {
        this.context = context;
        this.fontCache = new HashMap<>();
        this.availableFonts = new ArrayList<>();
        initializeFonts();
    }
    
    private void initializeFonts() {
        // Add default system fonts
        availableFonts.add("default");
        availableFonts.add("serif");
        availableFonts.add("sans-serif");
        availableFonts.add("monospace");
        
        // Add custom Arabic fonts (if available in assets/fonts/)
        availableFonts.add("droid_arabic_kufi");
        availableFonts.add("droid_arabic_kufi_bold");
        availableFonts.add("amiri");
        availableFonts.add("cairo");
        availableFonts.add("tajawal");
        
        // Cache default typefaces
        fontCache.put("default", Typeface.DEFAULT);
        fontCache.put("serif", Typeface.SERIF);
        fontCache.put("sans-serif", Typeface.SANS_SERIF);
        fontCache.put("monospace", Typeface.MONOSPACE);
    }
    
    /**
     * Get list of available font names
     */
    public List<String> getAvailableFonts() {
        return new ArrayList<>(availableFonts);
    }
    
    /**
     * Get typeface for a font family
     */
    public Typeface getTypeface(String fontFamily) {
        return getTypeface(fontFamily, Typeface.NORMAL);
    }
    
    /**
     * Get typeface for a font family with specific style
     */
    public Typeface getTypeface(String fontFamily, int style) {
        if (fontFamily == null) {
            fontFamily = "default";
        }
        
        String cacheKey = fontFamily + "_" + style;
        
        // Check cache first
        if (fontCache.containsKey(cacheKey)) {
            return fontCache.get(cacheKey);
        }
        
        Typeface typeface = null;
        
        // Handle system fonts
        switch (fontFamily) {
            case "default":
                typeface = Typeface.defaultFromStyle(style);
                break;
            case "serif":
                typeface = Typeface.create(Typeface.SERIF, style);
                break;
            case "sans-serif":
                typeface = Typeface.create(Typeface.SANS_SERIF, style);
                break;
            case "monospace":
                typeface = Typeface.create(Typeface.MONOSPACE, style);
                break;
            default:
                // Try to load custom font from assets
                typeface = loadCustomFont(fontFamily, style);
                break;
        }
        
        // Fallback to default if font not found
        if (typeface == null) {
            typeface = Typeface.defaultFromStyle(style);
        }
        
        // Cache the typeface
        fontCache.put(cacheKey, typeface);
        
        return typeface;
    }
    
    /**
     * Load custom font from assets
     */
    private Typeface loadCustomFont(String fontFamily, int style) {
        try {
            String fontPath = "fonts/" + fontFamily;
            
            // Add appropriate suffix based on style
            switch (style) {
                case Typeface.BOLD:
                    fontPath += "_bold";
                    break;
                case Typeface.ITALIC:
                    fontPath += "_italic";
                    break;
                case Typeface.BOLD_ITALIC:
                    fontPath += "_bold_italic";
                    break;
            }
            
            fontPath += ".ttf";
            
            return Typeface.createFromAsset(context.getAssets(), fontPath);
        } catch (Exception e) {
            // Try without style suffix
            try {
                String fontPath = "fonts/" + fontFamily + ".ttf";
                Typeface baseTypeface = Typeface.createFromAsset(context.getAssets(), fontPath);
                return Typeface.create(baseTypeface, style);
            } catch (Exception e2) {
                return null;
            }
        }
    }
    
    /**
     * Get display name for a font
     */
    public String getFontDisplayName(String fontFamily) {
        switch (fontFamily) {
            case "default":
                return "الخط الافتراضي";
            case "serif":
                return "خط مزخرف";
            case "sans-serif":
                return "خط بسيط";
            case "monospace":
                return "خط أحادي المسافة";
            case "droid_arabic_kufi":
                return "الكوفي العربي";
            case "droid_arabic_kufi_bold":
                return "الكوفي العربي العريض";
            case "amiri":
                return "الأميري";
            case "cairo":
                return "القاهرة";
            case "tajawal":
                return "تجوال";
            default:
                return fontFamily;
        }
    }
    
    /**
     * Check if a font is available
     */
    public boolean isFontAvailable(String fontFamily) {
        return availableFonts.contains(fontFamily);
    }
    
    /**
     * Add a custom font to the available fonts list
     */
    public void addCustomFont(String fontFamily, String displayName) {
        if (!availableFonts.contains(fontFamily)) {
            availableFonts.add(fontFamily);
        }
    }
    
    /**
     * Clear font cache
     */
    public void clearCache() {
        fontCache.clear();
        // Re-cache system fonts
        fontCache.put("default", Typeface.DEFAULT);
        fontCache.put("serif", Typeface.SERIF);
        fontCache.put("sans-serif", Typeface.SANS_SERIF);
        fontCache.put("monospace", Typeface.MONOSPACE);
    }
    
    /**
     * Get font style name
     */
    public String getStyleName(int style) {
        switch (style) {
            case Typeface.NORMAL:
                return "عادي";
            case Typeface.BOLD:
                return "عريض";
            case Typeface.ITALIC:
                return "مائل";
            case Typeface.BOLD_ITALIC:
                return "عريض مائل";
            default:
                return "عادي";
        }
    }
    
    /**
     * Get all available styles for a font
     */
    public List<Integer> getAvailableStyles(String fontFamily) {
        List<Integer> styles = new ArrayList<>();
        styles.add(Typeface.NORMAL);
        styles.add(Typeface.BOLD);
        styles.add(Typeface.ITALIC);
        styles.add(Typeface.BOLD_ITALIC);
        return styles;
    }
    
    /**
     * Check if font supports Arabic text
     */
    public boolean supportsArabic(String fontFamily) {
        // Most fonts support Arabic, but some custom fonts might not
        switch (fontFamily) {
            case "droid_arabic_kufi":
            case "droid_arabic_kufi_bold":
            case "amiri":
            case "cairo":
            case "tajawal":
                return true;
            default:
                return true; // Assume support for system fonts
        }
    }
    
    /**
     * Get recommended fonts for Arabic text
     */
    public List<String> getArabicFonts() {
        List<String> arabicFonts = new ArrayList<>();
        for (String font : availableFonts) {
            if (supportsArabic(font)) {
                arabicFonts.add(font);
            }
        }
        return arabicFonts;
    }
}
