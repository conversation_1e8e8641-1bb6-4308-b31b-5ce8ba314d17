package com.alrbea.androidapp.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.provider.Settings;
import android.util.Log;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class AppHelper {
    
    private static final String PREFS_NAME = "PrayerAppPrefs";
    private static final String TAG = "AppHelper";
    
    private static AppHelper instance;
    private SharedPreferences sharedPreferences;
    private Context context;
    
    private AppHelper(Context context) {
        this.context = context.getApplicationContext();
        this.sharedPreferences = this.context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    public static synchronized AppHelper getInstance(Context context) {
        if (instance == null) {
            instance = new AppHelper(context);
        }
        return instance;
    }
    
    // ==================== DATA STORAGE ====================
    
    public void saveString(String key, String value) {
        sharedPreferences.edit().putString(key, value).apply();
    }
    
    public String getString(String key, String defaultValue) {
        return sharedPreferences.getString(key, defaultValue);
    }
    
    public void saveInt(String key, int value) {
        sharedPreferences.edit().putInt(key, value).apply();
    }
    
    public int getInt(String key, int defaultValue) {
        return sharedPreferences.getInt(key, defaultValue);
    }
    
    public void saveBoolean(String key, boolean value) {
        sharedPreferences.edit().putBoolean(key, value).apply();
    }
    
    public boolean getBoolean(String key, boolean defaultValue) {
        return sharedPreferences.getBoolean(key, defaultValue);
    }
    
    public void saveFloat(String key, float value) {
        sharedPreferences.edit().putFloat(key, value).apply();
    }
    
    public float getFloat(String key, float defaultValue) {
        return sharedPreferences.getFloat(key, defaultValue);
    }
    
    public void saveLong(String key, long value) {
        sharedPreferences.edit().putLong(key, value).apply();
    }
    
    public long getLong(String key, long defaultValue) {
        return sharedPreferences.getLong(key, defaultValue);
    }
    
    public void removeKey(String key) {
        sharedPreferences.edit().remove(key).apply();
    }
    
    public void clearAll() {
        sharedPreferences.edit().clear().apply();
    }
    
    public boolean containsKey(String key) {
        return sharedPreferences.contains(key);
    }
    
    // ==================== APP INFO ====================
    
    public String getAppVersion() {
        try {
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
            return packageInfo.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "Error getting app version", e);
            return "1.0.0";
        }
    }
    
    public int getAppVersionCode() {
        try {
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
            return packageInfo.versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "Error getting app version code", e);
            return 1;
        }
    }
    
    public String getDeviceId() {
        return Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
    }
    
    // ==================== NETWORK ====================
    
    public boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) 
                context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }
    
    // ==================== DATE & TIME ====================
    
    public String getCurrentDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        return sdf.format(new Date());
    }
    
    public String getCurrentTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());
        return sdf.format(new Date());
    }
    
    public String getCurrentDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        return sdf.format(new Date());
    }
    
    public long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }
    
    // ==================== APP SPECIFIC HELPERS ====================
    
    // User Authentication
    public boolean isUserLoggedIn() {
        return getBoolean("is_logged_in", false);
    }
    
    public void setUserLoggedIn(boolean loggedIn) {
        saveBoolean("is_logged_in", loggedIn);
    }
    
    // Setup Status
    public boolean isLocationSetupComplete() {
        return getBoolean("location_setup_complete", false);
    }
    
    public void setLocationSetupComplete(boolean complete) {
        saveBoolean("location_setup_complete", complete);
    }
    
    public boolean isOrientationSet() {
        return getBoolean("orientation_set", false);
    }
    
    public void setOrientationSet(boolean set) {
        saveBoolean("orientation_set", set);
    }
    
    // Location Data
    public void saveLocationData(String countryCode, String countryName, 
                               String cityCode, String cityName, 
                               String methodCode, String methodName,
                               float latitude, float longitude, String timezone) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString("selected_country_code", countryCode);
        editor.putString("selected_country_name", countryName);
        editor.putString("selected_city_code", cityCode);
        editor.putString("selected_city_name", cityName);
        editor.putString("selected_method_code", methodCode);
        editor.putString("selected_method_name", methodName);
        editor.putFloat("city_latitude", latitude);
        editor.putFloat("city_longitude", longitude);
        editor.putString("city_timezone", timezone);
        editor.apply();
    }
    
    public String getSelectedCityCode() {
        return getString("selected_city_code", "riyadh");
    }
    
    public String getSelectedCityName() {
        return getString("selected_city_name", "الرياض");
    }
    
    // First Run
    public boolean isFirstRun() {
        return getBoolean("first_run", true);
    }
    
    public void setFirstRunComplete() {
        saveBoolean("first_run", false);
    }
    
    // Permissions
    public boolean arePermissionsGranted() {
        return getBoolean("permissions_granted", false);
    }
    
    public void setPermissionsGranted(boolean granted) {
        saveBoolean("permissions_granted", granted);
    }
    
    // ==================== LOGGING ====================
    
    public void logInfo(String message) {
        Log.i(TAG, message);
    }
    
    public void logError(String message, Throwable throwable) {
        Log.e(TAG, message, throwable);
    }
    
    public void logDebug(String message) {
        Log.d(TAG, message);
    }
}