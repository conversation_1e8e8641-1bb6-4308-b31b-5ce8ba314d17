package com.alrbea.androidapp.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.alrbea.androidapp.data.model.Gallery;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GalleryManager {
    
    private static final String PREFS_NAME = "GalleryManager";
    private static final String KEY_GALLERIES = "galleries";
    private static final String KEY_GALLERY_ENABLED = "gallery_enabled";
    
    private Context context;
    private SharedPreferences sharedPreferences;
    private Gson gson;
    
    public GalleryManager(Context context) {
        this.context = context;
        this.sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.gson = new Gson();
    }
    
    /**
     * Save a gallery to storage
     */
    public boolean saveGallery(Gallery gallery) {
        try {
            Map<String, Gallery> galleries = getAllGalleriesMap();
            galleries.put(gallery.getId(), gallery);
            
            String galleriesJson = gson.toJson(galleries);
            return sharedPreferences.edit()
                    .putString(KEY_GALLERIES, galleriesJson)
                    .commit();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Get a gallery by ID
     */
    public Gallery getGallery(String galleryId) {
        Map<String, Gallery> galleries = getAllGalleriesMap();
        return galleries.get(galleryId);
    }
    
    /**
     * Get all galleries as a list
     */
    public List<Gallery> getAllGalleries() {
        Map<String, Gallery> galleries = getAllGalleriesMap();
        return new ArrayList<>(galleries.values());
    }
    
    /**
     * Get all enabled galleries
     */
    public List<Gallery> getEnabledGalleries() {
        List<Gallery> allGalleries = getAllGalleries();
        List<Gallery> enabledGalleries = new ArrayList<>();
        
        for (Gallery gallery : allGalleries) {
            if (gallery.isEnabled()) {
                enabledGalleries.add(gallery);
            }
        }
        
        return enabledGalleries;
    }
    
    /**
     * Delete a gallery
     */
    public boolean deleteGallery(String galleryId) {
        try {
            Map<String, Gallery> galleries = getAllGalleriesMap();
            Gallery gallery = galleries.remove(galleryId);
            
            if (gallery != null) {
                // Delete associated image files
                for (String imagePath : gallery.getImagePaths()) {
                    FileUtils.deleteFile(imagePath);
                }
                
                String galleriesJson = gson.toJson(galleries);
                return sharedPreferences.edit()
                        .putString(KEY_GALLERIES, galleriesJson)
                        .commit();
            }
            
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Update gallery enabled state
     */
    public boolean updateGalleryEnabledState(String galleryId, boolean enabled) {
        Gallery gallery = getGallery(galleryId);
        if (gallery != null) {
            gallery.setEnabled(enabled);
            return saveGallery(gallery);
        }
        return false;
    }
    
    /**
     * Check if gallery feature is globally enabled
     */
    public boolean isGalleryFeatureEnabled() {
        return sharedPreferences.getBoolean(KEY_GALLERY_ENABLED, true);
    }
    
    /**
     * Set gallery feature enabled state
     */
    public void setGalleryFeatureEnabled(boolean enabled) {
        sharedPreferences.edit()
                .putBoolean(KEY_GALLERY_ENABLED, enabled)
                .apply();
    }
    
    /**
     * Get gallery count
     */
    public int getGalleryCount() {
        return getAllGalleries().size();
    }
    
    /**
     * Get enabled gallery count
     */
    public int getEnabledGalleryCount() {
        return getEnabledGalleries().size();
    }
    
    /**
     * Check if gallery name already exists
     */
    public boolean isGalleryNameExists(String name, String excludeId) {
        List<Gallery> galleries = getAllGalleries();
        for (Gallery gallery : galleries) {
            if (gallery.getName().equals(name) && !gallery.getId().equals(excludeId)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get galleries with images
     */
    public List<Gallery> getGalleriesWithImages() {
        List<Gallery> allGalleries = getAllGalleries();
        List<Gallery> galleriesWithImages = new ArrayList<>();
        
        for (Gallery gallery : allGalleries) {
            if (gallery.hasImages()) {
                galleriesWithImages.add(gallery);
            }
        }
        
        return galleriesWithImages;
    }
    
    /**
     * Clear all galleries (for testing or reset)
     */
    public boolean clearAllGalleries() {
        try {
            // Delete all image files first
            List<Gallery> galleries = getAllGalleries();
            for (Gallery gallery : galleries) {
                for (String imagePath : gallery.getImagePaths()) {
                    FileUtils.deleteFile(imagePath);
                }
            }
            
            return sharedPreferences.edit()
                    .remove(KEY_GALLERIES)
                    .commit();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Get all galleries as a map for internal use
     */
    private Map<String, Gallery> getAllGalleriesMap() {
        try {
            String galleriesJson = sharedPreferences.getString(KEY_GALLERIES, "{}");
            Type type = new TypeToken<Map<String, Gallery>>(){}.getType();
            Map<String, Gallery> galleries = gson.fromJson(galleriesJson, type);
            return galleries != null ? galleries : new HashMap<>();
        } catch (Exception e) {
            e.printStackTrace();
            return new HashMap<>();
        }
    }
    
    /**
     * Export galleries data (for backup)
     */
    public String exportGalleries() {
        return sharedPreferences.getString(KEY_GALLERIES, "{}");
    }
    
    /**
     * Import galleries data (for restore)
     */
    public boolean importGalleries(String galleriesJson) {
        try {
            // Validate JSON first
            Type type = new TypeToken<Map<String, Gallery>>(){}.getType();
            Map<String, Gallery> galleries = gson.fromJson(galleriesJson, type);
            
            if (galleries != null) {
                return sharedPreferences.edit()
                        .putString(KEY_GALLERIES, galleriesJson)
                        .commit();
            }
            
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
