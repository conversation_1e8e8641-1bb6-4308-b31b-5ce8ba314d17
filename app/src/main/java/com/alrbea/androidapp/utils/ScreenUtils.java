package com.alrbea.androidapp.utils;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Point;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.WindowManager;

public class ScreenUtils {
    
    public enum ScreenType {
        PHONE_PORTRAIT,
        PHONE_LANDSCAPE,
        TABLET_PORTRAIT,
        TABLET_LANDSCAPE,
        TV
    }
    
    /**
     * Get screen type based on size and orientation
     */
    public static ScreenType getScreenType(Context context) {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        Configuration config = context.getResources().getConfiguration();
        
        // Calculate screen size in inches
        float widthInches = metrics.widthPixels / metrics.xdpi;
        float heightInches = metrics.heightPixels / metrics.ydpi;
        double screenSizeInches = Math.sqrt(Math.pow(widthInches, 2) + Math.pow(heightInches, 2));
        
        // Check if it's TV
        if (isTV(context)) {
            return ScreenType.TV;
        }
        
        // Determine if tablet (7+ inches) or phone
        boolean isTablet = screenSizeInches >= 7.0;
        boolean isLandscape = config.orientation == Configuration.ORIENTATION_LANDSCAPE;
        
        if (isTablet) {
            return isLandscape ? ScreenType.TABLET_LANDSCAPE : ScreenType.TABLET_PORTRAIT;
        } else {
            return isLandscape ? ScreenType.PHONE_LANDSCAPE : ScreenType.PHONE_PORTRAIT;
        }
    }
    
    /**
     * Check if device is Android TV
     */
    public static boolean isTV(Context context) {
        return context.getPackageManager().hasSystemFeature("android.software.leanback");
    }
    
    /**
     * Check if device is tablet
     */
    public static boolean isTablet(Context context) {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        float widthInches = metrics.widthPixels / metrics.xdpi;
        float heightInches = metrics.heightPixels / metrics.ydpi;
        double screenSizeInches = Math.sqrt(Math.pow(widthInches, 2) + Math.pow(heightInches, 2));
        return screenSizeInches >= 7.0;
    }
    
    /**
     * Get screen width in pixels
     */
    public static int getScreenWidth(Context context) {
        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display display = wm.getDefaultDisplay();
        Point size = new Point();
        display.getSize(size);
        return size.x;
    }
    
    /**
     * Get screen height in pixels
     */
    public static int getScreenHeight(Context context) {
        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display display = wm.getDefaultDisplay();
        Point size = new Point();
        display.getSize(size);
        return size.y;
    }
    
    /**
     * Convert dp to pixels
     */
    public static int dpToPx(Context context, int dp) {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        return Math.round(dp * metrics.density);
    }
    
    /**
     * Convert pixels to dp
     */
    public static int pxToDp(Context context, int px) {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        return Math.round(px / metrics.density);
    }
    
    /**
     * Get appropriate column count for RecyclerView based on screen type
     */
    public static int getColumnCount(Context context) {
        ScreenType screenType = getScreenType(context);
        
        switch (screenType) {
            case PHONE_PORTRAIT:
                return 1;
            case PHONE_LANDSCAPE:
                return 2;
            case TABLET_PORTRAIT:
                return 2;
            case TABLET_LANDSCAPE:
                return 3;
            case TV:
                return 4;
            default:
                return 1;
        }
    }
    
    /**
     * Get appropriate padding based on screen type
     */
    public static int getScreenPadding(Context context) {
        ScreenType screenType = getScreenType(context);
        
        switch (screenType) {
            case PHONE_PORTRAIT:
            case PHONE_LANDSCAPE:
                return dpToPx(context, 16);
            case TABLET_PORTRAIT:
            case TABLET_LANDSCAPE:
                return dpToPx(context, 24);
            case TV:
                return dpToPx(context, 48);
            default:
                return dpToPx(context, 16);
        }
    }
    
    /**
     * Get appropriate text size multiplier based on screen type
     */
    public static float getTextSizeMultiplier(Context context) {
        ScreenType screenType = getScreenType(context);
        
        switch (screenType) {
            case PHONE_PORTRAIT:
            case PHONE_LANDSCAPE:
                return 1.0f;
            case TABLET_PORTRAIT:
            case TABLET_LANDSCAPE:
                return 1.2f;
            case TV:
                return 1.5f;
            default:
                return 1.0f;
        }
    }
    
    /**
     * Check if device is in landscape mode
     */
    public static boolean isLandscape(Context context) {
        return context.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE;
    }
    
    /**
     * Get safe area insets for modern devices
     */
    public static int getStatusBarHeight(Context context) {
        int result = 0;
        int resourceId = context.getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            result = context.getResources().getDimensionPixelSize(resourceId);
        }
        return result;
    }
}
