package com.alrbea.androidapp.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.BackupData;
import com.alrbea.androidapp.data.model.SettingsCategory;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataSyncManager {
    private static final String TAG = "DataSyncManager";
    private Context context;
    private Gson gson;

    // Server URLs - replace with your actual server endpoints
    private static final String BASE_URL = "https://application.alrbea.com/api/";
    private static final String UPLOAD_ENDPOINT = BASE_URL + "backup/upload";
    private static final String DOWNLOAD_ENDPOINT = BASE_URL + "backup/download";
    private static final String LIST_ENDPOINT = BASE_URL + "backup/list";
    private static final String DELETE_ENDPOINT = BASE_URL + "backup/delete";

    public interface SyncCallback {
        void onSuccess(String message);
        void onError(String error);
        void onProgress(int progress);
    }

    public interface BackupListCallback {
        void onSuccess(List<BackupData> backups);
        void onError(String error);
    }

    public DataSyncManager(Context context) {
        this.context = context;
        this.gson = new Gson();
    }

    public List<SettingsCategory> getSettingsCategories() {
        List<SettingsCategory> categories = new ArrayList<>();

        // Prayer Times Settings
        categories.add(new SettingsCategory(
                "prayer_times",
                "أوقات الصلاة",
                "إعدادات أوقات الصلاة والتنبيهات",
                R.drawable.ic_prayer_times,
                "PrayerTimesSettings"
        ));

        // Azan Settings
        categories.add(new SettingsCategory(
                "azan",
                "الأذان",
                "إعدادات الأذان والأصوات",
                R.drawable.ic_volume,
                "AzanSettings"
        ));

        // Azkar Settings
        categories.add(new SettingsCategory(
                "azkar",
                "الأذكار",
                "إعدادات الأذكار والتذكيرات",
                R.drawable.ic_azkar,
                "AzkarSettings"
        ));

        // Gallery Settings
        categories.add(new SettingsCategory(
                "gallery",
                "المعرض",
                "إعدادات معرض الصور والعروض",
                R.drawable.ic_gallery,
                "GallerySettings"
        ));

        // Text Design Settings
        categories.add(new SettingsCategory(
                "text_design",
                "تصميم النصوص",
                "إعدادات تصميم النصوص والشعارات",
                R.drawable.ic_design,
                "TextDesignSettings"
        ));

        // App Settings
        categories.add(new SettingsCategory(
                "app_settings",
                "إعدادات التطبيق",
                "الإعدادات العامة للتطبيق",
                R.drawable.ic_settings,
                "AppSettings"
        ));

        // Location Settings
        categories.add(new SettingsCategory(
                "location",
                "الموقع",
                "إعدادات الموقع والمدينة",
                R.drawable.ic_location,
                "LocationSettings"
        ));

        // Notification Settings
        categories.add(new SettingsCategory(
                "notifications",
                "الإشعارات",
                "إعدادات الإشعارات والتنبيهات",
                R.drawable.ic_notifications,
                "NotificationSettings"
        ));

        // Theme Settings
        categories.add(new SettingsCategory(
                "theme",
                "المظهر",
                "إعدادات المظهر والألوان",
                R.drawable.ic_palette,
                "ThemeSettings"
        ));

        // Language Settings
        categories.add(new SettingsCategory(
                "language",
                "اللغة",
                "إعدادات اللغة والنصوص",
                R.drawable.ic_language,
                "LanguageSettings"
        ));

        // Backup Settings
        categories.add(new SettingsCategory(
                "backup",
                "النسخ الاحتياطي",
                "إعدادات النسخ الاحتياطي والاستعادة",
                R.drawable.ic_backup,
                "BackupSettings"
        ));

        // Remote Control Settings
        categories.add(new SettingsCategory(
                "remote_control",
                "التحكم عن بُعد",
                "إعدادات التحكم عن بُعد والاتصال",
                R.drawable.ic_remote,
                "RemoteControlSettings"
        ));

        // Calculate sizes for each category
        for (SettingsCategory category : categories) {
            category.setSize(calculateCategorySize(category));
        }

        return categories;
    }

    private long calculateCategorySize(SettingsCategory category) {
        try {
            SharedPreferences prefs = context.getSharedPreferences(category.getPreferencesKey(), Context.MODE_PRIVATE);
            Map<String, ?> allPrefs = prefs.getAll();
            String json = gson.toJson(allPrefs);
            return json.getBytes().length;
        } catch (Exception e) {
            Log.e(TAG, "Error calculating size for category: " + category.getId(), e);
            return 0;
        }
    }

    public BackupData createBackup(String clientId, String backupName, List<SettingsCategory> selectedCategories) {
        BackupData backup = new BackupData(clientId, backupName);
        Map<String, Object> allSettings = new HashMap<>();
        List<String> categoryIds = new ArrayList<>();
        long totalSize = 0;

        for (SettingsCategory category : selectedCategories) {
            if (category.isEnabled()) {
                try {
                    SharedPreferences prefs = context.getSharedPreferences(category.getPreferencesKey(), Context.MODE_PRIVATE);
                    Map<String, ?> categorySettings = prefs.getAll();
                    allSettings.put(category.getId(), categorySettings);
                    categoryIds.add(category.getId());
                    totalSize += category.getSize();
                } catch (Exception e) {
                    Log.e(TAG, "Error reading settings for category: " + category.getId(), e);
                }
            }
        }

        backup.setSettings(allSettings);
        backup.setCategories(categoryIds);
        backup.setSize(totalSize);
        backup.setDescription("نسخة احتياطية تحتوي على " + categoryIds.size() + " فئة إعدادات");

        return backup;
    }

    public void restoreBackup(BackupData backup, SyncCallback callback) {
        try {
            Map<String, Object> settings = backup.getSettings();
            int totalCategories = settings.size();
            int processedCategories = 0;

            for (Map.Entry<String, Object> entry : settings.entrySet()) {
                String categoryId = entry.getKey();
                Object categoryData = entry.getValue();

                // Find the category info
                SettingsCategory category = findCategoryById(categoryId);
                if (category != null) {
                    restoreCategorySettings(category, categoryData);
                }

                processedCategories++;
                int progress = (processedCategories * 100) / totalCategories;
                callback.onProgress(progress);
            }

            callback.onSuccess("تم استرجاع الإعدادات بنجاح");

        } catch (Exception e) {
            Log.e(TAG, "Error restoring backup", e);
            callback.onError("حدث خطأ في استرجاع الإعدادات: " + e.getMessage());
        }
    }

    private SettingsCategory findCategoryById(String categoryId) {
        List<SettingsCategory> categories = getSettingsCategories();
        for (SettingsCategory category : categories) {
            if (category.getId().equals(categoryId)) {
                return category;
            }
        }
        return null;
    }

    private void restoreCategorySettings(SettingsCategory category, Object data) {
        try {
            SharedPreferences prefs = context.getSharedPreferences(category.getPreferencesKey(), Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.clear(); // Clear existing settings

            if (data instanceof Map) {
                Map<String, Object> settingsMap = (Map<String, Object>) data;
                for (Map.Entry<String, Object> entry : settingsMap.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();

                    if (value instanceof String) {
                        editor.putString(key, (String) value);
                    } else if (value instanceof Boolean) {
                        editor.putBoolean(key, (Boolean) value);
                    } else if (value instanceof Integer) {
                        editor.putInt(key, (Integer) value);
                    } else if (value instanceof Long) {
                        editor.putLong(key, (Long) value);
                    } else if (value instanceof Float) {
                        editor.putFloat(key, (Float) value);
                    }
                }
            }

            editor.apply();
        } catch (Exception e) {
            Log.e(TAG, "Error restoring category settings: " + category.getId(), e);
        }
    }

    // Server communication methods (to be implemented with your actual server)
    public void uploadBackup(BackupData backup, SyncCallback callback) {
        // TODO: Implement actual server upload
        // This is a placeholder for server communication
        
        // Simulate upload progress
        new Thread(() -> {
            try {
                for (int i = 0; i <= 100; i += 10) {
                    callback.onProgress(i);
                    Thread.sleep(200);
                }
                callback.onSuccess("تم رفع النسخة الاحتياطية بنجاح");
            } catch (InterruptedException e) {
                callback.onError("تم إلغاء العملية");
            }
        }).start();
    }

    public void downloadBackupsList(String clientId, BackupListCallback callback) {
        // TODO: Implement actual server communication
        // This is a placeholder that returns sample data
        
        new Thread(() -> {
            try {
                Thread.sleep(1000); // Simulate network delay
                
                List<BackupData> sampleBackups = new ArrayList<>();
                // Add sample backup data
                BackupData backup1 = new BackupData(clientId, "نسخة احتياطية 1");
                backup1.setDescription("نسخة احتياطية كاملة");
                backup1.setSize(2048000); // 2MB
                sampleBackups.add(backup1);
                
                callback.onSuccess(sampleBackups);
            } catch (InterruptedException e) {
                callback.onError("تم إلغاء العملية");
            }
        }).start();
    }

    public void deleteBackup(String backupId, SyncCallback callback) {
        // TODO: Implement actual server communication
        new Thread(() -> {
            try {
                Thread.sleep(500);
                callback.onSuccess("تم حذف النسخة الاحتياطية");
            } catch (InterruptedException e) {
                callback.onError("تم إلغاء العملية");
            }
        }).start();
    }
}
