package com.alrbea.androidapp.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.alrbea.androidapp.data.model.TextDesign;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TextDesignManager {
    
    private static final String PREFS_NAME = "TextDesignManager";
    private static final String KEY_TEXT_DESIGNS = "text_designs";
    private static final String KEY_ACTIVE_DESIGN_ID = "active_design_id";
    private static final String KEY_TEXT_DESIGN_ENABLED = "text_design_enabled";
    
    private Context context;
    private SharedPreferences sharedPreferences;
    private Gson gson;
    
    public TextDesignManager(Context context) {
        this.context = context;
        this.sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.gson = new Gson();
    }
    
    /**
     * Save a text design to storage
     */
    public boolean saveTextDesign(TextDesign design) {
        try {
            Map<String, TextDesign> designs = getAllTextDesignsMap();
            designs.put(design.getId(), design);
            
            String designsJson = gson.toJson(designs);
            return sharedPreferences.edit()
                    .putString(KEY_TEXT_DESIGNS, designsJson)
                    .commit();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Get a text design by ID
     */
    public TextDesign getTextDesign(String designId) {
        Map<String, TextDesign> designs = getAllTextDesignsMap();
        return designs.get(designId);
    }
    
    /**
     * Get all text designs as a list
     */
    public List<TextDesign> getAllTextDesigns() {
        Map<String, TextDesign> designs = getAllTextDesignsMap();
        return new ArrayList<>(designs.values());
    }
    
    /**
     * Get all enabled text designs
     */
    public List<TextDesign> getEnabledTextDesigns() {
        List<TextDesign> allDesigns = getAllTextDesigns();
        List<TextDesign> enabledDesigns = new ArrayList<>();
        
        for (TextDesign design : allDesigns) {
            if (design.isEnabled()) {
                enabledDesigns.add(design);
            }
        }
        
        return enabledDesigns;
    }
    
    /**
     * Get text designs that should show during prayers
     */
    public List<TextDesign> getPrayerTextDesigns() {
        List<TextDesign> allDesigns = getAllTextDesigns();
        List<TextDesign> prayerDesigns = new ArrayList<>();
        
        for (TextDesign design : allDesigns) {
            if (design.isEnabled() && design.isShowInPrayers()) {
                prayerDesigns.add(design);
            }
        }
        
        return prayerDesigns;
    }
    
    /**
     * Delete a text design
     */
    public boolean deleteTextDesign(String designId) {
        try {
            Map<String, TextDesign> designs = getAllTextDesignsMap();
            TextDesign removedDesign = designs.remove(designId);
            
            if (removedDesign != null) {
                String designsJson = gson.toJson(designs);
                
                // If this was the active design, clear it
                if (designId.equals(getActiveDesignId())) {
                    setActiveDesignId(null);
                }
                
                return sharedPreferences.edit()
                        .putString(KEY_TEXT_DESIGNS, designsJson)
                        .commit();
            }
            
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Update text design enabled state
     */
    public boolean updateTextDesignEnabledState(String designId, boolean enabled) {
        TextDesign design = getTextDesign(designId);
        if (design != null) {
            design.setEnabled(enabled);
            return saveTextDesign(design);
        }
        return false;
    }
    
    /**
     * Get the currently active text design
     */
    public TextDesign getActiveTextDesign() {
        String activeId = getActiveDesignId();
        if (activeId != null) {
            return getTextDesign(activeId);
        }
        return null;
    }
    
    /**
     * Set the active text design
     */
    public void setActiveDesignId(String designId) {
        sharedPreferences.edit()
                .putString(KEY_ACTIVE_DESIGN_ID, designId)
                .apply();
    }
    
    /**
     * Get the active design ID
     */
    public String getActiveDesignId() {
        return sharedPreferences.getString(KEY_ACTIVE_DESIGN_ID, null);
    }
    
    /**
     * Check if text design feature is globally enabled
     */
    public boolean isTextDesignFeatureEnabled() {
        return sharedPreferences.getBoolean(KEY_TEXT_DESIGN_ENABLED, true);
    }
    
    /**
     * Set text design feature enabled state
     */
    public void setTextDesignFeatureEnabled(boolean enabled) {
        sharedPreferences.edit()
                .putBoolean(KEY_TEXT_DESIGN_ENABLED, enabled)
                .apply();
    }
    
    /**
     * Get text design count
     */
    public int getTextDesignCount() {
        return getAllTextDesigns().size();
    }
    
    /**
     * Get enabled text design count
     */
    public int getEnabledTextDesignCount() {
        return getEnabledTextDesigns().size();
    }
    
    /**
     * Check if design name already exists
     */
    public boolean isDesignNameExists(String name, String excludeId) {
        List<TextDesign> designs = getAllTextDesigns();
        for (TextDesign design : designs) {
            if (design.getName().equals(name) && !design.getId().equals(excludeId)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Create a copy of an existing design
     */
    public TextDesign duplicateTextDesign(String designId) {
        TextDesign original = getTextDesign(designId);
        if (original != null) {
            TextDesign copy = original.copy();
            
            // Ensure unique name
            String baseName = copy.getName();
            int counter = 1;
            while (isDesignNameExists(copy.getName(), copy.getId())) {
                copy.setName(baseName + " (" + counter + ")");
                counter++;
            }
            
            if (saveTextDesign(copy)) {
                return copy;
            }
        }
        return null;
    }
    
    /**
     * Clear all text designs (for testing or reset)
     */
    public boolean clearAllTextDesigns() {
        try {
            return sharedPreferences.edit()
                    .remove(KEY_TEXT_DESIGNS)
                    .remove(KEY_ACTIVE_DESIGN_ID)
                    .commit();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Get all text designs as a map for internal use
     */
    private Map<String, TextDesign> getAllTextDesignsMap() {
        try {
            String designsJson = sharedPreferences.getString(KEY_TEXT_DESIGNS, "{}");
            Type type = new TypeToken<Map<String, TextDesign>>(){}.getType();
            Map<String, TextDesign> designs = gson.fromJson(designsJson, type);
            return designs != null ? designs : new HashMap<>();
        } catch (Exception e) {
            e.printStackTrace();
            return new HashMap<>();
        }
    }
    
    /**
     * Export text designs data (for backup)
     */
    public String exportTextDesigns() {
        return sharedPreferences.getString(KEY_TEXT_DESIGNS, "{}");
    }
    
    /**
     * Import text designs data (for restore)
     */
    public boolean importTextDesigns(String designsJson) {
        try {
            // Validate JSON first
            Type type = new TypeToken<Map<String, TextDesign>>(){}.getType();
            Map<String, TextDesign> designs = gson.fromJson(designsJson, type);
            
            if (designs != null) {
                return sharedPreferences.edit()
                        .putString(KEY_TEXT_DESIGNS, designsJson)
                        .commit();
            }
            
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Get designs sorted by creation date (newest first)
     */
    public List<TextDesign> getTextDesignsSortedByDate() {
        List<TextDesign> designs = getAllTextDesigns();
        designs.sort((d1, d2) -> Long.compare(d2.getCreatedAt(), d1.getCreatedAt()));
        return designs;
    }
    
    /**
     * Get designs sorted by name
     */
    public List<TextDesign> getTextDesignsSortedByName() {
        List<TextDesign> designs = getAllTextDesigns();
        designs.sort((d1, d2) -> d1.getName().compareToIgnoreCase(d2.getName()));
        return designs;
    }
}
