package com.alrbea.androidapp.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.alrbea.androidapp.data.model.Event;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class EventsManager {
    private static final String PREFS_NAME = "events_data";
    private static final String KEY_EVENTS = "events_list";
    private static final String KEY_EVENTS_ENABLED = "events_enabled";
    
    private SharedPreferences sharedPreferences;
    private Gson gson;
    
    public EventsManager(Context context) {
        sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        gson = new Gson();
    }
    
    public List<Event> getAllEvents() {
        String eventsJson = sharedPreferences.getString(KEY_EVENTS, "[]");
        Type listType = new TypeToken<List<Event>>(){}.getType();
        List<Event> events = gson.fromJson(eventsJson, listType);
        return events != null ? events : new ArrayList<>();
    }
    
    public void saveEvents(List<Event> events) {
        String eventsJson = gson.toJson(events);
        sharedPreferences.edit()
                .putString(KEY_EVENTS, eventsJson)
                .apply();
    }
    
    public void addEvent(Event event) {
        List<Event> events = getAllEvents();
        events.add(event);
        saveEvents(events);
    }
    
    public void updateEvent(Event updatedEvent) {
        List<Event> events = getAllEvents();
        for (int i = 0; i < events.size(); i++) {
            if (events.get(i).getId().equals(updatedEvent.getId())) {
                events.set(i, updatedEvent);
                break;
            }
        }
        saveEvents(events);
    }
    
    public void deleteEvent(String eventId) {
        List<Event> events = getAllEvents();
        events.removeIf(event -> event.getId().equals(eventId));
        saveEvents(events);
    }
    
    public Event getEventById(String eventId) {
        List<Event> events = getAllEvents();
        for (Event event : events) {
            if (event.getId().equals(eventId)) {
                return event;
            }
        }
        return null;
    }
    
    public List<Event> getEnabledEvents() {
        List<Event> allEvents = getAllEvents();
        List<Event> enabledEvents = new ArrayList<>();
        for (Event event : allEvents) {
            if (event.isEnabled()) {
                enabledEvents.add(event);
            }
        }
        return enabledEvents;
    }
    
    public boolean isEventsFeatureEnabled() {
        return sharedPreferences.getBoolean(KEY_EVENTS_ENABLED, false);
    }
    
    public void setEventsFeatureEnabled(boolean enabled) {
        sharedPreferences.edit()
                .putBoolean(KEY_EVENTS_ENABLED, enabled)
                .apply();
    }
    
    public void toggleEventEnabled(String eventId, boolean enabled) {
        Event event = getEventById(eventId);
        if (event != null) {
            event.setEnabled(enabled);
            updateEvent(event);
        }
    }
    
    public int getEventsCount() {
        return getAllEvents().size();
    }
    
    public int getEnabledEventsCount() {
        return getEnabledEvents().size();
    }
    
    public void clearAllEvents() {
        sharedPreferences.edit()
                .remove(KEY_EVENTS)
                .apply();
    }
}
