package com.alrbea.androidapp.utils;

import android.app.AlertDialog;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.GridView;

import androidx.cardview.widget.CardView;

import java.util.ArrayList;
import java.util.List;

public class ColorPickerDialog {
    
    public interface OnColorSelectedListener {
        void onColorSelected(int color);
    }
    
    private Context context;
    private AlertDialog dialog;
    
    // Predefined colors
    private static final int[] COLORS = {
        Color.BLACK,
        Color.WHITE,
        Color.RED,
        Color.GREEN,
        Color.BLUE,
        Color.YELLOW,
        Color.CYAN,
        Color.MAGENTA,
        Color.GRAY,
        Color.LTGRAY,
        Color.DKGRAY,
        Color.parseColor("#FF5722"), // Deep Orange
        Color.parseColor("#E91E63"), // Pink
        Color.parseColor("#9C27B0"), // Purple
        Color.parseColor("#673AB7"), // Deep Purple
        Color.parseColor("#3F51B5"), // Indigo
        Color.parseColor("#2196F3"), // Blue
        Color.parseColor("#03A9F4"), // Light Blue
        Color.parseColor("#00BCD4"), // Cyan
        Color.parseColor("#009688"), // Teal
        Color.parseColor("#4CAF50"), // Green
        Color.parseColor("#8BC34A"), // Light Green
        Color.parseColor("#CDDC39"), // Lime
        Color.parseColor("#FFEB3B"), // Yellow
        Color.parseColor("#FFC107"), // Amber
        Color.parseColor("#FF9800"), // Orange
        Color.parseColor("#795548"), // Brown
        Color.parseColor("#607D8B"), // Blue Grey
        Color.parseColor("#F44336"), // Red
        Color.parseColor("#E53935"), // Red 600
        Color.parseColor("#D32F2F"), // Red 700
        Color.parseColor("#C62828"), // Red 800
        Color.parseColor("#B71C1C"), // Red 900
        Color.parseColor("#1976D2"), // Blue 700
        Color.parseColor("#1565C0"), // Blue 800
        Color.parseColor("#0D47A1"), // Blue 900
        Color.parseColor("#388E3C"), // Green 700
        Color.parseColor("#2E7D32"), // Green 800
        Color.parseColor("#1B5E20"), // Green 900
        Color.parseColor("#F57F17"), // Yellow 800
        Color.parseColor("#E65100"), // Orange 900
        Color.parseColor("#BF360C"), // Deep Orange 900
        Color.parseColor("#3E2723"), // Brown 900
        Color.parseColor("#263238"), // Blue Grey 900
        Color.TRANSPARENT
    };
    
    public ColorPickerDialog(Context context) {
        this.context = context;
    }
    
    public void show(int currentColor, OnColorSelectedListener listener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle("اختر اللون");

        // Create a simple grid layout programmatically
        GridView gridView = new GridView(context);
        gridView.setNumColumns(6);
        gridView.setHorizontalSpacing(16);
        gridView.setVerticalSpacing(16);
        gridView.setPadding(32, 32, 32, 32);

        ColorAdapter adapter = new ColorAdapter(currentColor, color -> {
            if (listener != null) {
                listener.onColorSelected(color);
            }
            if (dialog != null) {
                dialog.dismiss();
            }
        });

        gridView.setAdapter(adapter);

        builder.setView(gridView);
        builder.setNegativeButton("إلغاء", null);

        dialog = builder.create();
        dialog.show();
    }
    
    private static class ColorAdapter extends BaseAdapter {
        private int selectedColor;
        private OnColorSelectedListener listener;
        
        public ColorAdapter(int selectedColor, OnColorSelectedListener listener) {
            this.selectedColor = selectedColor;
            this.listener = listener;
        }
        
        @Override
        public int getCount() {
            return COLORS.length;
        }
        
        @Override
        public Object getItem(int position) {
            return COLORS[position];
        }
        
        @Override
        public long getItemId(int position) {
            return position;
        }
        
        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            CardView colorCard;

            if (convertView == null) {
                // Create CardView programmatically
                colorCard = new CardView(parent.getContext());
                colorCard.setLayoutParams(new ViewGroup.LayoutParams(120, 120));
                colorCard.setRadius(60f);
                colorCard.setCardElevation(8f);
                colorCard.setUseCompatPadding(true);

                // Create color indicator view
                View colorIndicator = new View(parent.getContext());
                colorIndicator.setLayoutParams(new ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT));

                colorCard.addView(colorIndicator);
                colorCard.setTag(colorIndicator);
            } else {
                colorCard = (CardView) convertView;
            }

            View colorIndicator = (View) colorCard.getTag();
            int color = COLORS[position];

            // Set color
            if (color == Color.TRANSPARENT) {
                // Show gray pattern for transparent
                colorIndicator.setBackgroundColor(Color.LTGRAY);
            } else {
                colorIndicator.setBackgroundColor(color);
            }

            // Show selection with border
            if (color == selectedColor) {
//                colorCard.setStrokeColor(Color.BLACK);
//                colorCard.setStrokeWidth(6);
            } else {
//                colorCard.setStrokeWidth(0);
            }

            // Set click listener
            colorCard.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onColorSelected(color);
                }
            });

            return colorCard;
        }
    }
}
