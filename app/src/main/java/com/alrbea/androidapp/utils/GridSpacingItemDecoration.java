package com.alrbea.androidapp.utils;

import android.graphics.Rect;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.GridLayoutManager;

/**
 * ItemDecoration for creating equal spacing in grid layouts
 * Ensures perfect square spacing for grid items
 */
public class GridSpacingItemDecoration extends RecyclerView.ItemDecoration {

    private final int spanCount;
    private final int spacing;
    private final boolean includeEdge;

    public GridSpacingItemDecoration(int spanCount, int spacing, boolean includeEdge) {
        this.spanCount = spanCount;
        this.spacing = spacing;
        this.includeEdge = includeEdge;
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view,
                              @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        
        int position = parent.getChildAdapterPosition(view);
        int column = position % spanCount;

        if (includeEdge) {
            // Include edge spacing
            outRect.left = spacing - column * spacing / spanCount;
            outRect.right = (column + 1) * spacing / spanCount;

            if (position < spanCount) {
                outRect.top = spacing;
            }
            outRect.bottom = spacing;
        } else {
            // No edge spacing
            outRect.left = column * spacing / spanCount;
            outRect.right = spacing - (column + 1) * spacing / spanCount;
            
            if (position >= spanCount) {
                outRect.top = spacing;
            }
        }
    }

    /**
     * Create spacing decoration based on screen type
     */
    public static GridSpacingItemDecoration createForScreen(android.content.Context context, int spanCount) {
        ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(context);
        
        int spacing;
        boolean includeEdge = true;
        
        switch (screenType) {
            case PHONE_PORTRAIT:
            case PHONE_LANDSCAPE:
                spacing = ScreenUtils.dpToPx(context, 12); // Optimized for responsive grid with min 2 columns
                break;
            case TABLET_PORTRAIT:
            case TABLET_LANDSCAPE:
                spacing = ScreenUtils.dpToPx(context, 16); // Optimized for responsive grid with min 2 columns
                break;
            case TV:
                spacing = ScreenUtils.dpToPx(context, 20); // Optimized for responsive grid with min 2 columns
                break;
            default:
                spacing = ScreenUtils.dpToPx(context, 12);
                break;
        }
        
        return new GridSpacingItemDecoration(spanCount, spacing, includeEdge);
    }
}
