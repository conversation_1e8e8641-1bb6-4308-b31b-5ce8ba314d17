package com.alrbea.androidapp.utils;

import android.content.Context;
import com.alrbea.androidapp.data.model.PrayerTimes;
import com.alrbea.androidapp.data.model.PrayerTimeInfo;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

public class PrayerTimeCalculator {
    
    private static final int IQAMA_DELAY_MINUTES = 10; // Default iqama delay
    private static final String[] PRAYER_NAMES = {"الفجر", "الظهر", "العصر", "المغرب", "العشاء"};
    private static final String[] PRAYER_NAMES_EN = {"Fajr", "Dhuhr", "Asr", "Maghrib", "Isha"};
    
    public static List<PrayerTimeInfo> calculatePrayerTimesInfo(PrayerTimes prayerTimes, Context context) {
        List<PrayerTimeInfo> prayerTimeInfoList = new ArrayList<>();
        
        if (prayerTimes == null) {
            return prayerTimeInfoList;
        }
        
        Calendar now = Calendar.getInstance();
        String currentTime = getCurrentTimeString();
        String gregorianDate = getGregorianDateString();
        String hijriDate = getHijriDateString();
        String dayOfWeek = getDayOfWeekString();
        
        // Get prayer times
        String[] prayerTimesArray = {
            prayerTimes.getFajr(),
            prayerTimes.getDhuhr(),
            prayerTimes.getAsr(),
            prayerTimes.getMaghrib(),
            prayerTimes.getIsha()
        };
        
        PrayerTimeInfo nextPrayer = null;
        int minTimeUntilPrayer = Integer.MAX_VALUE;
        
        for (int i = 0; i < PRAYER_NAMES.length; i++) {
            PrayerTimeInfo prayerTimeInfo = new PrayerTimeInfo();
            prayerTimeInfo.setPrayerName(PRAYER_NAMES[i]);
            prayerTimeInfo.setPrayerTime(prayerTimesArray[i]);
            prayerTimeInfo.setCurrentTime(currentTime);
            prayerTimeInfo.setGregorianDate(now.getTime());
            prayerTimeInfo.setHijriDate(hijriDate);
            prayerTimeInfo.setDayOfWeek(dayOfWeek);
            prayerTimeInfo.setTimeZone(TimeZone.getDefault().getID());
            
            // Calculate iqama time
            String iqamaTime = calculateIqamaTime(prayerTimesArray[i]);
            prayerTimeInfo.setIqamaTime(iqamaTime);
            
            // Calculate time until prayer
            int timeUntilPrayer = calculateTimeUntil(prayerTimesArray[i]);
            prayerTimeInfo.setTimeUntilPrayer(timeUntilPrayer);
            
            // Calculate time until iqama
            int timeUntilIqama = calculateTimeUntil(iqamaTime);
            prayerTimeInfo.setTimeUntilIqama(timeUntilIqama);
            
            // Set azkar
            prayerTimeInfo.setAzkarAfterPrayer(getAzkarAfterPrayer(PRAYER_NAMES[i]));
            prayerTimeInfo.setAzkarAtAdhan(getAzkarAtAdhan());
            
            // Check if this is the next prayer
            if (timeUntilPrayer > 0 && timeUntilPrayer < minTimeUntilPrayer) {
                minTimeUntilPrayer = timeUntilPrayer;
                nextPrayer = prayerTimeInfo;
            }
            
            // Check if this is the current prayer (within iqama time)
            if (timeUntilPrayer <= 0 && timeUntilIqama > 0) {
                prayerTimeInfo.setCurrentPrayer(true);
            }
            
            prayerTimeInfoList.add(prayerTimeInfo);
        }
        
        // Set next prayer
        if (nextPrayer != null) {
            nextPrayer.setNextPrayer(true);
        }
        
        return prayerTimeInfoList;
    }
    
    private static String calculateIqamaTime(String prayerTime) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm", Locale.getDefault());
            Date prayerDate = sdf.parse(prayerTime);
            
            Calendar cal = Calendar.getInstance();
            cal.setTime(prayerDate);
            cal.add(Calendar.MINUTE, IQAMA_DELAY_MINUTES);
            
            return sdf.format(cal.getTime());
        } catch (Exception e) {
            return prayerTime; // Return original time if parsing fails
        }
    }
    
    private static int calculateTimeUntil(String timeString) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm", Locale.getDefault());
            Date targetTime = sdf.parse(timeString);
            
            Calendar now = Calendar.getInstance();
            Calendar target = Calendar.getInstance();
            target.setTime(targetTime);
            
            // Set target to today
            target.set(Calendar.YEAR, now.get(Calendar.YEAR));
            target.set(Calendar.MONTH, now.get(Calendar.MONTH));
            target.set(Calendar.DAY_OF_MONTH, now.get(Calendar.DAY_OF_MONTH));
            
            // If target time has passed today, set it to tomorrow
            if (target.getTimeInMillis() <= now.getTimeInMillis()) {
                target.add(Calendar.DAY_OF_MONTH, 1);
            }
            
            long diffInMillis = target.getTimeInMillis() - now.getTimeInMillis();
            return (int) (diffInMillis / 1000); // Convert to seconds
            
        } catch (Exception e) {
            return -1; // Error
        }
    }
    
    private static String getCurrentTimeString() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());
        return sdf.format(new Date());
    }
    
    private static String getGregorianDateString() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
        return sdf.format(new Date());
    }
    
    private static String getHijriDateString() {
        return HijriCalendarConverter.getCurrentHijriDateArabicString();
    }
    
    private static String getDayOfWeekString() {
        SimpleDateFormat sdf = new SimpleDateFormat("EEEE", new Locale("ar"));
        return sdf.format(new Date());
    }
    
    private static String getAzkarAfterPrayer(String prayerName) {
        switch (prayerName) {
            case "الفجر":
                return "سبحان الله (٣٣ مرة)\nالحمد لله (٣٣ مرة)\nالله أكبر (٣٣ مرة)\nلا إله إلا الله وحده لا شريك له";
            case "الظهر":
                return "سبحان الله (٣٣ مرة)\nالحمد لله (٣٣ مرة)\nالله أكبر (٣٣ مرة)\nلا إله إلا الله وحده لا شريك له";
            case "العصر":
                return "سبحان الله (٣٣ مرة)\nالحمد لله (٣٣ مرة)\nالله أكبر (٣٣ مرة)\nلا إله إلا الله وحده لا شريك له";
            case "المغرب":
                return "سبحان الله (٣٣ مرة)\nالحمد لله (٣٣ مرة)\nالله أكبر (٣٣ مرة)\nلا إله إلا الله وحده لا شريك له";
            case "العشاء":
                return "سبحان الله (٣٣ مرة)\nالحمد لله (٣٣ مرة)\nالله أكبر (٣٣ مرة)\nلا إله إلا الله وحده لا شريك له";
            default:
                return "سبحان الله (٣٣ مرة)\nالحمد لله (٣٣ مرة)\nالله أكبر (٣٣ مرة)";
        }
    }
    
    private static String getAzkarAtAdhan() {
        return "اللَّهُمَّ صَلِّ وَسَلِّمْ عَلَى مُحَمَّدٍ\n" +
               "اللَّهُمَّ إِنِّي أَسْأَلُكَ الْجَنَّةَ وَأَعُوذُ بِكَ مِنَ النَّارِ\n" +
               "اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَفْوَ وَالْعَافِيَةَ فِي الدُّنْيَا وَالْآخِرَةِ";
    }
    
    public static PrayerTimeInfo getNextPrayer(List<PrayerTimeInfo> prayerTimeInfoList) {
        for (PrayerTimeInfo prayerTimeInfo : prayerTimeInfoList) {
            if (prayerTimeInfo.isNextPrayer()) {
                return prayerTimeInfo;
            }
        }
        return null;
    }
    
    public static PrayerTimeInfo getCurrentPrayer(List<PrayerTimeInfo> prayerTimeInfoList) {
        for (PrayerTimeInfo prayerTimeInfo : prayerTimeInfoList) {
            if (prayerTimeInfo.isCurrentPrayer()) {
                return prayerTimeInfo;
            }
        }
        return null;
    }
} 