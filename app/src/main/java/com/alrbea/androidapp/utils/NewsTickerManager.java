package com.alrbea.androidapp.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.widget.TextView;
import java.util.ArrayList;
import java.util.List;

public class NewsTickerManager {
    
    private static final String PREFS_NAME = "NewsTickerPrefs";
    private static final String KEY_NEWS_ENABLED = "news_enabled";
    private static final String KEY_NEWS_TEXT = "news_text";
    
    private Context context;
    private TextView newsTickerView;
    private Handler handler;
    private Runnable tickerRunnable;
    private boolean isRunning = false;
    private int currentIndex = 0;
    private List<String> newsItems;
    
    public NewsTickerManager(Context context) {
        this.context = context;
        this.handler = new Handler(Looper.getMainLooper());
        this.newsItems = new ArrayList<>();
        loadNewsItems();
    }
    public void setNewsTextView(TextView newsTickerView) {
        this.newsTickerView = newsTickerView;
    }

    private void loadNewsItems() {
        // Load news items from SharedPreferences or default values
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        String savedNews = prefs.getString(KEY_NEWS_TEXT, "");
        
        if (!savedNews.isEmpty()) {
            String[] items = savedNews.split("\\|");
            for (String item : items) {
                if (!item.trim().isEmpty()) {
                    newsItems.add(item.trim());
                }
            }
        }
        
        // Add default news items if none are saved
        if (newsItems.isEmpty()) {
            newsItems.add("مرحباً بكم في تطبيق مواقيت الصلاة");
            newsItems.add("لا تنسى ذكر الله في كل وقت");
            newsItems.add("الصلاة عماد الدين");
            newsItems.add("اللهم صل على محمد وعلى آل محمد");
        }
    }
    
    public void startTicker() {
        if (isNewsEnabled() && !isRunning && !newsItems.isEmpty()) {
            isRunning = true;
            showNextNewsItem();
        }
    }
    
    public void stopTicker() {
        isRunning = false;
        if (handler != null && tickerRunnable != null) {
            handler.removeCallbacks(tickerRunnable);
        }
    }
    
    private void showNextNewsItem() {
        if (!isRunning || newsItems.isEmpty()) {
            return;
        }
        
        String currentNews = newsItems.get(currentIndex);
        newsTickerView.setText(currentNews);
        
        // Animate the text (scroll from right to left)
        animateTextScroll();
        
        // Schedule next news item
        tickerRunnable = new Runnable() {
            @Override
            public void run() {
                currentIndex = (currentIndex + 1) % newsItems.size();
                showNextNewsItem();
            }
        };
        
        // Show each news item for 5 seconds
        handler.postDelayed(tickerRunnable, 5000);
    }
    
    private void animateTextScroll() {
        // Simple text animation - you can enhance this with more sophisticated animations
        newsTickerView.setAlpha(0f);
        newsTickerView.animate()
            .alpha(1f)
            .setDuration(500)
            .start();
    }
    
    public void setNewsEnabled(boolean enabled) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit().putBoolean(KEY_NEWS_ENABLED, enabled).apply();
        
        if (enabled) {
            startTicker();
        } else {
            stopTicker();
            newsTickerView.setVisibility(android.view.View.GONE);
        }
    }
    
    public boolean isNewsEnabled() {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getBoolean(KEY_NEWS_ENABLED, true);
    }
    
    public void setNewsText(String newsText) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit().putString(KEY_NEWS_TEXT, newsText).apply();
        
        // Reload news items
        newsItems.clear();
        loadNewsItems();
        
        // Restart ticker if it's running
        if (isRunning) {
            stopTicker();
            startTicker();
        }
    }
    
    public void addNewsItem(String newsItem) {
        if (!newsItem.trim().isEmpty()) {
            newsItems.add(newsItem.trim());
            
            // Save to SharedPreferences
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < newsItems.size(); i++) {
                if (i > 0) sb.append("|");
                sb.append(newsItems.get(i));
            }
            setNewsText(sb.toString());
        }
    }
    
    public void removeNewsItem(int index) {
        if (index >= 0 && index < newsItems.size()) {
            newsItems.remove(index);
            
            // Save to SharedPreferences
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < newsItems.size(); i++) {
                if (i > 0) sb.append("|");
                sb.append(newsItems.get(i));
            }
            setNewsText(sb.toString());
        }
    }
    
    public List<String> getNewsItems() {
        return new ArrayList<>(newsItems);
    }
    
    public void clearNewsItems() {
        newsItems.clear();
        setNewsText("");
    }
    
    public void onResume() {
        if (isNewsEnabled()) {
            startTicker();
        }
    }
    
    public void onPause() {
        stopTicker();
    }
    
    public void onDestroy() {
        stopTicker();
    }
} 