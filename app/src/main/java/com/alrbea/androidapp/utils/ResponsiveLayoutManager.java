package com.alrbea.androidapp.utils;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

public class ResponsiveLayoutManager {
    
    /**
     * Create appropriate LayoutManager based on screen type
     */
    public static RecyclerView.LayoutManager createLayoutManager(Context context, LayoutType layoutType) {
        ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(context);
        
        switch (layoutType) {
            case PRAYER_TIMES_LIST:
                return createPrayerTimesLayoutManager(context, screenType);
            case PRAYER_TIMES_GRID:
                return createPrayerTimesGridManager(context, screenType);
            case SETTINGS_LIST:
                return createSettingsLayoutManager(context, screenType);
            default:
                return new LinearLayoutManager(context);
        }
    }
    
    private static RecyclerView.LayoutManager createPrayerTimesLayoutManager(Context context, ScreenUtils.ScreenType screenType) {
        switch (screenType) {
            case PHONE_PORTRAIT:
                return new LinearLayoutManager(context);
            case PHONE_LANDSCAPE:
                return new GridLayoutManager(context, 2);
            case TABLET_PORTRAIT:
                return new GridLayoutManager(context, 2);
            case TABLET_LANDSCAPE:
                return new GridLayoutManager(context, 3);
            case TV:
                return new GridLayoutManager(context, 4);
            default:
                return new LinearLayoutManager(context);
        }
    }
    
    private static RecyclerView.LayoutManager createPrayerTimesGridManager(Context context, ScreenUtils.ScreenType screenType) {
        switch (screenType) {
            case PHONE_PORTRAIT:
                return new StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
            case PHONE_LANDSCAPE:
                return new StaggeredGridLayoutManager(3, StaggeredGridLayoutManager.VERTICAL);
            case TABLET_PORTRAIT:
                return new StaggeredGridLayoutManager(3, StaggeredGridLayoutManager.VERTICAL);
            case TABLET_LANDSCAPE:
                return new StaggeredGridLayoutManager(4, StaggeredGridLayoutManager.VERTICAL);
            case TV:
                return new StaggeredGridLayoutManager(5, StaggeredGridLayoutManager.VERTICAL);
            default:
                return new StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
        }
    }
    
    private static RecyclerView.LayoutManager createSettingsLayoutManager(Context context, ScreenUtils.ScreenType screenType) {
        // Settings always use linear layout but with different spacing
        return new LinearLayoutManager(context);
    }
    
    /**
     * Apply responsive margins and padding to a view
     */
    public static void applyResponsiveSpacing(Context context, View view) {
        ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(context);
        
        int padding = getResponsivePadding(context, screenType);
        int margin = getResponsiveMargin(context, screenType);
        
        view.setPadding(padding, padding, padding, padding);
        
        if (view.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
            params.setMargins(margin, margin, margin, margin);
            view.setLayoutParams(params);
        }
    }
    
    /**
     * Get responsive padding based on screen type
     */
    public static int getResponsivePadding(Context context, ScreenUtils.ScreenType screenType) {
        switch (screenType) {
            case PHONE_PORTRAIT:
            case PHONE_LANDSCAPE:
                return ScreenUtils.dpToPx(context, 16);
            case TABLET_PORTRAIT:
            case TABLET_LANDSCAPE:
                return ScreenUtils.dpToPx(context, 24);
            case TV:
                return ScreenUtils.dpToPx(context, 48);
            default:
                return ScreenUtils.dpToPx(context, 16);
        }
    }
    
    /**
     * Get responsive margin based on screen type
     */
    public static int getResponsiveMargin(Context context, ScreenUtils.ScreenType screenType) {
        switch (screenType) {
            case PHONE_PORTRAIT:
            case PHONE_LANDSCAPE:
                return ScreenUtils.dpToPx(context, 8);
            case TABLET_PORTRAIT:
            case TABLET_LANDSCAPE:
                return ScreenUtils.dpToPx(context, 12);
            case TV:
                return ScreenUtils.dpToPx(context, 24);
            default:
                return ScreenUtils.dpToPx(context, 8);
        }
    }
    
    /**
     * Get responsive text size based on screen type
     */
    public static float getResponsiveTextSize(Context context, float baseSize) {
        float multiplier = ScreenUtils.getTextSizeMultiplier(context);
        return baseSize * multiplier;
    }
    
    /**
     * Apply responsive text size to a view
     */
    public static void applyResponsiveTextSize(Context context, android.widget.TextView textView, float baseSize) {
        float responsiveSize = getResponsiveTextSize(context, baseSize);
        textView.setTextSize(responsiveSize);
    }
    
    /**
     * Get appropriate item decoration spacing
     */
    public static int getItemDecorationSpacing(Context context) {
        ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(context);
        
        switch (screenType) {
            case PHONE_PORTRAIT:
            case PHONE_LANDSCAPE:
                return ScreenUtils.dpToPx(context, 8);
            case TABLET_PORTRAIT:
            case TABLET_LANDSCAPE:
                return ScreenUtils.dpToPx(context, 12);
            case TV:
                return ScreenUtils.dpToPx(context, 16);
            default:
                return ScreenUtils.dpToPx(context, 8);
        }
    }
    
    public enum LayoutType {
        PRAYER_TIMES_LIST,
        PRAYER_TIMES_GRID,
        SETTINGS_LIST
    }
}
