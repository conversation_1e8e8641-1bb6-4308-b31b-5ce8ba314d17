package com.alrbea.androidapp.utils;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.media.AudioAttributes;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import com.alrbea.androidapp.MainActivity;
import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.PrayerTimeInfo;

public class AdhanNotificationManager {
    
    private static final String CHANNEL_ID = "adhan_channel";
    private static final String CHANNEL_NAME = "Adhan Notifications";
    private static final String CHANNEL_DESCRIPTION = "Notifications for prayer times";
    
    private Context context;
    private NotificationManagerCompat notificationManager;
    
    public AdhanNotificationManager(Context context) {
        this.context = context;
        this.notificationManager = NotificationManagerCompat.from(context);
        createNotificationChannel();
    }
    
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            );
            channel.setDescription(CHANNEL_DESCRIPTION);
            channel.enableVibration(true);
            channel.setVibrationPattern(new long[]{0, 1000, 500, 1000, 500, 1000});
            
            // Set custom sound for adhan
            Uri soundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
            AudioAttributes audioAttributes = new AudioAttributes.Builder()
                .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                .setUsage(AudioAttributes.USAGE_NOTIFICATION_EVENT)
                .build();
            channel.setSound(soundUri, audioAttributes);
            
            notificationManager.createNotificationChannel(channel);
        }
    }
    
    public void showAdhanNotification(PrayerTimeInfo prayerTimeInfo) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
//        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
//            .setSmallIcon(R.drawable.ic_mosque)
//            .setContentTitle("حان وقت " + prayerTimeInfo.getPrayerName())
//            .setContentText("حان الآن موعد صلاة " + prayerTimeInfo.getPrayerName())
//            .setStyle(new NotificationCompat.BigTextStyle()
//                .bigText("حان الآن موعد صلاة " + prayerTimeInfo.getPrayerName() +
//                        "\n" + prayerTimeInfo.getAzkarAtAdhan()))
//            .setPriority(NotificationCompat.PRIORITY_HIGH)
//            .setAutoCancel(true)
//            .setContentIntent(pendingIntent)
//            .setVibrate(new long[]{0, 1000, 500, 1000, 500, 1000})
//            .setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION));
//
//        notificationManager.notify(prayerTimeInfo.getPrayerName().hashCode(), builder.build());
    }
    
    public void showIqamaNotification(PrayerTimeInfo prayerTimeInfo) {
//        Intent intent = new Intent(context, MainActivity.class);
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
//
//        PendingIntent pendingIntent = PendingIntent.getActivity(
//            context,
//            0,
//            intent,
//            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
//        );
//
//        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
//            .setSmallIcon(R.drawable.ic_iqama)
//            .setContentTitle("حان وقت الإقامة")
//            .setContentText("حان الآن موعد إقامة صلاة " + prayerTimeInfo.getPrayerName())
//            .setStyle(new NotificationCompat.BigTextStyle()
//                .bigText("حان الآن موعد إقامة صلاة " + prayerTimeInfo.getPrayerName() +
//                        "\n" + prayerTimeInfo.getAzkarAfterPrayer()))
//            .setPriority(NotificationCompat.PRIORITY_HIGH)
//            .setAutoCancel(true)
//            .setContentIntent(pendingIntent)
//            .setVibrate(new long[]{0, 1000, 500, 1000, 500, 1000})
//            .setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION));
//
//        notificationManager.notify((prayerTimeInfo.getPrayerName() + "_iqama").hashCode(), builder.build());
    }
    
    public void showNextPrayerNotification(PrayerTimeInfo prayerTimeInfo) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_prayer)
            .setContentTitle("الصلاة القادمة: " + prayerTimeInfo.getPrayerName())
            .setContentText("متبقي " + prayerTimeInfo.getFormattedTimeUntilPrayer() + " على صلاة " + prayerTimeInfo.getPrayerName())
            .setStyle(new NotificationCompat.BigTextStyle()
                .bigText("متبقي " + prayerTimeInfo.getFormattedTimeUntilPrayer() + " على صلاة " + prayerTimeInfo.getPrayerName() +
                        "\nوقت الصلاة: " + prayerTimeInfo.getPrayerTime() +
                        "\nوقت الإقامة: " + prayerTimeInfo.getIqamaTime()))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent);
        
//        notificationManager.notify((prayerTimeInfo.getPrayerName() + "_next").hashCode(), builder.build());
    }
    
    public void cancelAllNotifications() {
        notificationManager.cancelAll();
    }
} 