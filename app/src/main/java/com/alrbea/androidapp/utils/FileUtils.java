package com.alrbea.androidapp.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Environment;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

public class FileUtils {
    
    private static final String GALLERY_FOLDER = "galleries";
    private static final int MAX_IMAGE_SIZE = 1024; // Max width/height in pixels
    private static final int JPEG_QUALITY = 85; // JPEG compression quality
    
    /**
     * Save image from URI to internal storage
     */
    public static String saveImageToInternalStorage(Context context, Uri imageUri) {
        try {
            // Create galleries directory if it doesn't exist
            File galleriesDir = new File(context.getFilesDir(), GALLERY_FOLDER);
            if (!galleriesDir.exists()) {
                galleriesDir.mkdirs();
            }
            
            // Generate unique filename
            String filename = UUID.randomUUID().toString() + ".jpg";
            File imageFile = new File(galleriesDir, filename);
            
            // Load and compress the image
            InputStream inputStream = context.getContentResolver().openInputStream(imageUri);
            Bitmap bitmap = BitmapFactory.decodeStream(inputStream);
            inputStream.close();
            
            if (bitmap != null) {
                // Resize if necessary
                Bitmap resizedBitmap = resizeImage(bitmap, MAX_IMAGE_SIZE);
                
                // Save to file
                FileOutputStream outputStream = new FileOutputStream(imageFile);
                resizedBitmap.compress(Bitmap.CompressFormat.JPEG, JPEG_QUALITY, outputStream);
                outputStream.close();
                
                // Clean up
                if (resizedBitmap != bitmap) {
                    resizedBitmap.recycle();
                }
                bitmap.recycle();
                
                return imageFile.getAbsolutePath();
            }
            
        } catch (IOException e) {
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * Delete a file
     */
    public static boolean deleteFile(String filePath) {
        try {
            File file = new File(filePath);
            return file.exists() && file.delete();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Check if file exists
     */
    public static boolean fileExists(String filePath) {
        try {
            File file = new File(filePath);
            return file.exists();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Get file size in bytes
     */
    public static long getFileSize(String filePath) {
        try {
            File file = new File(filePath);
            return file.exists() ? file.length() : 0;
        } catch (Exception e) {
            return 0;
        }
    }
    
    /**
     * Get galleries directory
     */
    public static File getGalleriesDirectory(Context context) {
        return new File(context.getFilesDir(), GALLERY_FOLDER);
    }
    
    /**
     * Clean up orphaned image files (images not referenced by any gallery)
     */
    public static int cleanupOrphanedImages(Context context) {
        // This would require access to GalleryManager to check which images are still referenced
        // For now, return 0 as placeholder
        return 0;
    }
    
    /**
     * Get total size of all gallery images
     */
    public static long getTotalGallerySize(Context context) {
        File galleriesDir = getGalleriesDirectory(context);
        return getDirectorySize(galleriesDir);
    }
    
    /**
     * Resize image if it's larger than maxSize
     */
    private static Bitmap resizeImage(Bitmap bitmap, int maxSize) {
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        
        if (width <= maxSize && height <= maxSize) {
            return bitmap; // No resizing needed
        }
        
        float ratio = Math.min((float) maxSize / width, (float) maxSize / height);
        int newWidth = Math.round(width * ratio);
        int newHeight = Math.round(height * ratio);
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true);
    }
    
    /**
     * Get directory size recursively
     */
    private static long getDirectorySize(File directory) {
        long size = 0;
        
        if (directory != null && directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        size += getDirectorySize(file);
                    } else {
                        size += file.length();
                    }
                }
            }
        }
        
        return size;
    }
    
    /**
     * Format file size for display
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * Check if external storage is available for writing
     */
    public static boolean isExternalStorageWritable() {
        String state = Environment.getExternalStorageState();
        return Environment.MEDIA_MOUNTED.equals(state);
    }
    
    /**
     * Check if external storage is available for reading
     */
    public static boolean isExternalStorageReadable() {
        String state = Environment.getExternalStorageState();
        return Environment.MEDIA_MOUNTED.equals(state) ||
                Environment.MEDIA_MOUNTED_READ_ONLY.equals(state);
    }
}
