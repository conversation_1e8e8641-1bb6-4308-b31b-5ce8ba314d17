package com.alrbea.androidapp.utils;

import com.alrbea.androidapp.data.model.AladhanResponse;
import com.alrbea.androidapp.data.model.AladhanCalendarResponse;
import com.alrbea.androidapp.data.model.PrayerTimes;
import java.util.ArrayList;
import java.util.List;

public class AladhanConverter {
    
    public static PrayerTimes aladhanToPrayerTimes(AladhanResponse aladhanResponse, String city) {
        if (aladhanResponse == null || aladhanResponse.getData() == null || aladhanResponse.getData().getTimings() == null) {
            return null;
        }
        
        AladhanResponse.AladhanTimings timings = aladhanResponse.getData().getTimings();
        AladhanResponse.AladhanDate date = aladhanResponse.getData().getDate();
        
        PrayerTimes prayerTimes = new PrayerTimes();
        prayerTimes.setFajr(timings.getFajr());
        prayerTimes.setSunrise(timings.getSunrise());
        prayerTimes.setDhuhr(timings.getDhuhr());
        prayerTimes.setAsr(timings.getAsr());
        prayerTimes.setMaghrib(timings.getMaghrib());
        prayerTimes.setIsha(timings.getIsha());
        prayerTimes.setDate(date.getReadable());
        prayerTimes.setCity(city);
        
        return prayerTimes;
    }
    
    public static List<PrayerTimes> calendarToPrayerTimesList(AladhanCalendarResponse calendarResponse, String city) {
        List<PrayerTimes> prayerTimesList = new ArrayList<>();
        
        if (calendarResponse == null || calendarResponse.getData() == null) {
            return prayerTimesList;
        }
        
        for (AladhanResponse.AladhanData dayData : calendarResponse.getData()) {
            if (dayData.getTimings() != null) {
                PrayerTimes prayerTimes = new PrayerTimes();
                prayerTimes.setFajr(dayData.getTimings().getFajr());
                prayerTimes.setSunrise(dayData.getTimings().getSunrise());
                prayerTimes.setDhuhr(dayData.getTimings().getDhuhr());
                prayerTimes.setAsr(dayData.getTimings().getAsr());
                prayerTimes.setMaghrib(dayData.getTimings().getMaghrib());
                prayerTimes.setIsha(dayData.getTimings().getIsha());
                prayerTimes.setDate(dayData.getDate().getReadable());
                prayerTimes.setCity(city);
                
                prayerTimesList.add(prayerTimes);
            }
        }
        
        return prayerTimesList;
    }
    
    public static List<PrayerTimes> aladhanToPrayerTimesList(AladhanResponse aladhanResponse, String city) {
        List<PrayerTimes> prayerTimesList = new ArrayList<>();
        
        if (aladhanResponse == null || aladhanResponse.getData() == null) {
            return prayerTimesList;
        }
        
        // Handle single day response
        PrayerTimes prayerTimes = aladhanToPrayerTimes(aladhanResponse, city);
        if (prayerTimes != null) {
            prayerTimesList.add(prayerTimes);
        }
        
        return prayerTimesList;
    }
} 