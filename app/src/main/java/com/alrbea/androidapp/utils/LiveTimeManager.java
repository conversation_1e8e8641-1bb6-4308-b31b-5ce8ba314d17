package com.alrbea.androidapp.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.widget.TextView;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public class LiveTimeManager {
    
    private Context context;
    private TextView timeTextView;
    private TextView dateTextView;
    private TextView hijriDateTextView;
    private Handler handler;
    private Runnable timeRunnable;
    private boolean isRunning = false;
    
    public LiveTimeManager(Context context) {
        this.context = context;
        this.handler = new Handler(Looper.getMainLooper());
    }
    
    public void setTimeTextView(TextView timeTextView) {
        this.timeTextView = timeTextView;
    }
    
    public void setDateTextView(TextView dateTextView) {
        this.dateTextView = dateTextView;
    }
    
    public void setHijriDateTextView(TextView hijriDateTextView) {
        this.hijriDateTextView = hijriDateTextView;
    }
    
    public void startLiveTime() {
        if (!isRunning) {
            isRunning = true;
            updateTime();
        }
    }
    
    public void stopLiveTime() {
        isRunning = false;
        if (handler != null && timeRunnable != null) {
            handler.removeCallbacks(timeRunnable);
        }
    }
    
    private void updateTime() {
        if (!isRunning) {
            return;
        }
        
        Calendar now = Calendar.getInstance();
        
        // Update current time
        if (timeTextView != null) {
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());
            String currentTime = timeFormat.format(now.getTime());
            timeTextView.setText(currentTime);
        }
        
        // Update Gregorian date
        if (dateTextView != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("EEEE, dd MMMM yyyy", new Locale("ar"));
            String currentDate = dateFormat.format(now.getTime());
            dateTextView.setText(currentDate);
        }
        
        // Update Hijri date
        if (hijriDateTextView != null) {
            String hijriDate = HijriCalendarConverter.getCurrentHijriDateArabicString();
            hijriDateTextView.setText(hijriDate);
        }
        
        // Schedule next update (every second)
        timeRunnable = new Runnable() {
            @Override
            public void run() {
                updateTime();
            }
        };
        
        handler.postDelayed(timeRunnable, 1000);
    }
    
    public String getCurrentTimeString() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());
        return sdf.format(new Date());
    }
    
    public String getCurrentDateString() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
        return sdf.format(new Date());
    }
    
    public String getCurrentHijriDateString() {
        return HijriCalendarConverter.getCurrentHijriDateArabicString();
    }
    
    public String getCurrentDayOfWeek() {
        SimpleDateFormat sdf = new SimpleDateFormat("EEEE", new Locale("ar"));
        return sdf.format(new Date());
    }
    
    public String getTimeZoneString() {
        return TimeZone.getDefault().getID();
    }
    
    public void onResume() {
        startLiveTime();
    }
    
    public void onPause() {
        stopLiveTime();
    }
    
    public void onDestroy() {
        stopLiveTime();
    }
} 