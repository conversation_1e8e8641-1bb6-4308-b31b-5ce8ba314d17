package com.alrbea.androidapp.utils;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

public class HijriCalendarConverter {
    
    private static final int HIJRI_YEAR_OFFSET = 579;
    private static final int HIJRI_MONTH_OFFSET = 1;
    private static final int HIJRI_DAY_OFFSET = 1;
    
    public static class HijriDate {
        private int year;
        private int month;
        private int day;
        private String monthName;
        private String dayName;
        
        public HijriDate(int year, int month, int day) {
            this.year = year;
            this.month = month;
            this.day = day;
            this.monthName = getMonthName();
            this.dayName = getDayName();
        }
        
        public int getYear() { return year; }
        public int getMonth() { return month; }
        public int getDay() { return day; }
        public String getMonthName() { return monthName; }
        public String getDayName() { return dayName; }
        
        @Override
        public String toString() {
            return String.format("%04d/%02d/%02d", year, month, day);
        }
        
        public String toArabicString() {
            return String.format("%s %d %s %d", dayName, day, monthName, year);
        }
    }
    
    public static HijriDate gregorianToHijri(Date gregorianDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(gregorianDate);
        
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DAY_OF_MONTH);
        
        return gregorianToHijri(year, month, day);
    }
    
    public static HijriDate gregorianToHijri(int year, int month, int day) {
        // Simple conversion algorithm (for more accurate results, use a proper library)
        int hijriYear = year - HIJRI_YEAR_OFFSET;
        int hijriMonth = month;
        int hijriDay = day;
        
        // Adjust for Hijri calendar differences
        if (month < 3) {
            hijriYear--;
        }
        
        // Simple month adjustment (not 100% accurate but close)
        if (month == 1) {
            hijriMonth = 5;
        } else if (month == 2) {
            hijriMonth = 6;
        } else if (month == 3) {
            hijriMonth = 7;
        } else if (month == 4) {
            hijriMonth = 8;
        } else if (month == 5) {
            hijriMonth = 9;
        } else if (month == 6) {
            hijriMonth = 10;
        } else if (month == 7) {
            hijriMonth = 11;
        } else if (month == 8) {
            hijriMonth = 12;
        } else if (month == 9) {
            hijriMonth = 1;
        } else if (month == 10) {
            hijriMonth = 2;
        } else if (month == 11) {
            hijriMonth = 3;
        } else if (month == 12) {
            hijriMonth = 4;
        }
        
        return new HijriDate(hijriYear, hijriMonth, hijriDay);
    }
    
    private static String getMonthName(int month) {
        switch (month) {
            case 1: return "محرم";
            case 2: return "صفر";
            case 3: return "ربيع الأول";
            case 4: return "ربيع الآخر";
            case 5: return "جمادى الأولى";
            case 6: return "جمادى الآخرة";
            case 7: return "رجب";
            case 8: return "شعبان";
            case 9: return "رمضان";
            case 10: return "شوال";
            case 11: return "ذو القعدة";
            case 12: return "ذو الحجة";
            default: return "غير معروف";
        }
    }
    
    private static String getDayName(int month, int day) {
        // This is a simplified version - for more accuracy, calculate the actual day
        Calendar cal = Calendar.getInstance();
        cal.set(2024, month - 1, day); // Use a reference year
        
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        switch (dayOfWeek) {
            case Calendar.SUNDAY: return "الأحد";
            case Calendar.MONDAY: return "الاثنين";
            case Calendar.TUESDAY: return "الثلاثاء";
            case Calendar.WEDNESDAY: return "الأربعاء";
            case Calendar.THURSDAY: return "الخميس";
            case Calendar.FRIDAY: return "الجمعة";
            case Calendar.SATURDAY: return "السبت";
            default: return "غير معروف";
        }
    }
    
    public static String getCurrentHijriDateString() {
        HijriDate hijriDate = gregorianToHijri(new Date());
        return hijriDate.toString();
    }
    
    public static String getCurrentHijriDateArabicString() {
        HijriDate hijriDate = gregorianToHijri(new Date());
        return hijriDate.toArabicString();
    }
} 