package com.alrbea.androidapp.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import java.util.Locale;

public class LocaleHelper {
    
    private static final String PREFS_NAME = "PrayerAppPrefs";
    private static final String KEY_LANGUAGE = "selected_language";
    
    public static final String LANGUAGE_ARABIC = "ar";
    public static final String LANGUAGE_ENGLISH = "en";
    
    /**
     * Set and persist the app language
     */
    public static Context setLocale(Context context, String language) {
        persist(context, language);
        return updateResources(context, language);
    }
    
    /**
     * Get the saved language or default to Arabic
     */
    public static String getLanguage(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getString(KEY_LANGUAGE, LANGUAGE_ARABIC);
    }
    
    /**
     * Persist the language preference
     */
    private static void persist(Context context, String language) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit().putString(KEY_LANGUAGE, language).apply();
    }
    
    /**
     * Update the app resources with the new language
     */
    private static Context updateResources(Context context, String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);
        
        Resources resources = context.getResources();
        Configuration configuration = resources.getConfiguration();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            configuration.setLocale(locale);
            configuration.setLayoutDirection(locale);
            return context.createConfigurationContext(configuration);
        } else {
            configuration.locale = locale;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                configuration.setLayoutDirection(locale);
            }
            resources.updateConfiguration(configuration, resources.getDisplayMetrics());
            return context;
        }
    }
    
    /**
     * Check if current language is RTL
     */
    public static boolean isRTL(Context context) {
        String language = getLanguage(context);
        return LANGUAGE_ARABIC.equals(language);
    }
    
    /**
     * Toggle between Arabic and English
     */
    public static String toggleLanguage(Context context) {
        String currentLanguage = getLanguage(context);
        String newLanguage = LANGUAGE_ARABIC.equals(currentLanguage) ? 
                            LANGUAGE_ENGLISH : LANGUAGE_ARABIC;
        setLocale(context, newLanguage);
        return newLanguage;
    }
    
    /**
     * Get language display name
     */
    public static String getLanguageDisplayName(String language) {
        switch (language) {
            case LANGUAGE_ARABIC:
                return "العربية";
            case LANGUAGE_ENGLISH:
                return "English";
            default:
                return "العربية";
        }
    }
    
    /**
     * Get available languages
     */
    public static String[] getAvailableLanguages() {
        return new String[]{LANGUAGE_ARABIC, LANGUAGE_ENGLISH};
    }
    
    /**
     * Get available language display names
     */
    public static String[] getAvailableLanguageNames() {
        return new String[]{"العربية", "English"};
    }
//    public static String getValue(String key){
//        return
//    }
//    public static void setValue(String key,String value){
//
//    }
}
