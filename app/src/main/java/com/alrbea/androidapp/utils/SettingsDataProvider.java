package com.alrbea.androidapp.utils;

import android.content.Context;
import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.SettingsCategory;
import com.alrbea.androidapp.data.model.SettingsItem;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class SettingsDataProvider {
    
    public static List<SettingsCategory> getSettingsCategories(Context context) {
        List<SettingsCategory> categories = new ArrayList<>();
        
        // 1. إعدادات التطبيق
        categories.add(new SettingsCategory(
            "app_settings",
            "إعدادات التطبيق",
            "App Settings",
            R.drawable.ic_settings,
            getAppSettingsItems()
        ));
        
        // 2. الإعلانات
        categories.add(new SettingsCategory(
            "advertisements",
            "الإعلانات",
            "Advertisements",
            R.drawable.ic_ads,
            getAdvertisementItems()
        ));
        
        // 3. معرض الصور
        categories.add(new SettingsCategory(
            "photo_gallery",
            "معرض الصور",
            "Photo Gallery",
            R.drawable.ic_gallery,
            getPhotoGalleryItems()
        ));
        
        // 4. الأذكار
        categories.add(new SettingsCategory(
            "azkar",
            "الأذكار",
            "Azkar",
            R.drawable.ic_azkar,
            getAzkarItems()
        ));
        
        // 5. أوقات الصلاة
        categories.add(new SettingsCategory(
            "prayer_times",
            "أوقات الصلاة",
            "Prayer Times",
            R.drawable.ic_prayer_times,
            getPrayerTimesItems()
        ));
        
        // 6. تحديث التطبيق
        categories.add(new SettingsCategory(
            "app_update",
            "تحديث التطبيق",
            "App Update",
            R.drawable.ic_update,
            new ArrayList<SettingsItem>()
        ));
        
        // 7. حول التطبيق
        categories.add(new SettingsCategory(
            "about",
            "حول التطبيق",
            "About App",
            R.drawable.ic_info,
            new ArrayList<SettingsItem>()
        ));

        // 8. إعادة تشغيل التطبيق
        categories.add(new SettingsCategory(
            "restart",
            "إعادة تشغيل التطبيق",
            "Restart App",
            R.drawable.ic_restart,
            new ArrayList<SettingsItem>()
        ));

        // 9. موفر الطاقة
        categories.add(new SettingsCategory(
            "power_saver",
            "موفر الطاقة",
            "Power Saver",
            R.drawable.ic_battery,
            new ArrayList<SettingsItem>()
        ));

        // 10. مزامنة البيانات
        categories.add(new SettingsCategory(
            "data_sync",
            "مزامنة البيانات",
            "Data Sync",
            R.drawable.ic_sync,
            new ArrayList<SettingsItem>()
        ));       // 11. مزامنة البيانات
        categories.add(new SettingsCategory(
            "remote_control",
            "تحكم عن بعد",
            "Remote Controll",
            R.drawable.ic_sync,
            new ArrayList<SettingsItem>()
        ));
        
        return categories;
    }
    
    private static List<SettingsItem> getAppSettingsItems() {
        return Arrays.asList(
//            new SettingsItem("language", "اللغة", "Language", R.drawable.ic_language, SettingsItem.ItemType.NAVIGATION),
//            new SettingsItem("theme", "المظهر", "Theme", R.drawable.ic_theme, SettingsItem.ItemType.NAVIGATION),
//            new SettingsItem("notifications", "الإشعارات", "Notifications", R.drawable.ic_notifications, SettingsItem.ItemType.TOGGLE)
        );
    }
    
    private static List<SettingsItem> getAdvertisementItems() {
        return Arrays.asList(
//            new SettingsItem("news_banner", "الشريط الإخباري", "News Banner", R.drawable.ic_news, SettingsItem.ItemType.TOGGLE),
//            new SettingsItem("events", "الأحداث والمناسبات", "Events & Occasions", R.drawable.ic_events, SettingsItem.ItemType.TOGGLE),
//            new SettingsItem("funeral_messages", "رسائل الجنازة", "Funeral Messages", R.drawable.ic_funeral, SettingsItem.ItemType.TOGGLE),
//            new SettingsItem("friday_sermon", "عنوان خطبة الجمعة", "Friday Sermon Title", R.drawable.ic_sermon, SettingsItem.ItemType.TOGGLE)
        );
    }
    
    private static List<SettingsItem> getPhotoGalleryItems() {
        return Arrays.asList(
//            new SettingsItem("gallery_enable", "تفعيل المعرض", "Enable Gallery", R.drawable.ic_toggle, SettingsItem.ItemType.TOGGLE),
//            new SettingsItem("photo_duration", "مدة عرض الصورة", "Photo Display Duration", R.drawable.ic_timer, SettingsItem.ItemType.NUMBER_INPUT),
//            new SettingsItem("display_schedule", "جدولة العرض", "Display Schedule", R.drawable.ic_schedule, SettingsItem.ItemType.TIME_PICKER)
        );
    }
    
    private static List<SettingsItem> getAzkarItems() {
        return Arrays.asList(
//            new SettingsItem("morning_evening_azkar", "أذكار الصباح والمساء", "Morning & Evening Azkar", R.drawable.ic_sun_moon, SettingsItem.ItemType.TOGGLE),
//            new SettingsItem("after_prayer_azkar", "أذكار بعد الصلاة", "After Prayer Azkar", R.drawable.ic_prayer_beads, SettingsItem.ItemType.TOGGLE),
//            new SettingsItem("azkar_duration", "مدة عرض الأذكار", "Azkar Display Duration", R.drawable.ic_timer, SettingsItem.ItemType.NUMBER_INPUT)
        );
    }
    
    private static List<SettingsItem> getPrayerTimesItems() {
        return Arrays.asList(
//            new SettingsItem("ramadan_settings", "إعدادات رمضان", "Ramadan Settings", R.drawable.ic_ramadan, SettingsItem.ItemType.NAVIGATION),
//            new SettingsItem("normal_days_settings", "إعدادات الأيام العادية", "Normal Days Settings", R.drawable.ic_calendar, SettingsItem.ItemType.NAVIGATION),
//            new SettingsItem("iqama_timing", "توقيت الإقامة", "Iqama Timing", R.drawable.ic_iqama, SettingsItem.ItemType.TIME_PICKER),
//            new SettingsItem("prayer_duration", "مدة أداء الصلاة", "Prayer Duration", R.drawable.ic_duration, SettingsItem.ItemType.NUMBER_INPUT)
        );
    }
}
