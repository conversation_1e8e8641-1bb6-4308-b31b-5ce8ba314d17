package com.alrbea.androidapp.utils;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Environment;
import android.util.Log;
import androidx.core.content.FileProvider;

import com.alrbea.androidapp.data.model.AppUpdateInfo;

import org.json.JSONObject;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;

public class AppUpdateManager {
    
    private static final String TAG = "AppUpdateManager";
    private static final String PREFS_NAME = "AppUpdatePrefs";
    private static final String BASE_URL = "https://application.alrbea.com/public/api/app-update/";
    
    private Context context;
    private SharedPreferences prefs;
    private UpdateCheckListener updateCheckListener;
    private DownloadProgressListener downloadProgressListener;
    
    public interface UpdateCheckListener {
        void onUpdateAvailable(AppUpdateInfo updateInfo);
        void onNoUpdateAvailable();
        void onUpdateCheckFailed(String error);
    }
    
    public interface DownloadProgressListener {
        void onDownloadStarted();
        void onDownloadProgress(int progress, long downloadedBytes, long totalBytes);
        void onDownloadCompleted(File apkFile);
        void onDownloadFailed(String error);
        void onDownloadPaused();
        void onDownloadResumed();
    }
    
    public AppUpdateManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    public void setUpdateCheckListener(UpdateCheckListener listener) {
        this.updateCheckListener = listener;
    }
    
    public void setDownloadProgressListener(DownloadProgressListener listener) {
        this.downloadProgressListener = listener;
    }
    
    /**
     * Check if device is connected to internet
     */
    public boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = 
            (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        
        if (connectivityManager != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                android.net.Network network = connectivityManager.getActiveNetwork();
                android.net.NetworkCapabilities capabilities = 
                    connectivityManager.getNetworkCapabilities(network);
                return capabilities != null && 
                       (capabilities.hasTransport(android.net.NetworkCapabilities.TRANSPORT_WIFI) ||
                        capabilities.hasTransport(android.net.NetworkCapabilities.TRANSPORT_CELLULAR));
            } else {
                NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
                return activeNetworkInfo != null && activeNetworkInfo.isConnected();
            }
        }
        return false;
    }
    
    /**
     * Check for app updates
     */
    public void checkForUpdates() {
        if (!isNetworkAvailable()) {
            if (updateCheckListener != null) {
                updateCheckListener.onUpdateCheckFailed("لا يوجد اتصال بالإنترنت");
            }
            return;
        }
        
        new CheckUpdateTask().execute();
    }
    
    /**
     * Download app update with resume support
     */
    public void downloadUpdate(AppUpdateInfo updateInfo) {
        if (!isNetworkAvailable()) {
            if (downloadProgressListener != null) {
                downloadProgressListener.onDownloadFailed("لا يوجد اتصال بالإنترنت");
            }
            return;
        }
        
        new DownloadUpdateTask(updateInfo).execute();
    }
    
    /**
     * Install downloaded APK
     */
    public void installUpdate(File apkFile) {
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                Uri apkUri = FileProvider.getUriForFile(context, 
                    context.getPackageName() + ".fileprovider", apkFile);
                intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            } else {
                intent.setDataAndType(Uri.fromFile(apkFile), "application/vnd.android.package-archive");
            }
            
            context.startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error installing update", e);
        }
    }
    
    /**
     * Get current app version code
     */
    public int getCurrentVersionCode() {
        try {
            return context.getPackageManager()
                .getPackageInfo(context.getPackageName(), 0).versionCode;
        } catch (Exception e) {
            return 1;
        }
    }
    
    /**
     * Calculate MD5 hash of file
     */
    private String calculateMD5(File file) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            InputStream is = new java.io.FileInputStream(file);
            byte[] buffer = new byte[8192];
            int read;
            while ((read = is.read(buffer)) > 0) {
                md.update(buffer, 0, read);
            }
            is.close();
            
            byte[] md5sum = md.digest();
            StringBuilder hexString = new StringBuilder();
            for (byte b : md5sum) {
                hexString.append(String.format("%02x", b));
            }
            return hexString.toString();
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * AsyncTask to check for updates
     */
    private class CheckUpdateTask extends AsyncTask<Void, Void, AppUpdateInfo> {
        private String errorMessage;
        
        @Override
        protected AppUpdateInfo doInBackground(Void... voids) {
            try {
                URL url = new URL(BASE_URL + "check");
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setDoOutput(true);
                
                // Send current version code
                JSONObject requestBody = new JSONObject();
                requestBody.put("current_version_code", getCurrentVersionCode());
                requestBody.put("language", "ar");
                
                connection.getOutputStream().write(requestBody.toString().getBytes());
                
                int responseCode = connection.getResponseCode();
                if (responseCode == 200) {
                    InputStream inputStream = connection.getInputStream();
                    StringBuilder response = new StringBuilder();
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        response.append(new String(buffer, 0, bytesRead));
                    }
                    
                    JSONObject jsonResponse = new JSONObject(response.toString());
                    if (jsonResponse.getBoolean("success") && jsonResponse.getBoolean("has_update")) {
                        JSONObject updateData = jsonResponse.getJSONObject("update");
                        return AppUpdateInfo.fromJson(updateData);
                    }
                }
                
                connection.disconnect();
            } catch (Exception e) {
                errorMessage = e.getMessage();
                Log.e(TAG, "Error checking for updates", e);
            }
            return null;
        }
        
        @Override
        protected void onPostExecute(AppUpdateInfo updateInfo) {
            if (updateInfo != null) {
                if (updateCheckListener != null) {
                    updateCheckListener.onUpdateAvailable(updateInfo);
                }
            } else if (errorMessage != null) {
                if (updateCheckListener != null) {
                    updateCheckListener.onUpdateCheckFailed(errorMessage);
                }
            } else {
                if (updateCheckListener != null) {
                    updateCheckListener.onNoUpdateAvailable();
                }
            }
        }
    }
    
    /**
     * AsyncTask to download updates with resume support
     */
    private class DownloadUpdateTask extends AsyncTask<Void, Integer, File> {
        private AppUpdateInfo updateInfo;
        private String errorMessage;
        private File outputFile;
        private long downloadedBytes = 0;
        
        public DownloadUpdateTask(AppUpdateInfo updateInfo) {
            this.updateInfo = updateInfo;
        }
        
        @Override
        protected void onPreExecute() {
            if (downloadProgressListener != null) {
                downloadProgressListener.onDownloadStarted();
            }
        }
        
        @Override
        protected File doInBackground(Void... voids) {
            try {
                // Create downloads directory
                File downloadsDir = new File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "updates");
                if (!downloadsDir.exists()) {
                    downloadsDir.mkdirs();
                }
                
                outputFile = new File(downloadsDir, updateInfo.getFileName());
                
                // Check if file already exists (for resume)
                if (outputFile.exists()) {
                    downloadedBytes = outputFile.length();
                    
                    // Verify if existing file is complete and valid
                    if (downloadedBytes == updateInfo.getFileSize()) {
                        String existingHash = calculateMD5(outputFile);
                        if (updateInfo.getFileHash().equals(existingHash)) {
                            // File is already complete and valid
                            return outputFile;
                        } else {
                            // File is corrupted, delete and restart
                            outputFile.delete();
                            downloadedBytes = 0;
                        }
                    }
                }
                
                URL url = new URL(updateInfo.getDownloadUrl());
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                
                // Set range header for resume
                if (downloadedBytes > 0) {
                    connection.setRequestProperty("Range", "bytes=" + downloadedBytes + "-");
                    if (downloadProgressListener != null) {
                        downloadProgressListener.onDownloadResumed();
                    }
                }
                
                connection.connect();
                
                int responseCode = connection.getResponseCode();
                if (responseCode != 200 && responseCode != 206) {
                    throw new IOException("Server returned HTTP " + responseCode);
                }
                
                long totalBytes = updateInfo.getFileSize();
                InputStream input = new BufferedInputStream(connection.getInputStream());
                FileOutputStream output = new FileOutputStream(outputFile, downloadedBytes > 0);
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                long lastProgressUpdate = System.currentTimeMillis();
                
                while ((bytesRead = input.read(buffer)) != -1) {
                    if (isCancelled()) {
                        break;
                    }
                    
                    output.write(buffer, 0, bytesRead);
                    downloadedBytes += bytesRead;
                    
                    // Update progress every 500ms
                    long currentTime = System.currentTimeMillis();
                    if (currentTime - lastProgressUpdate > 500) {
                        int progress = (int) ((downloadedBytes * 100) / totalBytes);
                        publishProgress(progress);
                        lastProgressUpdate = currentTime;
                    }
                }
                
                output.close();
                input.close();
                connection.disconnect();
                
                // Verify downloaded file
                if (downloadedBytes == totalBytes) {
                    String downloadedHash = calculateMD5(outputFile);
                    if (updateInfo.getFileHash().equals(downloadedHash)) {
                        return outputFile;
                    } else {
                        throw new IOException("File verification failed");
                    }
                } else {
                    throw new IOException("Download incomplete");
                }
                
            } catch (Exception e) {
                errorMessage = e.getMessage();
                Log.e(TAG, "Error downloading update", e);
            }
            return null;
        }
        
        @Override
        protected void onProgressUpdate(Integer... progress) {
            if (downloadProgressListener != null) {
                downloadProgressListener.onDownloadProgress(progress[0], downloadedBytes, updateInfo.getFileSize());
            }
        }
        
        @Override
        protected void onPostExecute(File file) {
            if (file != null) {
                if (downloadProgressListener != null) {
                    downloadProgressListener.onDownloadCompleted(file);
                }
            } else {
                if (downloadProgressListener != null) {
                    downloadProgressListener.onDownloadFailed(errorMessage != null ? errorMessage : "Download failed");
                }
            }
        }
    }
}
