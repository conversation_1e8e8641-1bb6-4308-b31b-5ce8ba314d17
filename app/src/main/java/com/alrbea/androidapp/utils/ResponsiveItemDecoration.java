package com.alrbea.androidapp.utils;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class ResponsiveItemDecoration extends RecyclerView.ItemDecoration {
    
    private final int spacing;
    private final boolean includeEdge;
    
    public ResponsiveItemDecoration(Context context, boolean includeEdge) {
        this.spacing = ResponsiveLayoutManager.getItemDecorationSpacing(context);
        this.includeEdge = includeEdge;
    }
    
    public ResponsiveItemDecoration(int spacing, boolean includeEdge) {
        this.spacing = spacing;
        this.includeEdge = includeEdge;
    }
    
    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view,
                              @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {

        int position = parent.getChildAdapterPosition(view);
        int spanCount = getSpanCount(parent);
        int column = position % spanCount;

        if (includeEdge) {
            // Equal spacing for all items including edges
            outRect.left = spacing - column * spacing / spanCount;
            outRect.right = (column + 1) * spacing / spanCount;

            if (position < spanCount) {
                outRect.top = spacing;
            }
            outRect.bottom = spacing;
        } else {
            // Equal spacing between items only
            outRect.left = column * spacing / spanCount;
            outRect.right = spacing - (column + 1) * spacing / spanCount;

            if (position >= spanCount) {
                outRect.top = spacing;
            }
        }
    }
    
    private int getSpanCount(RecyclerView parent) {
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();
        
        if (layoutManager instanceof androidx.recyclerview.widget.GridLayoutManager) {
            return ((androidx.recyclerview.widget.GridLayoutManager) layoutManager).getSpanCount();
        } else if (layoutManager instanceof androidx.recyclerview.widget.StaggeredGridLayoutManager) {
            return ((androidx.recyclerview.widget.StaggeredGridLayoutManager) layoutManager).getSpanCount();
        }
        
        return 1; // LinearLayoutManager
    }
}
