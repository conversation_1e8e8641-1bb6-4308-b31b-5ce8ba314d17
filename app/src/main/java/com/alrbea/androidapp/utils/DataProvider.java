package com.alrbea.androidapp.utils;

import com.alrbea.androidapp.data.model.CalculationMethod;
import com.alrbea.androidapp.data.model.City;
import com.alrbea.androidapp.data.model.Country;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class DataProvider {
    
    public static List<Country> getCountries() {
        List<Country> countries = new ArrayList<>();
        
        // Saudi Arabia
        List<City> saudiCities = Arrays.asList(
            new City("riyadh", "الرياض", "Riyadh", 24.7136, 46.6753, "Asia/Riyadh"),
            new City("jeddah", "جدة", "Jeddah", 21.4858, 39.1925, "Asia/Riyadh"),
            new City("mecca", "مكة المكرمة", "Mecca", 21.3891, 39.8579, "Asia/Riyadh"),
            new City("medina", "المدينة المنورة", "Medina", 24.5247, 39.5692, "Asia/Riyadh"),
            new City("dammam", "الدمام", "Dammam", 26.4207, 50.0888, "Asia/Riyadh"),
            new City("khobar", "الخبر", "Khobar", 26.2172, 50.1971, "Asia/Riyadh"),
            new City("taif", "الطائف", "Taif", 21.2703, 40.4158, "Asia/Riyadh"),
            new City("abha", "أبها", "Abha", 18.2164, 42.5053, "Asia/Riyadh")
        );
        countries.add(new Country("SA", "المملكة العربية السعودية", "Saudi Arabia", saudiCities));
        
        // UAE
        List<City> uaeCities = Arrays.asList(
            new City("dubai", "دبي", "Dubai", 25.2048, 55.2708, "Asia/Dubai"),
            new City("abu_dhabi", "أبو ظبي", "Abu Dhabi", 24.2992, 54.6972, "Asia/Dubai"),
            new City("sharjah", "الشارقة", "Sharjah", 25.3463, 55.4209, "Asia/Dubai"),
            new City("ajman", "عجمان", "Ajman", 25.4052, 55.5136, "Asia/Dubai")
        );
        countries.add(new Country("AE", "الإمارات العربية المتحدة", "United Arab Emirates", uaeCities));
        
        // Kuwait
        List<City> kuwaitCities = Arrays.asList(
            new City("kuwait_city", "مدينة الكويت", "Kuwait City", 29.3759, 47.9774, "Asia/Kuwait"),
            new City("hawalli", "حولي", "Hawalli", 29.3375, 48.0281, "Asia/Kuwait")
        );
        countries.add(new Country("KW", "الكويت", "Kuwait", kuwaitCities));
        
        // Qatar
        List<City> qatarCities = Arrays.asList(
            new City("doha", "الدوحة", "Doha", 25.2854, 51.5310, "Asia/Qatar"),
            new City("al_rayyan", "الريان", "Al Rayyan", 25.2919, 51.4240, "Asia/Qatar")
        );
        countries.add(new Country("QA", "قطر", "Qatar", qatarCities));
        
        // Bahrain
        List<City> bahrainCities = Arrays.asList(
            new City("manama", "المنامة", "Manama", 26.2285, 50.5860, "Asia/Bahrain")
        );
        countries.add(new Country("BH", "البحرين", "Bahrain", bahrainCities));
        
        // Oman
        List<City> omanCities = Arrays.asList(
            new City("muscat", "مسقط", "Muscat", 23.5859, 58.4059, "Asia/Muscat"),
            new City("salalah", "صلالة", "Salalah", 17.0151, 54.0924, "Asia/Muscat")
        );
        countries.add(new Country("OM", "عُمان", "Oman", omanCities));
        
        // Jordan
        List<City> jordanCities = Arrays.asList(
            new City("amman", "عمان", "Amman", 31.9454, 35.9284, "Asia/Amman"),
            new City("irbid", "إربد", "Irbid", 32.5556, 35.8500, "Asia/Amman")
        );
        countries.add(new Country("JO", "الأردن", "Jordan", jordanCities));
        
        // Egypt
        List<City> egyptCities = Arrays.asList(
            new City("cairo", "القاهرة", "Cairo", 30.0444, 31.2357, "Africa/Cairo"),
            new City("alexandria", "الإسكندرية", "Alexandria", 31.2001, 29.9187, "Africa/Cairo"),
            new City("giza", "الجيزة", "Giza", 30.0131, 31.2089, "Africa/Cairo")
        );
        countries.add(new Country("EG", "مصر", "Egypt", egyptCities));
        
        return countries;
    }
    
    public static List<CalculationMethod> getCalculationMethods() {
        List<CalculationMethod> methods = new ArrayList<>();
        
        methods.add(new CalculationMethod("automatic", "أوتوماتيكي", "Automatic", 
                "يتم اختيار النظام تلقائياً حسب الموقع", true));
        
        methods.add(new CalculationMethod("umm_al_qura", "أم القرى، مكة المكرمة", "Umm al-Qura, Mecca", 
                "جامعة أم القرى، مكة المكرمة", false));
        
        methods.add(new CalculationMethod("egyptian", "الهيئة المصرية العامة للمساحة", "Egyptian General Survey Authority", 
                "الهيئة المصرية العامة للمساحة", false));
        
        methods.add(new CalculationMethod("karachi", "جامعة العلوم الإسلامية، كراتشي", "Islamic University of Sciences, Karachi", 
                "جامعة العلوم الإسلامية، كراتشي", false));
        
        methods.add(new CalculationMethod("mwl", "رابطة العالم الإسلامي", "Muslim World League", 
                "رابطة العالم الإسلامي", false));
        
        methods.add(new CalculationMethod("isna", "الإتحاد الإسلامي بأمريكا الشمالية", "ISNA North America", 
                "الإتحاد الإسلامي بأمريكا الشمالية", false));
        
        methods.add(new CalculationMethod("gulf", "منطقة الخليج", "Gulf Region", 
                "منطقة الخليج", false));
        
        methods.add(new CalculationMethod("kuwait", "وزارة الأوقاف والشؤون الإسلامية، الكويت", "Kuwait Ministry of Awqaf", 
                "وزارة الأوقاف والشؤون الإسلامية، الكويت", false));
        
        methods.add(new CalculationMethod("singapore", "مجلس أوقاف إسلام سنغافورا، سنغافورة", "MUIS Singapore", 
                "مجلس أوقاف إسلام سنغافورا، سنغافورة", false));
        
        methods.add(new CalculationMethod("france", "منظمة الاتحاد الإسلامي في فرنسا", "UOIF France", 
                "منظمة الاتحاد الإسلامي في فرنسا", false));
        
        methods.add(new CalculationMethod("turkey", "رئاسة الشؤون الدينية، تركيا", "Turkey Presidency of Religious Affairs", 
                "رئاسة الشؤون الدينية، تركيا", false));
        
        methods.add(new CalculationMethod("russia", "الإدارة الروحية لمسلمي روسيا", "Spiritual Administration of Muslims of Russia", 
                "الإدارة الروحية لمسلمي روسيا", false));
        
        methods.add(new CalculationMethod("custom", "تقويم مخصص", "Custom Calendar", 
                "تقويم مخصص حسب الإعدادات المحلية", false));
        
        return methods;
    }
}
