package com.alrbea.androidapp;

import android.app.Application;
import android.content.SharedPreferences;
import android.content.pm.ActivityInfo;

public class MyApp extends Application {

    public static int savedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED;
    private static final String PREFS_NAME = "PrayerAppPrefs";
    private static final String KEY_ORIENTATION = "screen_orientation";
    private static final String KEY_ORIENTATION_SET = "orientation_set";

    @Override
    public void onCreate() {
        super.onCreate();
        loadSavedOrientation();
    }

    private void loadSavedOrientation() {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        if (prefs.getBoolean(KEY_ORIENTATION_SET, false)) {
            savedOrientation = prefs.getInt(KEY_ORIENTATION, ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
    }
}
