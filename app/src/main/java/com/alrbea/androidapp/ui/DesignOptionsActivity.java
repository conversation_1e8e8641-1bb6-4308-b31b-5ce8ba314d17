package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;

import com.alrbea.androidapp.R;

public class DesignOptionsActivity extends BaseActivity {

    private Toolbar toolbar;
    private CardView cardTemplates;
    private CardView cardAzkarTemplates;
    private CardView cardBackgrounds;
    private CardView cardIqamaTemplates;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_design_options);

        initViews();
        setupToolbar();
        setupClickListeners();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        cardTemplates = findViewById(R.id.card_templates);
        cardAzkarTemplates = findViewById(R.id.card_azkar_templates);
        cardBackgrounds = findViewById(R.id.card_backgrounds);
        cardIqamaTemplates = findViewById(R.id.card_iqama_templates);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("خيارات التصميم");
        }
    }

    private void setupClickListeners() {
        cardTemplates.setOnClickListener(v -> openTemplatesActivity());
        cardAzkarTemplates.setOnClickListener(v -> openAzkarTemplatesActivity());
        cardBackgrounds.setOnClickListener(v -> openBackgroundsActivity());
        cardIqamaTemplates.setOnClickListener(v -> openIqamaTemplatesActivity());
    }

    private void openTemplatesActivity() {
        Intent intent = new Intent(this, ThemeTemplatesActivity.class);
        startActivity(intent);
    }

    private void openAzkarTemplatesActivity() {
        Intent intent = new Intent(this, AzkarTemplatesActivity.class);
        startActivity(intent);
    }

    private void openBackgroundsActivity() {
        // TODO: Implement backgrounds activity
        showMessage("الخلفيات");
    }

    private void openIqamaTemplatesActivity() {
        // TODO: Implement iqama templates activity
        showMessage("قوالب الإقامة");
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
