package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.ui.SoundSelectorActivity;

import java.util.List;

public class SoundAdapter extends RecyclerView.Adapter<SoundAdapter.SoundViewHolder> {

    public interface OnSoundSelectedListener {
        void onSoundSelected(SoundSelectorActivity.SoundItem soundItem);
        void onSoundPlay(SoundSelectorActivity.SoundItem soundItem);
        void onSoundStop();
    }

    private List<SoundSelectorActivity.SoundItem> sounds;
    private OnSoundSelectedListener listener;
    private String currentSelection;
    private Context context;
    private int playingPosition = -1;

    public SoundAdapter(List<SoundSelectorActivity.SoundItem> sounds, OnSoundSelectedListener listener, String currentSelection) {
        this.sounds = sounds;
        this.listener = listener;
        this.currentSelection = currentSelection;
    }

    @NonNull
    @Override
    public SoundViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context).inflate(R.layout.item_sound, parent, false);
        return new SoundViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SoundViewHolder holder, int position) {
        SoundSelectorActivity.SoundItem sound = sounds.get(position);
        holder.bind(sound, position);
    }

    @Override
    public int getItemCount() {
        return sounds.size();
    }

    public void setPlayingPosition(int position) {
        int previousPlaying = playingPosition;
        playingPosition = position;
        
        if (previousPlaying != -1) {
            notifyItemChanged(previousPlaying);
        }
        if (position != -1) {
            notifyItemChanged(position);
        }
    }

    public void stopPlaying() {
        setPlayingPosition(-1);
    }

    class SoundViewHolder extends RecyclerView.ViewHolder {
        private CardView cardSound;
        private TextView textSoundName;
        private ImageView iconPlay;
        private ImageView iconSelected;

        public SoundViewHolder(@NonNull View itemView) {
            super(itemView);
            cardSound = itemView.findViewById(R.id.card_sound);
            textSoundName = itemView.findViewById(R.id.text_sound_name);
            iconPlay = itemView.findViewById(R.id.icon_play);
            iconSelected = itemView.findViewById(R.id.icon_selected);
        }

        public void bind(SoundSelectorActivity.SoundItem sound, int position) {
            textSoundName.setText(sound.getName());
            
            // Show selected state
            boolean isSelected = sound.getName().equals(currentSelection);
            iconSelected.setVisibility(isSelected ? View.VISIBLE : View.GONE);
            
            // Show playing state
            boolean isPlaying = playingPosition == position;
            iconPlay.setImageResource(isPlaying ? R.drawable.ic_stop : R.drawable.ic_play);
            
            // Set card background based on selection
            if (isSelected) {
                cardSound.setCardBackgroundColor(context.getResources().getColor(R.color.primary_light));
            } else {
                cardSound.setCardBackgroundColor(context.getResources().getColor(R.color.card_background));
            }
            
            // Play button click
            iconPlay.setOnClickListener(v -> {
                if (isPlaying) {
                    listener.onSoundStop();
                    setPlayingPosition(-1);
                } else {
                    listener.onSoundPlay(sound);
                    setPlayingPosition(position);
                }
            });
            
            // Card click to select
            cardSound.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onSoundSelected(sound);
                }
            });
        }
    }
}
