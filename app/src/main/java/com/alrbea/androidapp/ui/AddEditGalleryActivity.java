package com.alrbea.androidapp.ui;

import android.app.AlertDialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.Gallery;
import com.alrbea.androidapp.ui.adapter.PhotoAdapter;
import com.alrbea.androidapp.utils.FileUtils;
import com.alrbea.androidapp.utils.GalleryManager;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class AddEditGalleryActivity extends BaseActivity implements PhotoAdapter.OnPhotoActionListener {
    
    public static final String EXTRA_GALLERY_ID = "gallery_id";
    public static final String EXTRA_IS_EDIT_MODE = "is_edit_mode";
    
    private Toolbar toolbar;
    private EditText editGalleryName;
    private LinearLayout btnAddPhotos;
    private RecyclerView recyclerViewPhotos;
    private LinearLayout btnExpandSettings;
    private LinearLayout layoutAdvancedSettings;
    private ImageView iconExpand;
    private Button btnGalleryDuration;
    private Button btnImageDuration;
    private Switch switchEnableWithAnnouncement;
    private Switch switchEnableWithAnnouncementFriday;
    private Button btnSaveGallery;
    private Button btnDeleteGallery;
    
    private PhotoAdapter photoAdapter;
    private GalleryManager galleryManager;
    private Gallery currentGallery;
    private boolean isEditMode = false;
    private boolean isAdvancedSettingsExpanded = false;
    
    private ActivityResultLauncher<Intent> imagePickerLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_edit_gallery);
        
        galleryManager = new GalleryManager(this);
        
        // Get extras
        String galleryId = getIntent().getStringExtra(EXTRA_GALLERY_ID);
        isEditMode = getIntent().getBooleanExtra(EXTRA_IS_EDIT_MODE, false);
        
        if (isEditMode && galleryId != null) {
            currentGallery = galleryManager.getGallery(galleryId);
        }
        
        if (currentGallery == null) {
            currentGallery = new Gallery(UUID.randomUUID().toString(), "");
            isEditMode = false;
        }
        
        initViews();
        setupToolbar();
        setupImagePicker();
        setupRecyclerView();
        setupClickListeners();
        loadGalleryData();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        editGalleryName = findViewById(R.id.edit_gallery_name);
        btnAddPhotos = findViewById(R.id.btn_add_photos);
        recyclerViewPhotos = findViewById(R.id.recycler_view_photos);
        btnExpandSettings = findViewById(R.id.btn_expand_settings);
        layoutAdvancedSettings = findViewById(R.id.layout_advanced_settings);
        iconExpand = findViewById(R.id.icon_expand);
        btnGalleryDuration = findViewById(R.id.btn_gallery_duration);
        btnImageDuration = findViewById(R.id.btn_image_duration);
        switchEnableWithAnnouncement = findViewById(R.id.switch_enable_with_announcement);
        switchEnableWithAnnouncementFriday = findViewById(R.id.switch_enable_with_announcement_friday);
        btnSaveGallery = findViewById(R.id.btn_save_gallery);
        btnDeleteGallery = findViewById(R.id.btn_delete_gallery);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle(isEditMode ? "تعديل المعرض" : "إنشاء معرض جديد");
        }
    }
    
    private void setupImagePicker() {
        imagePickerLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                    Intent data = result.getData();
                    if (data.getClipData() != null) {
                        // Multiple images selected
                        int count = data.getClipData().getItemCount();
                        for (int i = 0; i < count; i++) {
                            Uri imageUri = data.getClipData().getItemAt(i).getUri();
                            addImageToGallery(imageUri);
                        }
                    } else if (data.getData() != null) {
                        // Single image selected
                        Uri imageUri = data.getData();
                        addImageToGallery(imageUri);
                    }
                }
            }
        );
    }
    
    private void setupRecyclerView() {
        photoAdapter = new PhotoAdapter(currentGallery.getImagePaths(), this);
        recyclerViewPhotos.setLayoutManager(new GridLayoutManager(this, 3));
        recyclerViewPhotos.setAdapter(photoAdapter);
    }
    
    private void setupClickListeners() {
        btnAddPhotos.setOnClickListener(v -> openImagePicker());
        
        btnExpandSettings.setOnClickListener(v -> toggleAdvancedSettings());
        
        btnGalleryDuration.setOnClickListener(v -> showDurationDialog(
            "مدة عرض المعرض", 
            currentGallery.getGalleryDuration(),
            duration -> {
                currentGallery.setGalleryDuration(duration);
                updateDurationButtons();
            }
        ));
        
        btnImageDuration.setOnClickListener(v -> showDurationDialog(
            "مدة عرض الصورة الواحدة",
            currentGallery.getDisplayDuration(),
            duration -> {
                currentGallery.setDisplayDuration(duration);
                updateDurationButtons();
            }
        ));
        
        switchEnableWithAnnouncement.setOnCheckedChangeListener((buttonView, isChecked) -> 
            currentGallery.setEnableWithAnnouncement(isChecked));
        
        switchEnableWithAnnouncementFriday.setOnCheckedChangeListener((buttonView, isChecked) -> 
            currentGallery.setEnableWithAnnouncementFriday(isChecked));
        
        btnSaveGallery.setOnClickListener(v -> saveGallery());
        
        btnDeleteGallery.setOnClickListener(v -> showDeleteConfirmation());
    }
    
    private void loadGalleryData() {
        if (isEditMode) {
            editGalleryName.setText(currentGallery.getName());
            switchEnableWithAnnouncement.setChecked(currentGallery.isEnableWithAnnouncement());
            switchEnableWithAnnouncementFriday.setChecked(currentGallery.isEnableWithAnnouncementFriday());
            btnDeleteGallery.setVisibility(View.VISIBLE);
        }
        
        updateDurationButtons();
        photoAdapter.updatePhotos(currentGallery.getImagePaths());
    }
    
    private void updateDurationButtons() {
        btnGalleryDuration.setText(currentGallery.getGalleryDuration() + " ثانية");
        btnImageDuration.setText(currentGallery.getDisplayDuration() + " ثانية");
    }
    
    private void toggleAdvancedSettings() {
        isAdvancedSettingsExpanded = !isAdvancedSettingsExpanded;
        layoutAdvancedSettings.setVisibility(isAdvancedSettingsExpanded ? View.VISIBLE : View.GONE);
        iconExpand.setRotation(isAdvancedSettingsExpanded ? 180f : 0f);
    }
    
    private void openImagePicker() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("image/*");
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
        imagePickerLauncher.launch(Intent.createChooser(intent, "اختر الصور"));
    }
    
    private void addImageToGallery(Uri imageUri) {
        try {
            String imagePath = FileUtils.saveImageToInternalStorage(this, imageUri);
            if (imagePath != null) {
                currentGallery.addImagePath(imagePath);
                photoAdapter.addPhoto(imagePath);
            }
        } catch (Exception e) {
            Toast.makeText(this, "فشل في إضافة الصورة", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void showDurationDialog(String title, int currentValue, DurationCallback callback) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(title);
        
        EditText editText = new EditText(this);
        editText.setText(String.valueOf(currentValue));
        editText.setInputType(android.text.InputType.TYPE_CLASS_NUMBER);
        builder.setView(editText);
        
        builder.setPositiveButton("حفظ", (dialog, which) -> {
            try {
                int duration = Integer.parseInt(editText.getText().toString());
                if (duration > 0) {
                    callback.onDurationSet(duration);
                } else {
                    Toast.makeText(this, "يجب أن تكون المدة أكبر من صفر", Toast.LENGTH_SHORT).show();
                }
            } catch (NumberFormatException e) {
                Toast.makeText(this, "يرجى إدخال رقم صحيح", Toast.LENGTH_SHORT).show();
            }
        });
        
        builder.setNegativeButton("إلغاء", null);
        builder.show();
    }
    
    private void saveGallery() {
        String name = editGalleryName.getText().toString().trim();
        
        if (TextUtils.isEmpty(name)) {
            Toast.makeText(this, "يرجى إدخال اسم المعرض", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (currentGallery.getImageCount() == 0) {
            Toast.makeText(this, "يرجى إضافة صورة واحدة على الأقل", Toast.LENGTH_SHORT).show();
            return;
        }
        
        currentGallery.setName(name);
        
        if (galleryManager.saveGallery(currentGallery)) {
            Toast.makeText(this, "تم حفظ المعرض بنجاح", Toast.LENGTH_SHORT).show();
            setResult(RESULT_OK);
            finish();
        } else {
            Toast.makeText(this, "فشل في حفظ المعرض", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void showDeleteConfirmation() {
        new AlertDialog.Builder(this)
            .setTitle("حذف المعرض")
            .setMessage("هل أنت متأكد من حذف هذا المعرض؟")
            .setPositiveButton("حذف", (dialog, which) -> deleteGallery())
            .setNegativeButton("إلغاء", null)
            .show();
    }
    
    private void deleteGallery() {
        if (galleryManager.deleteGallery(currentGallery.getId())) {
            Toast.makeText(this, "تم حذف المعرض بنجاح", Toast.LENGTH_SHORT).show();
            setResult(RESULT_OK);
            finish();
        } else {
            Toast.makeText(this, "فشل في حذف المعرض", Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public void onPhotoDelete(String imagePath) {
        currentGallery.removeImagePath(imagePath);
        photoAdapter.removePhoto(imagePath);
        
        // Delete the actual file
        FileUtils.deleteFile(imagePath);
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    private interface DurationCallback {
        void onDurationSet(int duration);
    }
}
