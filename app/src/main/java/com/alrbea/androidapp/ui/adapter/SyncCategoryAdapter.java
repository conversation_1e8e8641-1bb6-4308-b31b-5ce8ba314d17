package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.SettingsCategory;

import java.util.List;

public class SyncCategoryAdapter extends RecyclerView.Adapter<SyncCategoryAdapter.CategoryViewHolder> {

    public interface OnCategoryToggleListener {
        void onCategoryToggled(SettingsCategory category, boolean enabled);
    }

    private List<SettingsCategory> categories;
    private OnCategoryToggleListener toggleListener;
    private Context context;

    public SyncCategoryAdapter(List<SettingsCategory> categories, OnCategoryToggleListener toggleListener) {
        this.categories = categories;
        this.toggleListener = toggleListener;
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context).inflate(R.layout.item_sync_category, parent, false);
        return new CategoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        SettingsCategory category = categories.get(position);
        holder.bind(category);
    }

    @Override
    public int getItemCount() {
        return categories.size();
    }

    public void updateCategories(List<SettingsCategory> newCategories) {
        this.categories = newCategories;
        notifyDataSetChanged();
    }

    public List<SettingsCategory> getSelectedCategories() {
        return categories;
    }

    class CategoryViewHolder extends RecyclerView.ViewHolder {
        private CheckBox checkboxCategory;
        private ImageView iconCategory;
        private TextView textCategoryName;
        private TextView textCategoryDescription;
        private TextView textCategorySize;

        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            checkboxCategory = itemView.findViewById(R.id.checkbox_category);
            iconCategory = itemView.findViewById(R.id.icon_category);
            textCategoryName = itemView.findViewById(R.id.text_category_name);
            textCategoryDescription = itemView.findViewById(R.id.text_category_description);
            textCategorySize = itemView.findViewById(R.id.text_category_size);
        }

        public void bind(SettingsCategory category) {
            textCategoryName.setText(category.getName());
            textCategoryDescription.setText(category.getDescription());
            textCategorySize.setText(category.getFormattedSize());
            iconCategory.setImageResource(category.getIconResource());
            
            // Set checkbox state without triggering listener
            checkboxCategory.setOnCheckedChangeListener(null);
            checkboxCategory.setChecked(category.isEnabled());
            
            // Set checkbox listener
            checkboxCategory.setOnCheckedChangeListener((buttonView, isChecked) -> {
                category.setEnabled(isChecked);
                if (toggleListener != null) {
                    toggleListener.onCategoryToggled(category, isChecked);
                }
            });
            
            // Make the whole item clickable
            itemView.setOnClickListener(v -> {
                boolean newState = !checkboxCategory.isChecked();
                checkboxCategory.setChecked(newState);
            });
            
            // Update appearance based on enabled state
            float alpha = category.isEnabled() ? 1.0f : 0.6f;
            itemView.setAlpha(alpha);
        }
    }
}
