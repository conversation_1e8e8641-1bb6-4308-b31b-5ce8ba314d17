package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.CalculationMethod;
import com.alrbea.androidapp.data.model.City;
import com.alrbea.androidapp.data.model.Country;
import com.alrbea.androidapp.utils.DataProvider;

import java.util.ArrayList;
import java.util.List;

public class LocationSetupActivity extends BaseActivity {
    
    private static final String PREFS_NAME = "PrayerAppPrefs";
    
    private Spinner spinnerCountry;
    private Spinner spinnerCity;
    private Spinner spinnerCalculationMethod;
    private Button btnContinue;
    private Button btnSkip;
    private TextView tvSelectedInfo;
    
    private List<Country> countries;
    private List<City> cities;
    private List<CalculationMethod> calculationMethods;
    
    private Country selectedCountry;
    private City selectedCity;
    private CalculationMethod selectedMethod;
    
    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_location_setup);
        
        initViews();
        loadData();
        setupSpinners();
        setupClickListeners();
    }
    
    private void initViews() {
        spinnerCountry = findViewById(R.id.spinner_country);
        spinnerCity = findViewById(R.id.spinner_city);
        spinnerCalculationMethod = findViewById(R.id.spinner_calculation_method);
        btnContinue = findViewById(R.id.btn_continue);
        btnSkip = findViewById(R.id.btn_skip);
        tvSelectedInfo = findViewById(R.id.tv_selected_info);
        
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
    }
    
    private void loadData() {
        countries = DataProvider.getCountries();
        calculationMethods = DataProvider.getCalculationMethods();
        cities = new ArrayList<>();
    }
    
    private void setupSpinners() {
        // Setup Country Spinner
        List<String> countryNames = new ArrayList<>();
        countryNames.add("اختر الدولة");
        for (Country country : countries) {
            countryNames.add(country.getDisplayName(getCurrentLanguage()));
        }
        
        ArrayAdapter<String> countryAdapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, countryNames);
        countryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCountry.setAdapter(countryAdapter);
        
        // Setup Calculation Method Spinner
        List<String> methodNames = new ArrayList<>();
        methodNames.add("اختر نظام الحساب");
        for (CalculationMethod method : calculationMethods) {
            methodNames.add(method.getDisplayName(getCurrentLanguage()));
        }
        
        ArrayAdapter<String> methodAdapter = new ArrayAdapter<>(this,
                android.R.layout.simple_spinner_item, methodNames);
        methodAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCalculationMethod.setAdapter(methodAdapter);
        
        // Setup City Spinner (initially empty)
        updateCitySpinner();
    }
    
    private void updateCitySpinner() {
        List<String> cityNames = new ArrayList<>();
        cityNames.add("اختر المدينة");
        
        if (selectedCountry != null) {
            cities = selectedCountry.getCities();
            for (City city : cities) {
                cityNames.add(city.getDisplayName(getCurrentLanguage()));
            }
        }
        
        ArrayAdapter<String> cityAdapter = new ArrayAdapter<>(this,
                android.R.layout.simple_spinner_item, cityNames);
        cityAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCity.setAdapter(cityAdapter);
    }
    
    private void setupClickListeners() {
        spinnerCountry.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) {
                    selectedCountry = countries.get(position - 1);
                    updateCitySpinner();
                    updateSelectedInfo();
                } else {
                    selectedCountry = null;
                    selectedCity = null;
                    updateCitySpinner();
                    updateSelectedInfo();
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
        
        spinnerCity.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0 && selectedCountry != null) {
                    selectedCity = cities.get(position - 1);
                    updateSelectedInfo();
                } else {
                    selectedCity = null;
                    updateSelectedInfo();
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
        
        spinnerCalculationMethod.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) {
                    selectedMethod = calculationMethods.get(position - 1);
                    updateSelectedInfo();
                } else {
                    selectedMethod = null;
                    updateSelectedInfo();
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
        
        btnContinue.setOnClickListener(v -> {
            if (validateSelections()) {
                saveSelectionsAndContinue();
            }
        });
        
        btnSkip.setOnClickListener(v -> {
            skipLocationSetup();
        });
    }
    
    private void updateSelectedInfo() {
        StringBuilder info = new StringBuilder();
        
        if (selectedCountry != null) {
            info.append("الدولة: ").append(selectedCountry.getDisplayName(getCurrentLanguage())).append("\n");
        }
        
        if (selectedCity != null) {
            info.append("المدينة: ").append(selectedCity.getDisplayName(getCurrentLanguage())).append("\n");
        }
        
        if (selectedMethod != null) {
            info.append("نظام الحساب: ").append(selectedMethod.getDisplayName(getCurrentLanguage()));
        }
        
        tvSelectedInfo.setText(info.toString());
        
        // Enable continue button if all selections are made
        btnContinue.setEnabled(selectedCountry != null && selectedCity != null && selectedMethod != null);
    }
    
    private boolean validateSelections() {
        if (selectedCountry == null) {
            showError("يرجى اختيار الدولة");
            return false;
        }
        
        if (selectedCity == null) {
            showError("يرجى اختيار المدينة");
            return false;
        }
        
        if (selectedMethod == null) {
            showError("يرجى اختيار نظام حساب مواقيت الصلاة");
            return false;
        }
        
        return true;
    }
    
    private void saveSelectionsAndContinue() {
        // Save selections
        sharedPreferences.edit()
                .putString("selected_country_code", selectedCountry.getCode())
                .putString("selected_country_name", selectedCountry.getDisplayName(getCurrentLanguage()))
                .putString("selected_city_code", selectedCity.getCode())
                .putString("selected_city_name", selectedCity.getDisplayName(getCurrentLanguage()))
                .putString("selected_method_code", selectedMethod.getCode())
                .putString("selected_method_name", selectedMethod.getDisplayName(getCurrentLanguage()))
                .putFloat("city_latitude", (float) selectedCity.getLatitude())
                .putFloat("city_longitude", (float) selectedCity.getLongitude())
                .putString("city_timezone", selectedCity.getTimezone())
                .putBoolean("location_setup_complete", true)
                .apply();
        
        // Navigate to orientation setup
        Intent intent = new Intent(this, OrientationSetupActivity.class);
        startActivity(intent);
        finish();
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }
    
    private void skipLocationSetup() {
        // Save default selections (Riyadh, Saudi Arabia, Umm al-Qura)
        sharedPreferences.edit()
                .putString("selected_country_code", "SA")
                .putString("selected_country_name", "المملكة العربية السعودية")
                .putString("selected_city_code", "riyadh")
                .putString("selected_city_name", "الرياض")
                .putString("selected_method_code", "umm_al_qura")
                .putString("selected_method_name", "أم القرى، مكة المكرمة")
                .putFloat("city_latitude", 24.7136f)
                .putFloat("city_longitude", 46.6753f)
                .putString("city_timezone", "Asia/Riyadh")
                .putBoolean("location_setup_complete", true)
                .apply();
        
        // Navigate to orientation setup
        Intent intent = new Intent(this, OrientationSetupActivity.class);
        startActivity(intent);
        finish();
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }
    
    private void showError(String message) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void onBackPressed() {
        // Prevent going back to login screen
        // User must complete location setup or skip it
    }
}
