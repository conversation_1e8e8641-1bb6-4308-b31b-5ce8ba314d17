package com.alrbea.androidapp.ui.adapter;

import android.app.TimePickerDialog;
import android.content.Context;
import android.text.InputType;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.PhotoGallerySetting;
import com.alrbea.androidapp.utils.LocaleHelper;
import com.alrbea.androidapp.utils.ResponsiveLayoutManager;
import com.alrbea.androidapp.utils.ScreenUtils;

import java.util.Calendar;
import java.util.List;

public class PhotoGallerySettingsAdapter extends RecyclerView.Adapter<PhotoGallerySettingsAdapter.SettingViewHolder> {
    
    public interface OnSettingChangeListener {
        void onSettingChanged(PhotoGallerySetting setting, Object value);
    }
    
    private List<PhotoGallerySetting> settings;
    private OnSettingChangeListener changeListener;
    private Context context;

    public PhotoGallerySettingsAdapter(List<PhotoGallerySetting> settings, OnSettingChangeListener changeListener) {
        this.settings = settings;
        this.changeListener = changeListener;
    }

    @NonNull
    @Override
    public SettingViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context)
                .inflate(R.layout.item_photo_gallery_setting, parent, false);
        return new SettingViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SettingViewHolder holder, int position) {
        PhotoGallerySetting setting = settings.get(position);
        holder.bind(setting);
    }

    @Override
    public int getItemCount() {
        return settings.size();
    }

    class SettingViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private ImageView iconImageView;
        private TextView titleTextView;
        private TextView descriptionTextView;
        private TextView valueTextView;
        private Switch toggleSwitch;
        private Button actionButton;

        public SettingViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_setting);
            iconImageView = itemView.findViewById(R.id.iv_setting_icon);
            titleTextView = itemView.findViewById(R.id.tv_setting_title);
            descriptionTextView = itemView.findViewById(R.id.tv_setting_description);
            valueTextView = itemView.findViewById(R.id.tv_setting_value);
            toggleSwitch = itemView.findViewById(R.id.switch_setting);
            actionButton = itemView.findViewById(R.id.btn_action);
        }

        public void bind(PhotoGallerySetting setting) {
            String language = LocaleHelper.getLanguage(context);
            
            // Set setting data
            iconImageView.setImageResource(setting.getIconRes());
            titleTextView.setText(setting.getDisplayTitle(language));
            descriptionTextView.setText(setting.getDisplayDescription(language));
            
            // Hide all controls first
            toggleSwitch.setVisibility(View.GONE);
            actionButton.setVisibility(View.GONE);
            valueTextView.setVisibility(View.GONE);
            
            // Show appropriate control based on setting type
            switch (setting.getType()) {
                case TOGGLE:
                    toggleSwitch.setVisibility(View.VISIBLE);
                    toggleSwitch.setOnCheckedChangeListener(null);
                    toggleSwitch.setChecked(setting.isEnabled());
                    toggleSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                        if (changeListener != null) {
                            changeListener.onSettingChanged(setting, isChecked);
                        }
                    });
                    break;
                    
                case ACTION_BUTTON:
                    actionButton.setVisibility(View.VISIBLE);
                    actionButton.setText("اختيار");
                    actionButton.setOnClickListener(v -> {
                        if (changeListener != null) {
                            changeListener.onSettingChanged(setting, null);
                        }
                    });
                    break;
                    
                case NUMBER_INPUT:
                    valueTextView.setVisibility(View.VISIBLE);
                    String currentValue = setting.getValue() != null ? setting.getValue() : "10";
                    valueTextView.setText(currentValue + " ثانية");
                    cardView.setOnClickListener(v -> showNumberInputDialog(setting));
                    break;
                    
                case TIME_PICKER:
                    valueTextView.setVisibility(View.VISIBLE);
                    String timeValue = setting.getValue() != null ? setting.getValue() : "06:00";
                    valueTextView.setText(timeValue);
                    cardView.setOnClickListener(v -> showTimePickerDialog(setting));
                    break;
                    
                case SELECTION:
                    valueTextView.setVisibility(View.VISIBLE);
                    String selectionValue = setting.getValue() != null ? setting.getValue() : "تلاشي";
                    valueTextView.setText(getTransitionDisplayName(selectionValue));
                    cardView.setOnClickListener(v -> showSelectionDialog(setting));
                    break;
            }
            
            // Apply responsive styling
            applyResponsiveLayout();
        }
        
        private void showNumberInputDialog(PhotoGallerySetting setting) {
            EditText editText = new EditText(context);
            editText.setInputType(InputType.TYPE_CLASS_NUMBER);
            editText.setHint("أدخل المدة بالثواني");
            editText.setText(setting.getValue() != null ? setting.getValue() : "10");
            
            new AlertDialog.Builder(context)
                    .setTitle("مدة عرض الصورة")
                    .setView(editText)
                    .setPositiveButton("حفظ", (dialog, which) -> {
                        String value = editText.getText().toString();
                        if (!value.isEmpty()) {
                            if (changeListener != null) {
                                changeListener.onSettingChanged(setting, value);
                            }
                            valueTextView.setText(value + " ثانية");
                        }
                    })
                    .setNegativeButton("إلغاء", null)
                    .show();
        }
        
        private void showTimePickerDialog(PhotoGallerySetting setting) {
            Calendar calendar = Calendar.getInstance();
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            
            // Parse current value if exists
            if (setting.getValue() != null) {
                String[] timeParts = setting.getValue().split(":");
                if (timeParts.length == 2) {
                    hour = Integer.parseInt(timeParts[0]);
                    minute = Integer.parseInt(timeParts[1]);
                }
            }
            
            TimePickerDialog timePickerDialog = new TimePickerDialog(context,
                    (view, selectedHour, selectedMinute) -> {
                        String timeValue = String.format("%02d:%02d", selectedHour, selectedMinute);
                        if (changeListener != null) {
                            changeListener.onSettingChanged(setting, timeValue);
                        }
                        valueTextView.setText(timeValue);
                    }, hour, minute, true);
            
            timePickerDialog.show();
        }
        
        private void showSelectionDialog(PhotoGallerySetting setting) {
            String[] options = {"fade", "slide", "zoom", "flip"};
            String[] displayNames = {"تلاشي", "انزلاق", "تكبير", "قلب"};
            
            int selectedIndex = 0;
            if (setting.getValue() != null) {
                for (int i = 0; i < options.length; i++) {
                    if (options[i].equals(setting.getValue())) {
                        selectedIndex = i;
                        break;
                    }
                }
            }
            
            new AlertDialog.Builder(context)
                    .setTitle("اختر تأثير الانتقال")
                    .setSingleChoiceItems(displayNames, selectedIndex, (dialog, which) -> {
                        String selectedValue = options[which];
                        if (changeListener != null) {
                            changeListener.onSettingChanged(setting, selectedValue);
                        }
                        valueTextView.setText(displayNames[which]);
                        dialog.dismiss();
                    })
                    .setNegativeButton("إلغاء", null)
                    .show();
        }
        
        private String getTransitionDisplayName(String value) {
            switch (value) {
                case "fade": return "تلاشي";
                case "slide": return "انزلاق";
                case "zoom": return "تكبير";
                case "flip": return "قلب";
                default: return "تلاشي";
            }
        }
        
        private void applyResponsiveLayout() {
            ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(context);
            
            // Apply responsive padding
            int padding = ResponsiveLayoutManager.getResponsivePadding(context, screenType);
            cardView.setContentPadding(padding, padding, padding, padding);
            
            // Apply responsive text sizes
            ResponsiveLayoutManager.applyResponsiveTextSize(context, titleTextView, 16f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, descriptionTextView, 14f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, valueTextView, 14f);
            
            // Apply responsive icon size
            int iconSize = getResponsiveIconSize(screenType);
            ViewGroup.LayoutParams iconParams = iconImageView.getLayoutParams();
            iconParams.width = iconSize;
            iconParams.height = iconSize;
            iconImageView.setLayoutParams(iconParams);
        }
        
        private int getResponsiveIconSize(ScreenUtils.ScreenType screenType) {
            switch (screenType) {
                case PHONE_PORTRAIT:
                case PHONE_LANDSCAPE:
                    return ScreenUtils.dpToPx(context, 40);
                case TABLET_PORTRAIT:
                case TABLET_LANDSCAPE:
                    return ScreenUtils.dpToPx(context, 48);
                case TV:
                    return ScreenUtils.dpToPx(context, 56);
                default:
                    return ScreenUtils.dpToPx(context, 40);
            }
        }
    }
}
