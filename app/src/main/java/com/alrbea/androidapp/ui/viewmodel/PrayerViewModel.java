package com.alrbea.androidapp.ui.viewmodel;

import android.content.Context;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import com.alrbea.androidapp.data.model.PrayerTimes;
import com.alrbea.androidapp.data.repository.SmartPrayerRepository;
import java.util.List;

public class PrayerViewModel extends ViewModel {
    private SmartPrayerRepository repository;

    public PrayerViewModel(Context context) {
        repository = new SmartPrayerRepository(context);
    }

    public LiveData<List<PrayerTimes>> getPrayerTimes() {
        return repository.getPrayerTimesLiveData();
    }

    public LiveData<PrayerTimes> getTodayPrayerTimes() {
        return repository.getTodayPrayerTimesLiveData();
    }

    public LiveData<String> getError() {
        return repository.getErrorLiveData();
    }

    public LiveData<Boolean> getLoading() {
        return repository.getLoadingLiveData();
    }

    public LiveData<Boolean> getNeedsRefresh() {
        return repository.getNeedsRefreshLiveData();
    }

    public void loadTodayPrayerTimes(String city) {
        repository.loadTodayPrayerTimes(city);
    }

    public void refreshData(String city) {
        repository.refreshData(city);
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        if (repository != null) {
            repository.shutdown();
        }
    }
}
