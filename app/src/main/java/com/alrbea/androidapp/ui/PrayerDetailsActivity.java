package com.alrbea.androidapp.ui;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageButton;
import android.widget.Switch;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;

import com.alrbea.androidapp.R;

public class PrayerDetailsActivity extends BaseActivity {
    
    public static final String EXTRA_PRAYER_ID = "prayer_id";
    public static final String EXTRA_PRAYER_NAME = "prayer_name";
    public static final String EXTRA_SETTING_TYPE = "setting_type";
    
    private static final String PREFS_NAME = "PrayerDetailsSettings";
    
    private Toolbar toolbar;
    private Switch switchAnnouncement;
    private TextView textIqamaTime;
    private TextView textIqamaDuration;
    private TextView textPrayerDuration;
    private TextView textDhuhaDuration;
    private TextView textPrayerPerformanceLabel;
    private CardView cardDhuhaAnnouncement;
    
    private ImageButton btnDecreaseIqama, btnIncreaseIqama;
    private ImageButton btnDecreasePrayer, btnIncreasePrayer;
    private ImageButton btnDecreaseDhuha, btnIncreaseDhuha;
    
    private SharedPreferences sharedPreferences;
    private String prayerId;
    private String prayerName;
    private String settingType;
    
    private int iqamaDuration = 10;
    private int prayerDuration = 10;
    private int dhuhaDuration = 15;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_prayer_details);
        
        // Get extras
        prayerId = getIntent().getStringExtra(EXTRA_PRAYER_ID);
        prayerName = getIntent().getStringExtra(EXTRA_PRAYER_NAME);
        settingType = getIntent().getStringExtra(EXTRA_SETTING_TYPE);
        
        initViews();
        setupToolbar();
        setupClickListeners();
        loadSavedSettings();
        updateUI();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        switchAnnouncement = findViewById(R.id.switch_announcement);
        textIqamaTime = findViewById(R.id.text_iqama_time);
        textIqamaDuration = findViewById(R.id.text_iqama_duration);
        textPrayerDuration = findViewById(R.id.text_prayer_duration);
        textDhuhaDuration = findViewById(R.id.text_dhuha_duration);
        textPrayerPerformanceLabel = findViewById(R.id.text_prayer_performance_label);
        cardDhuhaAnnouncement = findViewById(R.id.card_dhuha_announcement);
        
        btnDecreaseIqama = findViewById(R.id.btn_decrease_iqama);
        btnIncreaseIqama = findViewById(R.id.btn_increase_iqama);
        btnDecreasePrayer = findViewById(R.id.btn_decrease_prayer);
        btnIncreasePrayer = findViewById(R.id.btn_increase_prayer);
        btnDecreaseDhuha = findViewById(R.id.btn_decrease_dhuha);
        btnIncreaseDhuha = findViewById(R.id.btn_increase_dhuha);
        
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle(prayerName != null ? prayerName : "تفاصيل الصلاة");
        }
    }
    
    private void setupClickListeners() {
        // Announcement switch
        switchAnnouncement.setOnCheckedChangeListener((buttonView, isChecked) -> {
            saveAnnouncementSetting(isChecked);
        });
        
        // Iqama duration controls
        btnDecreaseIqama.setOnClickListener(v -> {
            if (iqamaDuration > 1) {
                iqamaDuration--;
                updateIqamaDuration();
                saveIqamaDuration();
            }
        });
        
        btnIncreaseIqama.setOnClickListener(v -> {
            if (iqamaDuration < 60) {
                iqamaDuration++;
                updateIqamaDuration();
                saveIqamaDuration();
            }
        });
        
        // Prayer duration controls
        btnDecreasePrayer.setOnClickListener(v -> {
            if (prayerDuration > 1) {
                prayerDuration--;
                updatePrayerDuration();
                savePrayerDuration();
            }
        });
        
        btnIncreasePrayer.setOnClickListener(v -> {
            if (prayerDuration < 60) {
                prayerDuration++;
                updatePrayerDuration();
                savePrayerDuration();
            }
        });
        
        // Dhuha duration controls
        btnDecreaseDhuha.setOnClickListener(v -> {
            if (dhuhaDuration > 1) {
                dhuhaDuration--;
                updateDhuhaDuration();
                saveDhuhaDuration();
            }
        });
        
        btnIncreaseDhuha.setOnClickListener(v -> {
            if (dhuhaDuration < 60) {
                dhuhaDuration++;
                updateDhuhaDuration();
                saveDhuhaDuration();
            }
        });
    }
    
    private void loadSavedSettings() {
        String prefix = settingType + "_" + prayerId + "_";
        
        boolean announcementEnabled = sharedPreferences.getBoolean(prefix + "announcement", true);
        switchAnnouncement.setChecked(announcementEnabled);
        
        iqamaDuration = sharedPreferences.getInt(prefix + "iqama_duration", 10);
        prayerDuration = sharedPreferences.getInt(prefix + "prayer_duration", 10);
        dhuhaDuration = sharedPreferences.getInt(prefix + "dhuha_duration", 15);
    }
    
    private void updateUI() {
        // Show/hide Dhuha announcement card based on prayer type
        if ("fajr".equals(prayerId)) {
            cardDhuhaAnnouncement.setVisibility(View.VISIBLE);
        } else {
            cardDhuhaAnnouncement.setVisibility(View.GONE);
        }
        
        // Update prayer performance label based on prayer and day
        updatePrayerPerformanceLabel();
        
        // Update duration displays
        updateIqamaDuration();
        updatePrayerDuration();
        updateDhuhaDuration();
        
        // Set sample iqama time (this would be calculated based on prayer times)
        textIqamaTime.setText("12:00 AM");
    }
    
    private void updatePrayerPerformanceLabel() {
        String label = "الوقت المستغرق لأداء صلاة " + prayerName;
        if ("fajr".equals(prayerId)) {
            label += " يوم الجمعة";
        }
        textPrayerPerformanceLabel.setText(label);
    }
    
    private void updateIqamaDuration() {
        textIqamaDuration.setText(String.valueOf(iqamaDuration));
    }
    
    private void updatePrayerDuration() {
        textPrayerDuration.setText(String.valueOf(prayerDuration));
    }
    
    private void updateDhuhaDuration() {
        textDhuhaDuration.setText(String.valueOf(dhuhaDuration));
    }
    
    private void saveAnnouncementSetting(boolean enabled) {
        String prefix = settingType + "_" + prayerId + "_";
        sharedPreferences.edit()
                .putBoolean(prefix + "announcement", enabled)
                .apply();
    }
    
    private void saveIqamaDuration() {
        String prefix = settingType + "_" + prayerId + "_";
        sharedPreferences.edit()
                .putInt(prefix + "iqama_duration", iqamaDuration)
                .apply();
    }
    
    private void savePrayerDuration() {
        String prefix = settingType + "_" + prayerId + "_";
        sharedPreferences.edit()
                .putInt(prefix + "prayer_duration", prayerDuration)
                .apply();
    }
    
    private void saveDhuhaDuration() {
        String prefix = settingType + "_" + prayerId + "_";
        sharedPreferences.edit()
                .putInt(prefix + "dhuha_duration", dhuhaDuration)
                .apply();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
