package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.SettingsCategory;
import com.alrbea.androidapp.utils.LocaleHelper;
import com.alrbea.androidapp.utils.ResponsiveLayoutManager;
import com.alrbea.androidapp.utils.ScreenUtils;

import java.util.List;

public class SettingsCategoryAdapter extends RecyclerView.Adapter<SettingsCategoryAdapter.CategoryViewHolder> {
    
    public interface OnCategoryClickListener {
        void onCategoryClick(SettingsCategory category);
    }
    
    private List<SettingsCategory> categories;
    private OnCategoryClickListener clickListener;
    private Context context;

    public SettingsCategoryAdapter(List<SettingsCategory> categories, OnCategoryClickListener clickListener) {
        this.categories = categories;
        this.clickListener = clickListener;
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context)
                .inflate(R.layout.item_settings_category, parent, false);
        return new CategoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        SettingsCategory category = categories.get(position);
        holder.bind(category);
    }

    @Override
    public int getItemCount() {
        return categories.size();
    }

    class CategoryViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private ImageView iconImageView;
        private TextView titleTextView;
        private TextView countTextView;

        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_category);
            iconImageView = itemView.findViewById(R.id.iv_category_icon);
            titleTextView = itemView.findViewById(R.id.tv_category_title);
            countTextView = itemView.findViewById(R.id.tv_category_count);
        }

        public void bind(SettingsCategory category) {
            String language = LocaleHelper.getLanguage(context);
            
            // Set category data
            iconImageView.setImageResource(category.getIconRes());
            titleTextView.setText(category.getDisplayTitle(language));
            
            // Show item count if available
            if (category.getItems() != null && !category.getItems().isEmpty()) {
                countTextView.setVisibility(View.VISIBLE);
                countTextView.setText(String.valueOf(category.getItems().size()));
            } else {
                countTextView.setVisibility(View.GONE);
            }
            
            // Apply responsive styling
            applyResponsiveLayout();
            
            // Set click listener
            cardView.setOnClickListener(v -> {
                if (clickListener != null) {
                    clickListener.onCategoryClick(category);
                }
            });
        }
        
        private void applyResponsiveLayout() {
            ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(context);

            // Apply responsive text sizes
            ResponsiveLayoutManager.applyResponsiveTextSize(context, titleTextView, 16f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, countTextView, 12f);

            // Apply responsive card elevation
            float elevation = getResponsiveElevation(screenType);
            cardView.setCardElevation(elevation);
        }
        
        private float getResponsiveElevation(ScreenUtils.ScreenType screenType) {
            switch (screenType) {
                case PHONE_PORTRAIT:
                case PHONE_LANDSCAPE:
                    return ScreenUtils.dpToPx(context, 4);
                case TABLET_PORTRAIT:
                case TABLET_LANDSCAPE:
                    return ScreenUtils.dpToPx(context, 6);
                case TV:
                    return ScreenUtils.dpToPx(context, 8);
                default:
                    return ScreenUtils.dpToPx(context, 4);
            }
        }
    }
}
