package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.content.SharedPreferences;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.ui.adapter.DialogSoundAdapter;

import java.util.ArrayList;
import java.util.List;

public class NotificationSettingsActivity extends BaseActivity {

    private Toolbar toolbar;
    private Switch switchAzanEnabled;
    private Switch switchIqamaEnabled;
    private LinearLayout layoutAzanSound;
    private LinearLayout layoutIqamaSound;
    private TextView textSelectedAzan;
    private TextView textSelectedIqama;

    private SharedPreferences sharedPreferences;
    private static final String PREFS_NAME = "NotificationSettings";
    private static final String KEY_AZAN_ENABLED = "azan_enabled";
    private static final String KEY_IQAMA_ENABLED = "iqama_enabled";
    private static final String KEY_SELECTED_AZAN = "selected_azan";
    private static final String KEY_SELECTED_IQAMA = "selected_iqama";

    private MediaPlayer mediaPlayer;
    private List<SoundItem> azanSounds = new ArrayList<>();
    private List<SoundItem> iqamaSounds = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_notification_settings);

        initViews();
        setupToolbar();
        loadSounds();
        loadSettings();
        setupClickListeners();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        switchAzanEnabled = findViewById(R.id.switch_azan_enabled);
        switchIqamaEnabled = findViewById(R.id.switch_iqama_enabled);
        layoutAzanSound = findViewById(R.id.layout_azan_sound);
        layoutIqamaSound = findViewById(R.id.layout_iqama_sound);
        textSelectedAzan = findViewById(R.id.text_selected_azan);
        textSelectedIqama = findViewById(R.id.text_selected_iqama);

        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("إعدادات التنبيهات");
        }
    }

    private void loadSettings() {
        boolean azanEnabled = sharedPreferences.getBoolean(KEY_AZAN_ENABLED, true);
        boolean iqamaEnabled = sharedPreferences.getBoolean(KEY_IQAMA_ENABLED, true);
        String selectedAzan = sharedPreferences.getString(KEY_SELECTED_AZAN, "الشيخ محمد رفعت");
        String selectedIqama = sharedPreferences.getString(KEY_SELECTED_IQAMA, "النغمة الافتراضية");

        switchAzanEnabled.setChecked(azanEnabled);
        switchIqamaEnabled.setChecked(iqamaEnabled);
        textSelectedAzan.setText(selectedAzan);
        textSelectedIqama.setText(selectedIqama);

        updateLayoutsVisibility();
    }

    private void setupClickListeners() {
        switchAzanEnabled.setOnCheckedChangeListener((buttonView, isChecked) -> {
            saveAzanEnabled(isChecked);
            updateLayoutsVisibility();
        });

        switchIqamaEnabled.setOnCheckedChangeListener((buttonView, isChecked) -> {
            saveIqamaEnabled(isChecked);
            updateLayoutsVisibility();
        });

        layoutAzanSound.setOnClickListener(v -> {
            if (switchAzanEnabled.isChecked()) {
                openAzanSoundSelector();
            }
        });

        layoutIqamaSound.setOnClickListener(v -> {
            if (switchIqamaEnabled.isChecked()) {
                openIqamaSoundSelector();
            }
        });
    }

    private void updateLayoutsVisibility() {
        float azanAlpha = switchAzanEnabled.isChecked() ? 1.0f : 0.5f;
        float iqamaAlpha = switchIqamaEnabled.isChecked() ? 1.0f : 0.5f;

        layoutAzanSound.setAlpha(azanAlpha);
        layoutIqamaSound.setAlpha(iqamaAlpha);

        layoutAzanSound.setEnabled(switchAzanEnabled.isChecked());
        layoutIqamaSound.setEnabled(switchIqamaEnabled.isChecked());
    }

    private void openAzanSoundSelector() {
        showSoundSelectionDialog("اختيار نغمة الأذان", azanSounds, textSelectedAzan.getText().toString(),
            selectedSound -> {
                textSelectedAzan.setText(selectedSound.getName());
                saveSelectedAzan(selectedSound.getName());
            });
    }

    private void openIqamaSoundSelector() {
        showSoundSelectionDialog("اختيار نغمة الإقامة", iqamaSounds, textSelectedIqama.getText().toString(),
            selectedSound -> {
                textSelectedIqama.setText(selectedSound.getName());
                saveSelectedIqama(selectedSound.getName());
            });
    }



    private void saveAzanEnabled(boolean enabled) {
        sharedPreferences.edit().putBoolean(KEY_AZAN_ENABLED, enabled).apply();
    }

    private void saveIqamaEnabled(boolean enabled) {
        sharedPreferences.edit().putBoolean(KEY_IQAMA_ENABLED, enabled).apply();
    }

    private void saveSelectedAzan(String azan) {
        sharedPreferences.edit().putString(KEY_SELECTED_AZAN, azan).apply();
    }

    private void saveSelectedIqama(String iqama) {
        sharedPreferences.edit().putString(KEY_SELECTED_IQAMA, iqama).apply();
    }

    private void loadSounds() {
        // Load Azan sounds
        azanSounds.clear();
        azanSounds.add(new SoundItem("الشيخ محمد رفعت", "azan_rifaat", 0));
        azanSounds.add(new SoundItem("الشيخ عبد الباسط عبد الصمد", "azan_abdulbasit", 0));
        azanSounds.add(new SoundItem("الشيخ محمد صديق المنشاوي", "azan_minshawi", 0));
        azanSounds.add(new SoundItem("الشيخ مشاري العفاسي", "azan_afasy", 0));
        azanSounds.add(new SoundItem("الشيخ ماهر المعيقلي", "azan_maher", 0));
        azanSounds.add(new SoundItem("النغمة الافتراضية", "azan_default", 0));

        // Load Iqama sounds
        iqamaSounds.clear();
        iqamaSounds.add(new SoundItem("النغمة الافتراضية", "iqama_default", 0));
        iqamaSounds.add(new SoundItem("نغمة الإقامة الكلاسيكية", "iqama_classic", 0));
        iqamaSounds.add(new SoundItem("نغمة الإقامة الحديثة", "iqama_modern", 0));
    }

    private void showSoundSelectionDialog(String title, List<SoundItem> sounds, String currentSelection, OnSoundSelectedListener listener) {
        // Inflate custom dialog layout
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_sound_selection, null);

        TextView dialogTitle = dialogView.findViewById(R.id.dialog_title);
        RecyclerView recyclerSounds = dialogView.findViewById(R.id.recycler_dialog_sounds);
        Button btnCancel = dialogView.findViewById(R.id.btn_cancel);
        Button btnConfirm = dialogView.findViewById(R.id.btn_confirm);

        dialogTitle.setText(title);

        // Setup RecyclerView
        recyclerSounds.setLayoutManager(new LinearLayoutManager(this));
        DialogSoundAdapter adapter = new DialogSoundAdapter(sounds, new DialogSoundAdapter.OnSoundActionListener() {
            @Override
            public void onSoundSelected(SoundItem soundItem, int position) {
                btnConfirm.setEnabled(true);
            }

            @Override
            public void onSoundPlay(SoundItem soundItem, int position) {
                playSound(soundItem.getResourceId());
            }

            @Override
            public void onSoundStop() {
                stopSound();
            }
        }, currentSelection);

        recyclerSounds.setAdapter(adapter);

        // Create dialog
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setView(dialogView);
        builder.setCancelable(true);

        AlertDialog dialog = builder.create();

        // Setup button listeners
        btnCancel.setOnClickListener(v -> {
            stopSound();
            dialog.dismiss();
        });

        btnConfirm.setOnClickListener(v -> {
            SoundItem selectedSound = adapter.getSelectedSound();
            if (selectedSound != null && listener != null) {
                listener.onSoundSelected(selectedSound);
            }
            stopSound();
            dialog.dismiss();
        });

        // Stop sound when dialog is dismissed
        dialog.setOnDismissListener(d -> stopSound());

        dialog.show();
    }

    private void playSound(int resourceId) {
        try {
            stopSound(); // Stop any currently playing sound

            if (resourceId != 0) {
                mediaPlayer = MediaPlayer.create(this, resourceId);
                if (mediaPlayer != null) {
                    mediaPlayer.setOnCompletionListener(mp -> stopSound());
                    mediaPlayer.start();
                }
            } else {
                // Show message that sound file is not available
                Toast.makeText(this, "ملف الصوت غير متوفر حالياً", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Toast.makeText(this, "خطأ في تشغيل الصوت", Toast.LENGTH_SHORT).show();
        }
    }

    private void stopSound() {
        if (mediaPlayer != null) {
            try {
                if (mediaPlayer.isPlaying()) {
                    mediaPlayer.stop();
                }
                mediaPlayer.release();
            } catch (Exception e) {
                // Handle error
            } finally {
                mediaPlayer = null;
            }
        }
    }

    @Override
    protected void onDestroy() {
        stopSound();
        super.onDestroy();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    // Interface for sound selection callback
    public interface OnSoundSelectedListener {
        void onSoundSelected(SoundItem soundItem);
    }

    // Inner class for sound items
    public static class SoundItem {
        private String name;
        private String id;
        private int resourceId;

        public SoundItem(String name, String id, int resourceId) {
            this.name = name;
            this.id = id;
            this.resourceId = resourceId;
        }

        public String getName() {
            return name;
        }

        public String getId() {
            return id;
        }

        public int getResourceId() {
            return resourceId;
        }
    }
}
