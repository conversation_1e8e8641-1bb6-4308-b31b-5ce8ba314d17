package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.Gallery;

import java.io.File;
import java.util.List;

public class GalleryAdapter extends RecyclerView.Adapter<GalleryAdapter.GalleryViewHolder> {
    
    public interface OnGalleryActionListener {
        void onGalleryToggle(Gallery gallery, boolean enabled);
        void onGalleryEdit(Gallery gallery);
        void onGalleryDelete(Gallery gallery);
    }
    
    private List<Gallery> galleries;
    private OnGalleryActionListener actionListener;
    private Context context;

    public GalleryAdapter(List<Gallery> galleries, OnGalleryActionListener actionListener) {
        this.galleries = galleries;
        this.actionListener = actionListener;
    }

    @NonNull
    @Override
    public GalleryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context)
                .inflate(R.layout.item_gallery, parent, false);
        return new GalleryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull GalleryViewHolder holder, int position) {
        Gallery gallery = galleries.get(position);
        holder.bind(gallery);
    }

    @Override
    public int getItemCount() {
        return galleries.size();
    }
    
    public void updateGalleries(List<Gallery> newGalleries) {
        this.galleries = newGalleries;
        notifyDataSetChanged();
    }
    
    public void addGallery(Gallery gallery) {
        galleries.add(gallery);
        notifyItemInserted(galleries.size() - 1);
    }
    
    public void removeGallery(int position) {
        if (position >= 0 && position < galleries.size()) {
            galleries.remove(position);
            notifyItemRemoved(position);
        }
    }
    
    public void updateGallery(int position, Gallery gallery) {
        if (position >= 0 && position < galleries.size()) {
            galleries.set(position, gallery);
            notifyItemChanged(position);
        }
    }

    class GalleryViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private ImageView imageGalleryPreview;
        private TextView textGalleryName;
        private TextView textGalleryInfo;
        private Switch switchGalleryEnabled;
        private Button btnEditGallery;
        private Button btnDeleteGallery;

        public GalleryViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = (CardView) itemView;
            imageGalleryPreview = itemView.findViewById(R.id.image_gallery_preview);
            textGalleryName = itemView.findViewById(R.id.text_gallery_name);
            textGalleryInfo = itemView.findViewById(R.id.text_gallery_info);
            switchGalleryEnabled = itemView.findViewById(R.id.switch_gallery_enabled);
            btnEditGallery = itemView.findViewById(R.id.btn_edit_gallery);
            btnDeleteGallery = itemView.findViewById(R.id.btn_delete_gallery);
        }

        public void bind(Gallery gallery) {
            // Set gallery data
            textGalleryName.setText(gallery.getName());
            
            // Set gallery info
            String info = gallery.getImageCount() + " صور";
            if (gallery.getGalleryDuration() > 0) {
                info += " • " + gallery.getGalleryDuration() + " ثانية";
            }
            textGalleryInfo.setText(info);
            
            // Set enabled state
            switchGalleryEnabled.setOnCheckedChangeListener(null);
            switchGalleryEnabled.setChecked(gallery.isEnabled());
            switchGalleryEnabled.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (actionListener != null) {
                    actionListener.onGalleryToggle(gallery, isChecked);
                }
            });
            
            // Load preview image
            loadPreviewImage(gallery);
            
            // Set click listeners
            btnEditGallery.setOnClickListener(v -> {
                if (actionListener != null) {
                    actionListener.onGalleryEdit(gallery);
                }
            });
            
            btnDeleteGallery.setOnClickListener(v -> {
                if (actionListener != null) {
                    actionListener.onGalleryDelete(gallery);
                }
            });
            
            // Update card appearance based on enabled state
            updateCardAppearance(gallery.isEnabled());
        }
        
        private void loadPreviewImage(Gallery gallery) {
            String firstImagePath = gallery.getFirstImagePath();
            if (firstImagePath != null && !firstImagePath.isEmpty()) {
                // Load image using Uri (simple approach without Glide)
                try {
                    Uri imageUri = Uri.fromFile(new File(firstImagePath));
                    imageGalleryPreview.setImageURI(imageUri);
                } catch (Exception e) {
                    imageGalleryPreview.setImageResource(R.drawable.ic_gallery);
                }
            } else {
                // Show default gallery icon
                imageGalleryPreview.setImageResource(R.drawable.ic_gallery);
            }
        }
        
        private void updateCardAppearance(boolean enabled) {
            float alpha = enabled ? 1.0f : 0.6f;
            cardView.setAlpha(alpha);
            
            // Update button states
            btnEditGallery.setEnabled(enabled);
            btnDeleteGallery.setEnabled(true); // Always allow deletion
        }
    }
}
