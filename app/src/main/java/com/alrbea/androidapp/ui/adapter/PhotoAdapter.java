package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;

import java.io.File;
import java.util.List;

public class PhotoAdapter extends RecyclerView.Adapter<PhotoAdapter.PhotoViewHolder> {
    
    public interface OnPhotoActionListener {
        void onPhotoDelete(String imagePath);
    }
    
    private List<String> imagePaths;
    private OnPhotoActionListener actionListener;
    private Context context;

    public PhotoAdapter(List<String> imagePaths, OnPhotoActionListener actionListener) {
        this.imagePaths = imagePaths;
        this.actionListener = actionListener;
    }

    @NonNull
    @Override
    public PhotoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context)
                .inflate(R.layout.item_photo, parent, false);
        return new PhotoViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PhotoViewHolder holder, int position) {
        String imagePath = imagePaths.get(position);
        holder.bind(imagePath);
    }

    @Override
    public int getItemCount() {
        return imagePaths.size();
    }
    
    public void updatePhotos(List<String> newImagePaths) {
        this.imagePaths = newImagePaths;
        notifyDataSetChanged();
    }
    
    public void addPhoto(String imagePath) {
        imagePaths.add(imagePath);
        notifyItemInserted(imagePaths.size() - 1);
    }
    
    public void removePhoto(String imagePath) {
        int position = imagePaths.indexOf(imagePath);
        if (position != -1) {
            imagePaths.remove(position);
            notifyItemRemoved(position);
        }
    }

    class PhotoViewHolder extends RecyclerView.ViewHolder {
        private ImageView imagePhoto;
        private ImageButton btnDeletePhoto;

        public PhotoViewHolder(@NonNull View itemView) {
            super(itemView);
            imagePhoto = itemView.findViewById(R.id.image_photo);
            btnDeletePhoto = itemView.findViewById(R.id.btn_delete_photo);
        }

        public void bind(String imagePath) {
            // Load image using Uri (simple approach without Glide)
            try {
                Uri imageUri = Uri.fromFile(new File(imagePath));
                imagePhoto.setImageURI(imageUri);
            } catch (Exception e) {
                imagePhoto.setImageResource(R.drawable.ic_gallery);
            }

            // Set delete button click listener
            btnDeletePhoto.setOnClickListener(v -> {
                if (actionListener != null) {
                    actionListener.onPhotoDelete(imagePath);
                }
            });
        }
    }
}
