package com.alrbea.androidapp.ui.viewmodel;

import android.app.Application;
import android.content.SharedPreferences;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.alrbea.androidapp.data.model.ThemeTemplate;
import com.alrbea.androidapp.data.repository.ThemeRepository;

import java.util.List;

public class ThemeViewModel extends AndroidViewModel {
    private final ThemeRepository repository;
    private final MutableLiveData<ThemeTemplate> selectedTheme = new MutableLiveData<>();
    private final SharedPreferences preferences;

    public ThemeViewModel(@NonNull Application application) {
        super(application);
        repository = new ThemeRepository(application);
        preferences = application.getSharedPreferences("ThemeTemplateSettings", Application.MODE_PRIVATE);
        loadCurrentTheme();
    }

    public LiveData<ThemeTemplate> getCurrentTheme() {
        return repository.getCurrentThemeLiveData();
    }

    public LiveData<List<ThemeTemplate>> getAvailableThemes() {
        return repository.getThemesLiveData();
    }

    public LiveData<Boolean> getLoadingState() {
        return repository.getLoadingLiveData();
    }

    public LiveData<String> getErrorMessage() {
        return repository.getErrorLiveData();
    }

    public void selectTheme(ThemeTemplate theme) {
        repository.setCurrentTheme(theme);
        selectedTheme.setValue(theme);
    }

    public void loadCurrentTheme() {
        repository.loadCurrentTheme();
    }

    public void saveThemePreference(String themeId) {
        preferences.edit().putString("selected_theme", themeId).apply();
        loadCurrentTheme();
    }
}