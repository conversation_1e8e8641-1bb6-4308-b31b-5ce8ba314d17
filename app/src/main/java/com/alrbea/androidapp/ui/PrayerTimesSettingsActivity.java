package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Toast;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.PrayerTimesSetting;
import com.alrbea.androidapp.ui.adapter.PrayerTimesSettingsAdapter;
import com.alrbea.androidapp.utils.ResponsiveItemDecoration;
import com.alrbea.androidapp.utils.ScreenUtils;

import java.util.ArrayList;
import java.util.List;

public class PrayerTimesSettingsActivity extends BaseActivity {
    
    private static final String PREFS_NAME = "PrayerTimesSettings";
    
    private Toolbar toolbar;
    private CardView cardRamadanSettings;
    private CardView cardNormalSettings;
    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_prayer_times_settings);
        
        initViews();
        setupToolbar();
        setupCardClickListeners();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        cardRamadanSettings = findViewById(R.id.card_ramadan_settings);
        cardNormalSettings = findViewById(R.id.card_normal_settings);
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("إعدادات أوقات الصلاة");
        }
    }
    
    private void setupCardClickListeners() {
        cardRamadanSettings.setOnClickListener(v -> {
            Intent intent = new Intent(this, PrayerListActivity.class);
            intent.putExtra(PrayerListActivity.EXTRA_SETTING_TYPE, PrayerListActivity.TYPE_RAMADAN);
            startActivity(intent);
        });

        cardNormalSettings.setOnClickListener(v -> {
            Intent intent = new Intent(this, PrayerListActivity.class);
            intent.putExtra(PrayerListActivity.EXTRA_SETTING_TYPE, PrayerListActivity.TYPE_NORMAL);
            startActivity(intent);
        });
    }


    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }


}
