package com.alrbea.androidapp.ui;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.AppUpdateInfo;
import com.alrbea.androidapp.utils.AppUpdateManager;

import java.io.File;

public class AppUpdateActivity extends BaseActivity implements 
        AppUpdateManager.UpdateCheckListener, AppUpdateManager.DownloadProgressListener {
    
    private static final int STORAGE_PERMISSION_REQUEST = 1001;
    
    private Toolbar toolbar;
    private CardView cardCheckingUpdate;
    private CardView cardUpdateAvailable;
    private CardView cardNoUpdate;
    private CardView cardDownloading;
    
    private TextView tvCurrentVersion;
    private TextView tvUpdateTitle;
    private TextView tvUpdateDescription;
    private TextView tvUpdateChangelog;
    private TextView tvUpdateSize;
    private TextView tvUpdateDate;
    private TextView tvDownloadProgress;
    private TextView tvDownloadSpeed;
    private TextView tvDownloadStatus;
    
    private Button btnCheckUpdate;
    private Button btnDownloadUpdate;
    private Button btnInstallUpdate;
    private Button btnCancelDownload;
    
    private ProgressBar progressBarCheck;
    private ProgressBar progressBarDownload;
    
    private AppUpdateManager updateManager;
    private AppUpdateInfo currentUpdateInfo;
    private File downloadedApkFile;
    
    private long downloadStartTime;
    private long lastDownloadedBytes;
    private long lastSpeedUpdateTime;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_app_update);
        
        initViews();
        setupToolbar();
        setupUpdateManager();
        setupClickListeners();
        
        showCurrentVersion();
        checkForUpdatesAutomatically();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        cardCheckingUpdate = findViewById(R.id.card_checking_update);
        cardUpdateAvailable = findViewById(R.id.card_update_available);
        cardNoUpdate = findViewById(R.id.card_no_update);
        cardDownloading = findViewById(R.id.card_downloading);
        
        tvCurrentVersion = findViewById(R.id.tv_current_version);
        tvUpdateTitle = findViewById(R.id.tv_update_title);
        tvUpdateDescription = findViewById(R.id.tv_update_description);
        tvUpdateChangelog = findViewById(R.id.tv_update_changelog);
        tvUpdateSize = findViewById(R.id.tv_update_size);
        tvUpdateDate = findViewById(R.id.tv_update_date);
        tvDownloadProgress = findViewById(R.id.tv_download_progress);
        tvDownloadSpeed = findViewById(R.id.tv_download_speed);
        tvDownloadStatus = findViewById(R.id.tv_download_status);
        
        btnCheckUpdate = findViewById(R.id.btn_check_update);
        btnDownloadUpdate = findViewById(R.id.btn_download_update);
        btnInstallUpdate = findViewById(R.id.btn_install_update);
        btnCancelDownload = findViewById(R.id.btn_cancel_download);
        
        progressBarCheck = findViewById(R.id.progress_bar_check);
        progressBarDownload = findViewById(R.id.progress_bar_download);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("تحديث التطبيق");
        }
    }
    
    private void setupUpdateManager() {
        updateManager = new AppUpdateManager(this);
        updateManager.setUpdateCheckListener(this);
        updateManager.setDownloadProgressListener(this);
    }
    
    private void setupClickListeners() {
        btnCheckUpdate.setOnClickListener(v -> checkForUpdates());
        btnDownloadUpdate.setOnClickListener(v -> downloadUpdate());
        btnInstallUpdate.setOnClickListener(v -> installUpdate());
        btnCancelDownload.setOnClickListener(v -> cancelDownload());
    }
    
    private void showCurrentVersion() {
        try {
            String versionName = getPackageManager()
                .getPackageInfo(getPackageName(), 0).versionName;
            int versionCode = updateManager.getCurrentVersionCode();
            tvCurrentVersion.setText("الإصدار الحالي: " + versionName + " (" + versionCode + ")");
        } catch (Exception e) {
            tvCurrentVersion.setText("الإصدار الحالي: غير معروف");
        }
    }
    
    private void checkForUpdatesAutomatically() {
        if (updateManager.isNetworkAvailable()) {
            checkForUpdates();
        } else {
            showNoInternetMessage();
        }
    }
    
    private void checkForUpdates() {
        if (!updateManager.isNetworkAvailable()) {
            showNoInternetMessage();
            return;
        }
        
        showCheckingUpdateCard();
        updateManager.checkForUpdates();
    }
    
    private void downloadUpdate() {
        if (currentUpdateInfo == null) return;
        
        // Check storage permission
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE},
                STORAGE_PERMISSION_REQUEST);
            return;
        }
        
        if (!updateManager.isNetworkAvailable()) {
            showNoInternetMessage();
            return;
        }
        
        showDownloadingCard();
        downloadStartTime = System.currentTimeMillis();
        lastDownloadedBytes = 0;
        lastSpeedUpdateTime = System.currentTimeMillis();
        
        updateManager.downloadUpdate(currentUpdateInfo);
    }
    
    private void installUpdate() {
        if (downloadedApkFile != null && downloadedApkFile.exists()) {
            updateManager.installUpdate(downloadedApkFile);
        }
    }
    
    private void cancelDownload() {
        // TODO: Implement download cancellation
        showUpdateAvailableCard();
    }
    
    private void showCheckingUpdateCard() {
        hideAllCards();
        cardCheckingUpdate.setVisibility(View.VISIBLE);
        progressBarCheck.setVisibility(View.VISIBLE);
    }
    
    private void showUpdateAvailableCard() {
        hideAllCards();
        cardUpdateAvailable.setVisibility(View.VISIBLE);
        
        if (currentUpdateInfo != null) {
            tvUpdateTitle.setText(currentUpdateInfo.getTitle());
            tvUpdateDescription.setText(currentUpdateInfo.getDescription());
            tvUpdateChangelog.setText(currentUpdateInfo.getChangelog());
            tvUpdateSize.setText("حجم التحديث: " + currentUpdateInfo.getFileSizeFormatted());
            tvUpdateDate.setText("تاريخ الإصدار: " + currentUpdateInfo.getReleaseDate());
            
            btnDownloadUpdate.setText(currentUpdateInfo.isForced() ? "تحديث إجباري" : "تحميل التحديث");
        }
    }
    
    private void showNoUpdateCard() {
        hideAllCards();
        cardNoUpdate.setVisibility(View.VISIBLE);
    }
    
    private void showDownloadingCard() {
        hideAllCards();
        cardDownloading.setVisibility(View.VISIBLE);
        progressBarDownload.setProgress(0);
        tvDownloadProgress.setText("0%");
        tvDownloadSpeed.setText("سرعة التحميل: --");
        tvDownloadStatus.setText("جاري التحميل...");
    }
    
    private void hideAllCards() {
        cardCheckingUpdate.setVisibility(View.GONE);
        cardUpdateAvailable.setVisibility(View.GONE);
        cardNoUpdate.setVisibility(View.GONE);
        cardDownloading.setVisibility(View.GONE);
        progressBarCheck.setVisibility(View.GONE);
    }
    
    private void showNoInternetMessage() {
        Toast.makeText(this, "يرجى التحقق من اتصال الإنترنت", Toast.LENGTH_LONG).show();
        showNoUpdateCard();
    }
    
    private String calculateDownloadSpeed(long downloadedBytes, long timeElapsed) {
        if (timeElapsed == 0) return "-- KB/s";
        
        long bytesPerSecond = (downloadedBytes * 1000) / timeElapsed;
        
        if (bytesPerSecond < 1024) {
            return bytesPerSecond + " B/s";
        } else if (bytesPerSecond < 1024 * 1024) {
            return String.format("%.1f KB/s", bytesPerSecond / 1024.0);
        } else {
            return String.format("%.1f MB/s", bytesPerSecond / (1024.0 * 1024.0));
        }
    }
    
    // UpdateCheckListener implementation
    @Override
    public void onUpdateAvailable(AppUpdateInfo updateInfo) {
        currentUpdateInfo = updateInfo;
        showUpdateAvailableCard();
    }
    
    @Override
    public void onNoUpdateAvailable() {
        showNoUpdateCard();
    }
    
    @Override
    public void onUpdateCheckFailed(String error) {
        Toast.makeText(this, "فشل في فحص التحديثات: " + error, Toast.LENGTH_LONG).show();
        showNoUpdateCard();
    }
    
    // DownloadProgressListener implementation
    @Override
    public void onDownloadStarted() {
        tvDownloadStatus.setText("بدء التحميل...");
    }
    
    @Override
    public void onDownloadProgress(int progress, long downloadedBytes, long totalBytes) {
        progressBarDownload.setProgress(progress);
        tvDownloadProgress.setText(progress + "%");
        
        // Calculate and show download speed
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastSpeedUpdateTime > 1000) { // Update every second
            long timeElapsed = currentTime - downloadStartTime;
            String speed = calculateDownloadSpeed(downloadedBytes, timeElapsed);
            tvDownloadSpeed.setText("سرعة التحميل: " + speed);
            
            lastDownloadedBytes = downloadedBytes;
            lastSpeedUpdateTime = currentTime;
        }
        
        tvDownloadStatus.setText("جاري التحميل... " + formatBytes(downloadedBytes) + " / " + formatBytes(totalBytes));
    }
    
    @Override
    public void onDownloadCompleted(File apkFile) {
        downloadedApkFile = apkFile;
        tvDownloadStatus.setText("تم التحميل بنجاح!");
        btnInstallUpdate.setVisibility(View.VISIBLE);
        btnCancelDownload.setVisibility(View.GONE);
        
        Toast.makeText(this, "تم تحميل التحديث بنجاح", Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void onDownloadFailed(String error) {
        Toast.makeText(this, "فشل في تحميل التحديث: " + error, Toast.LENGTH_LONG).show();
        showUpdateAvailableCard();
    }
    
    @Override
    public void onDownloadPaused() {
        tvDownloadStatus.setText("تم إيقاف التحميل مؤقتاً");
    }
    
    @Override
    public void onDownloadResumed() {
        tvDownloadStatus.setText("تم استئناف التحميل...");
    }
    
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == STORAGE_PERMISSION_REQUEST) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                downloadUpdate();
            } else {
                Toast.makeText(this, "يجب منح إذن التخزين لتحميل التحديث", Toast.LENGTH_LONG).show();
            }
        }
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
