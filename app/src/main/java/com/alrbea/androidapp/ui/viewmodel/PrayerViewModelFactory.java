package com.alrbea.androidapp.ui.viewmodel;

import android.content.Context;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

public class PrayerViewModelFactory implements ViewModelProvider.Factory {
    private Context context;

    public PrayerViewModelFactory(Context context) {
        this.context = context;
    }

    @Override
    public <T extends ViewModel> T create(Class<T> modelClass) {
        if (modelClass.isAssignableFrom(PrayerViewModel.class)) {
            return (T) new PrayerViewModel(context);
        }
        throw new IllegalArgumentException("Unknown ViewModel class");
    }
} 