package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.PrayerTimeInfo;
import java.util.List;

public class EnhancedPrayerTimesAdapter extends RecyclerView.Adapter<EnhancedPrayerTimesAdapter.PrayerTimeViewHolder> {
    
    private List<PrayerTimeInfo> prayerTimeInfoList;
    private Context context;
    private OnPrayerTimeClickListener listener;
    
    public interface OnPrayerTimeClickListener {
        void onPrayerTimeClick(PrayerTimeInfo prayerTimeInfo);
        void onAzkarClick(PrayerTimeInfo prayerTimeInfo);
    }
    
    public EnhancedPrayerTimesAdapter(Context context, List<PrayerTimeInfo> prayerTimeInfoList) {
        this.context = context;
        this.prayerTimeInfoList = prayerTimeInfoList;
    }
    
    public void setOnPrayerTimeClickListener(OnPrayerTimeClickListener listener) {
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public PrayerTimeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_enhanced_prayer_times, parent, false);
        return new PrayerTimeViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull PrayerTimeViewHolder holder, int position) {
        PrayerTimeInfo prayerTimeInfo = prayerTimeInfoList.get(position);
        holder.bind(prayerTimeInfo);
    }
    
    @Override
    public int getItemCount() {
        return prayerTimeInfoList != null ? prayerTimeInfoList.size() : 0;
    }
    
    public void updateData(List<PrayerTimeInfo> newPrayerTimeInfoList) {
        this.prayerTimeInfoList = newPrayerTimeInfoList;
        notifyDataSetChanged();
    }
    
    class PrayerTimeViewHolder extends RecyclerView.ViewHolder {
        
        private CardView cardView;
        private TextView tvPrayerName;
        private TextView tvPrayerTime;
        private TextView tvIqamaTime;
        private TextView tvTimeUntilPrayer;
        private TextView tvTimeUntilIqama;
        private TextView tvAzkarAfterPrayer;
        private TextView tvAzkarAtAdhan;
        private TextView tvNextPrayerBadge;
        private TextView tvCurrentPrayerBadge;
        
        public PrayerTimeViewHolder(@NonNull View itemView) {
            super(itemView);
            
            cardView = itemView.findViewById(R.id.card_prayer_time);
            tvPrayerName = itemView.findViewById(R.id.tv_prayer_name);
            tvPrayerTime = itemView.findViewById(R.id.tv_prayer_time);
            tvIqamaTime = itemView.findViewById(R.id.tv_iqama_time);
            tvTimeUntilPrayer = itemView.findViewById(R.id.tv_time_until_prayer);
            tvTimeUntilIqama = itemView.findViewById(R.id.tv_time_until_iqama);
            tvAzkarAfterPrayer = itemView.findViewById(R.id.tv_azkar_after_prayer);
            tvAzkarAtAdhan = itemView.findViewById(R.id.tv_azkar_at_adhan);
            tvNextPrayerBadge = itemView.findViewById(R.id.tv_next_prayer_badge);
            tvCurrentPrayerBadge = itemView.findViewById(R.id.tv_current_prayer_badge);
            
            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        listener.onPrayerTimeClick(prayerTimeInfoList.get(position));
                    }
                }
            });
            
            tvAzkarAfterPrayer.setOnClickListener(v -> {
                if (listener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        listener.onAzkarClick(prayerTimeInfoList.get(position));
                    }
                }
            });
        }
        
        public void bind(PrayerTimeInfo prayerTimeInfo) {
            tvPrayerName.setText(prayerTimeInfo.getPrayerName());
            tvPrayerTime.setText(prayerTimeInfo.getPrayerTime());
            tvIqamaTime.setText("الإقامة: " + prayerTimeInfo.getIqamaTime());
            
            // Update time until prayer
            if (prayerTimeInfo.getTimeUntilPrayer() > 0) {
                tvTimeUntilPrayer.setText("متبقي: " + prayerTimeInfo.getFormattedTimeUntilPrayer());
                tvTimeUntilPrayer.setVisibility(View.VISIBLE);
            } else {
                tvTimeUntilPrayer.setVisibility(View.GONE);
            }
            
            // Update time until iqama
            if (prayerTimeInfo.getTimeUntilIqama() > 0) {
                tvTimeUntilIqama.setText("متبقي للإقامة: " + prayerTimeInfo.getFormattedTimeUntilIqama());
                tvTimeUntilIqama.setVisibility(View.VISIBLE);
            } else {
                tvTimeUntilIqama.setVisibility(View.GONE);
            }
            
            // Show/hide badges
            if (prayerTimeInfo.isNextPrayer()) {
                tvNextPrayerBadge.setVisibility(View.VISIBLE);
                tvNextPrayerBadge.setText("الصلاة القادمة");
                cardView.setCardBackgroundColor(context.getResources().getColor(R.color.next_prayer_background));
            } else {
                tvNextPrayerBadge.setVisibility(View.GONE);
                cardView.setCardBackgroundColor(context.getResources().getColor(R.color.card_background));
            }
            
            if (prayerTimeInfo.isCurrentPrayer()) {
                tvCurrentPrayerBadge.setVisibility(View.VISIBLE);
                tvCurrentPrayerBadge.setText("الصلاة الحالية");
                cardView.setCardBackgroundColor(context.getResources().getColor(R.color.current_prayer_background));
            } else {
                tvCurrentPrayerBadge.setVisibility(View.GONE);
            }
            
            // Set azkar text
            tvAzkarAfterPrayer.setText("أذكار بعد الصلاة: " + prayerTimeInfo.getAzkarAfterPrayer());
            tvAzkarAtAdhan.setText("أذكار عند الأذان: " + prayerTimeInfo.getAzkarAtAdhan());
        }
    }
} 