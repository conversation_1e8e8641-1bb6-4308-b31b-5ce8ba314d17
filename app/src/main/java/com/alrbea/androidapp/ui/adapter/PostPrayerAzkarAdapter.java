package com.alrbea.androidapp.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.PrayerAzkarSetting;

import java.util.List;

public class PostPrayerAzkarAdapter extends RecyclerView.Adapter<PostPrayerAzkarAdapter.ViewHolder> {
    
    private List<PrayerAzkarSetting> settings;
    private OnPrayerSettingChangeListener listener;
    
    public interface OnPrayerSettingChangeListener {
        void onPrayerSettingChanged(PrayerAzkarSetting setting, String action, Object value);
    }
    
    public PostPrayerAzkarAdapter(List<PrayerAzkarSetting> settings, OnPrayerSettingChangeListener listener) {
        this.settings = settings;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
            .inflate(R.layout.item_post_prayer_azkar, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        PrayerAzkarSetting setting = settings.get(position);
        holder.bind(setting);
    }
    
    @Override
    public int getItemCount() {
        return settings.size();
    }
    
    class ViewHolder extends RecyclerView.ViewHolder {
        private ImageView iconImageView;
        private TextView nameTextView;
        private Switch enableSwitch;
        private TextView durationTextView;
        private ImageButton decreaseButton;
        private ImageButton increaseButton;
        private TextView durationLabel;
        
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            iconImageView = itemView.findViewById(R.id.icon_prayer);
            nameTextView = itemView.findViewById(R.id.text_prayer_name);
            enableSwitch = itemView.findViewById(R.id.switch_enable);
            durationTextView = itemView.findViewById(R.id.text_duration);
            decreaseButton = itemView.findViewById(R.id.btn_decrease);
            increaseButton = itemView.findViewById(R.id.btn_increase);
            durationLabel = itemView.findViewById(R.id.text_duration_label);
        }
        
        public void bind(PrayerAzkarSetting setting) {
            iconImageView.setImageResource(setting.getIconResId());
            nameTextView.setText(setting.getName());
            enableSwitch.setChecked(setting.isEnabled());
            durationTextView.setText(String.valueOf(setting.getDuration()));
            
            // Show/hide duration controls based on enabled state
            updateDurationControlsVisibility(setting.isEnabled());
            
            // Set up listeners
            enableSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (listener != null) {
                    listener.onPrayerSettingChanged(setting, "enabled", isChecked);
                }
                updateDurationControlsVisibility(isChecked);
            });
            
            decreaseButton.setOnClickListener(v -> {
                int currentDuration = setting.getDuration();
                if (currentDuration > 1) {
                    int newDuration = currentDuration - 1;
                    durationTextView.setText(String.valueOf(newDuration));
                    if (listener != null) {
                        listener.onPrayerSettingChanged(setting, "duration", newDuration);
                    }
                }
            });
            
            increaseButton.setOnClickListener(v -> {
                int currentDuration = setting.getDuration();
                if (currentDuration < 60) { // Max 60 minutes
                    int newDuration = currentDuration + 1;
                    durationTextView.setText(String.valueOf(newDuration));
                    if (listener != null) {
                        listener.onPrayerSettingChanged(setting, "duration", newDuration);
                    }
                }
            });
        }
        
        private void updateDurationControlsVisibility(boolean enabled) {
            int visibility = enabled ? View.VISIBLE : View.GONE;
            durationTextView.setVisibility(visibility);
            decreaseButton.setVisibility(visibility);
            increaseButton.setVisibility(visibility);
            durationLabel.setVisibility(visibility);
        }
    }
}
