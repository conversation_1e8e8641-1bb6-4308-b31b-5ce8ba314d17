package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.Event;

import java.util.List;

public class EventsAdapter extends RecyclerView.Adapter<EventsAdapter.EventViewHolder> {
    
    public interface OnEventActionListener {
        void onEventToggled(Event event, boolean isEnabled);
        void onEventEdit(Event event);
        void onEventDelete(Event event);
    }
    
    private List<Event> events;
    private OnEventActionListener actionListener;
    private Context context;

    public EventsAdapter(List<Event> events, OnEventActionListener actionListener) {
        this.events = events;
        this.actionListener = actionListener;
    }

    @NonNull
    @Override
    public EventViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context).inflate(R.layout.item_event, parent, false);
        return new EventViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull EventViewHolder holder, int position) {
        Event event = events.get(position);
        holder.bind(event);
    }

    @Override
    public int getItemCount() {
        return events.size();
    }
    
    public void updateEvents(List<Event> newEvents) {
        this.events = newEvents;
        notifyDataSetChanged();
    }

    class EventViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private ImageView iconImageView;
        private TextView titleTextView;
        private TextView messageTextView;
        private TextView prayerTextView;
        private SwitchCompat enabledSwitch;
        private Button editButton;
        private Button deleteButton;

        public EventViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_event);
            iconImageView = itemView.findViewById(R.id.iv_event_icon);
            titleTextView = itemView.findViewById(R.id.tv_event_title);
            messageTextView = itemView.findViewById(R.id.tv_event_message);
            prayerTextView = itemView.findViewById(R.id.tv_event_prayer);
            enabledSwitch = itemView.findViewById(R.id.switch_event_enabled);
            editButton = itemView.findViewById(R.id.btn_edit_event);
            deleteButton = itemView.findViewById(R.id.btn_delete_event);
        }

        public void bind(Event event) {
            titleTextView.setText(event.getTitle());
            messageTextView.setText(event.getMessage());
            prayerTextView.setText(event.getPrayer());
            
            // Set switch state without triggering listener
            enabledSwitch.setOnCheckedChangeListener(null);
            enabledSwitch.setChecked(event.isEnabled());
            enabledSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (actionListener != null) {
                    actionListener.onEventToggled(event, isChecked);
                }
            });
            
            // Set click listeners
            editButton.setOnClickListener(v -> {
                if (actionListener != null) {
                    actionListener.onEventEdit(event);
                }
            });
            
            deleteButton.setOnClickListener(v -> {
                if (actionListener != null) {
                    actionListener.onEventDelete(event);
                }
            });
            
            // Card click for edit
            cardView.setOnClickListener(v -> {
                if (actionListener != null) {
                    actionListener.onEventEdit(event);
                }
            });
        }
    }
}
