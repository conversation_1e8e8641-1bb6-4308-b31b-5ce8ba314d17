package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.Prayer;
import com.alrbea.androidapp.ui.adapter.PrayerAdapter;

import java.util.ArrayList;
import java.util.List;

public class PrayerListActivity extends BaseActivity implements PrayerAdapter.OnPrayerClickListener {
    
    public static final String EXTRA_SETTING_TYPE = "setting_type";
    public static final String TYPE_RAMADAN = "ramadan";
    public static final String TYPE_NORMAL = "normal";
    
    private RecyclerView recyclerViewPrayers;
    private PrayerAdapter adapter;
    private Toolbar toolbar;
    private TextView textHeaderTitle;
    private TextView textHeaderSubtitle;
    private String settingType;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_prayer_list);
        
        settingType = getIntent().getStringExtra(EXTRA_SETTING_TYPE);
        if (settingType == null) {
            settingType = TYPE_NORMAL;
        }
        
        initViews();
        setupToolbar();
        setupRecyclerView();
        loadPrayers();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        recyclerViewPrayers = findViewById(R.id.recycler_view_prayers);
        textHeaderTitle = findViewById(R.id.text_header_title);
        textHeaderSubtitle = findViewById(R.id.text_header_subtitle);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }
        
        // Set title based on setting type
        if (TYPE_RAMADAN.equals(settingType)) {
            toolbar.setTitle("إعدادات رمضان");
            textHeaderTitle.setText("إعدادات رمضان");
            textHeaderSubtitle.setText("إعدادات خاصة بشهر رمضان المبارك");
        } else {
            toolbar.setTitle("إعدادات الأيام العادية");
            textHeaderTitle.setText("إعدادات الأيام العادية");
            textHeaderSubtitle.setText("إعدادات الأيام العادية خارج رمضان");
        }
    }
    
    private void setupRecyclerView() {
        recyclerViewPrayers.setLayoutManager(new LinearLayoutManager(this));
    }
    
    private void loadPrayers() {
        List<Prayer> prayers = createPrayersList();
        adapter = new PrayerAdapter(prayers, this);
        recyclerViewPrayers.setAdapter(adapter);
    }
    
    private List<Prayer> createPrayersList() {
        List<Prayer> prayers = new ArrayList<>();
        
        prayers.add(new Prayer("fajr", "صلاة الفجر", "Fajr Prayer", "05:30", R.drawable.ic_fajr));
        prayers.add(new Prayer("dhuhr", "صلاة الظهر", "Dhuhr Prayer", "12:15", R.drawable.ic_dhuhr));
        prayers.add(new Prayer("asr", "صلاة العصر", "Asr Prayer", "15:45", R.drawable.ic_asr));
        prayers.add(new Prayer("maghrib", "صلاة المغرب", "Maghrib Prayer", "18:20", R.drawable.ic_maghrib));
        prayers.add(new Prayer("isha", "صلاة العشاء", "Isha Prayer", "19:45", R.drawable.ic_isha));
        prayers.add(new Prayer("friday", "صلاة الجمعة", "Friday Prayer", "12:30", R.drawable.ic_friday));
        
        return prayers;
    }
    
    @Override
    public void onPrayerClick(Prayer prayer) {
        // Navigate to prayer details activity
        Intent intent = new Intent(this, PrayerDetailsActivity.class);
        intent.putExtra(PrayerDetailsActivity.EXTRA_PRAYER_ID, prayer.getId());
        intent.putExtra(PrayerDetailsActivity.EXTRA_PRAYER_NAME, prayer.getNameAr());
        intent.putExtra(PrayerDetailsActivity.EXTRA_SETTING_TYPE, settingType);
        startActivity(intent);
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
