package com.alrbea.androidapp.ui.viewmodel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import com.alrbea.androidapp.data.model.DeviceInfo;
import com.alrbea.androidapp.data.model.LoginResult;
import com.alrbea.androidapp.data.repository.AuthRepository;

public class AuthViewModel extends ViewModel {
    private AuthRepository repository;

    public AuthViewModel() {
        repository = new AuthRepository();
    }

    public LiveData<LoginResult> getLoginResult() {
        return repository.getLoginResultLiveData();
    }

    public LiveData<String> getError() {
        return repository.getErrorLiveData();
    }

    public LiveData<Boolean> getLoading() {
        return repository.getLoadingLiveData();
    }

    public void login(String activationCode, DeviceInfo deviceInfo) {
        repository.login(activationCode, deviceInfo);
    }
}
