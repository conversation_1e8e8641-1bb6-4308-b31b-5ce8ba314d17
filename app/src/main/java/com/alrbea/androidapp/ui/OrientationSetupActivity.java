package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ActivityInfo;
import android.os.Bundle;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;



import com.alrbea.androidapp.MainActivity;
import com.alrbea.androidapp.R;

public class OrientationSetupActivity extends BaseActivity {
    
    private static final String PREFS_NAME = "PrayerAppPrefs";
    private static final String KEY_ORIENTATION = "screen_orientation";
    private static final String KEY_ORIENTATION_SET = "orientation_set";
    
    private RadioGroup rgOrientation;
    private RadioButton rbPortrait;
    private RadioButton rbLandscape;
    private RadioButton rbReverseLandscape;
    private RadioButton rbSensorPortrait;
    private Button btnContinue;
    private Button btnSkip;
    private TextView tvPreview;
    
    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_orientation_setup);
        
        initViews();
        setupClickListeners();
        loadSavedOrientation();
    }
    
    private void initViews() {
        rgOrientation = findViewById(R.id.rg_orientation);
        rbPortrait = findViewById(R.id.rb_portrait);
        rbLandscape = findViewById(R.id.rb_landscape);
        rbReverseLandscape = findViewById(R.id.rb_reverse_landscape);
        rbSensorPortrait = findViewById(R.id.rb_sensor_portrait);
        btnContinue = findViewById(R.id.btn_continue);
        btnSkip = findViewById(R.id.btn_skip);
        tvPreview = findViewById(R.id.tv_preview);
        
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
    }
    
    private void setupClickListeners() {
        rgOrientation.setOnCheckedChangeListener((group, checkedId) -> {
            updatePreview(checkedId);
            applyOrientation(checkedId);
        });
        
        btnContinue.setOnClickListener(v -> {
            saveOrientationAndContinue();
        });
        
        btnSkip.setOnClickListener(v -> {
            skipOrientationSetup();
        });
    }
    
    private void loadSavedOrientation() {
        int savedOrientation = sharedPreferences.getInt(KEY_ORIENTATION, ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        
        switch (savedOrientation) {
            case ActivityInfo.SCREEN_ORIENTATION_PORTRAIT:
                rbPortrait.setChecked(true);
                break;
            case ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE:
                rbLandscape.setChecked(true);
                break;
            case ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE:
                rbReverseLandscape.setChecked(true);
                break;
            case ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT:
                rbSensorPortrait.setChecked(true);
                break;
            default:
                rbPortrait.setChecked(true);
                break;
        }
        
        updatePreview(rgOrientation.getCheckedRadioButtonId());
    }
    
    private void updatePreview(int checkedId) {
        String previewText = "";
        
        if (checkedId == R.id.rb_portrait) {
            previewText = "الشاشة ستكون في الوضع الطولي العادي";
        } else if (checkedId == R.id.rb_landscape) {
            previewText = "الشاشة ستكون في الوضع العرضي (الزر الرئيسي على اليمين)";
        } else if (checkedId == R.id.rb_reverse_landscape) {
            previewText = "الشاشة ستكون في الوضع العرضي المعكوس (الزر الرئيسي على اليسار)";
        } else if (checkedId == R.id.rb_sensor_portrait) {
            previewText = "الشاشة ستتبع اتجاه الجهاز تلقائياً (طولي فقط)";
        }
        
        tvPreview.setText(previewText);
    }
    
    private void applyOrientation(int checkedId) {
        int orientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
        
        if (checkedId == R.id.rb_portrait) {
            orientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
        } else if (checkedId == R.id.rb_landscape) {
            orientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;
        } else if (checkedId == R.id.rb_reverse_landscape) {
            orientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE;
        } else if (checkedId == R.id.rb_sensor_portrait) {
            orientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT;
        }
        
        // Apply orientation immediately for preview
        setRequestedOrientation(orientation);
    }
    
    private void saveOrientationAndContinue() {
        int checkedId = rgOrientation.getCheckedRadioButtonId();
        int orientation = getOrientationFromRadioButton(checkedId);
        
        // Save orientation preference
        sharedPreferences.edit()
            .putInt(KEY_ORIENTATION, orientation)
            .putBoolean(KEY_ORIENTATION_SET, true)
            .apply();
        
        // Navigate to main activity
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        finish();
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }
    
    private void skipOrientationSetup() {
        // Save default orientation (portrait) and mark as set
        sharedPreferences.edit()
            .putInt(KEY_ORIENTATION, ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
            .putBoolean(KEY_ORIENTATION_SET, true)
            .apply();
        
        // Navigate to main activity
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        finish();
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }
    
    private int getOrientationFromRadioButton(int checkedId) {
        if (checkedId == R.id.rb_portrait) {
            return ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
        } else if (checkedId == R.id.rb_landscape) {
            return ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;
        } else if (checkedId == R.id.rb_reverse_landscape) {
            return ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE;
        } else if (checkedId == R.id.rb_sensor_portrait) {
            return ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT;
        }
        return ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
    }
    
    @Override
    public void onBackPressed() {
        // Prevent going back to login screen
        // User must complete orientation setup or skip it
    }
}
