package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.SettingsCategory;
import com.alrbea.androidapp.ui.adapter.SettingsCategoryAdapter;
import com.alrbea.androidapp.utils.ResponsiveLayoutManager;
import com.alrbea.androidapp.utils.ResponsiveItemDecoration;
import com.alrbea.androidapp.utils.GridSpacingItemDecoration;
import com.alrbea.androidapp.utils.ScreenUtils;
import com.alrbea.androidapp.utils.SettingsDataProvider;

import java.util.List;

public class SettingsActivity extends BaseActivity {

    private RecyclerView recyclerViewCategories;
    private SettingsCategoryAdapter categoryAdapter;
    private Toolbar toolbar;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        initViews();
        setupToolbar();
        setupRecyclerView();
        loadSettingsCategories();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        recyclerViewCategories = findViewById(R.id.recycler_view_categories);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("الإعدادات");
        }
    }

    private void setupRecyclerView() {
        // Calculate columns based on screen size
        int columns = getColumnCount();

        GridLayoutManager layoutManager = new GridLayoutManager(this, columns);
        recyclerViewCategories.setLayoutManager(layoutManager);

        // Add grid spacing optimized for square items
        GridSpacingItemDecoration itemDecoration = GridSpacingItemDecoration.createForScreen(this, columns);
        recyclerViewCategories.addItemDecoration(itemDecoration);

        // Apply responsive padding
        int padding = ScreenUtils.getScreenPadding(this);
        recyclerViewCategories.setPadding(padding, padding, padding, padding);

        // Ensure items maintain square aspect ratio
        recyclerViewCategories.setHasFixedSize(true);
    }

    private int getColumnCount() {
        ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(this);

        switch (screenType) {
            case PHONE_PORTRAIT:
                return 2; // Minimum 2 columns for responsive grid
            case PHONE_LANDSCAPE:
                return 3; // Three columns in landscape for better space usage
            case TABLET_PORTRAIT:
                return 3; // Three columns for tablets
            case TABLET_LANDSCAPE:
                return 4; // Four columns for tablet landscape
            case TV:
                return 5; // Five columns for TV
            default:
                return 2; // Default minimum 2 columns
        }
    }

    private void loadSettingsCategories() {
        List<SettingsCategory> categories = SettingsDataProvider.getSettingsCategories(this);
//        RecyclerView.LayoutManager layoutManager = new GridLayoutManager(this, 2, GridLayoutManager.VERTICAL, false); // You can change 2 to any number of columns
//        recyclerViewCategories.setLayoutManager(layoutManager);
        categoryAdapter = new SettingsCategoryAdapter(categories, this::onCategoryClick);
        recyclerViewCategories.setAdapter(categoryAdapter);
    }

    private void onCategoryClick(SettingsCategory category) {
        // Navigate to specific settings screen based on category
        switch (category.getId()) {
            case "app_settings":
                Intent appSettingsIntent = new Intent(this, AppSettingsActivity.class);
                startActivity(appSettingsIntent);
                break;
            case "advertisements":
                openAdvertisementsSettings();
                break;
            case "photo_gallery":
                openPhotoGallerySettings();
                break;
            case "azkar":
                openAzkarSettings();
                break;
            case "prayer_times":
                openPrayerTimesSettings();
                break;
            case "app_update":
                checkForUpdates();
                break;
            case "about":
                openAboutScreen();
                break;
            case "restart":
                restartApp();
                break;
            case "power_saver":
                openPowerSaverSettings();
                break;
            case "data_sync":
                openDataSyncSettings();
                break;
           case "logo_design":
                openLogoDesign();
                break;
           case "remote_control":
                openRemoteControl();
                break;
        }
    }
    private void openRemoteControl() {
        Intent intent = new Intent(this, RemoteControlActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void openLogoDesign() {
        Intent intent = new Intent(this, LogoDesignActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }
    private void openAdvertisementsSettings() {
        Intent intent = new Intent(this, AdvertisementsSettingsActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void openPhotoGallerySettings() {
        Intent intent = new Intent(this, PhotoGallerySettingsActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void openAzkarSettings() {
        Intent intent = new Intent(this, AzkarSettingsActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void openPrayerTimesSettings() {
        Intent intent = new Intent(this, PrayerTimesSettingsActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void checkForUpdates() {
        Intent intent = new Intent(this, AppUpdateActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void openAboutScreen() {
        Intent intent = new Intent(this, AboutActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void restartApp() {

    }

    private void openPowerSaverSettings() {
        // TODO: Implement power saver settings
        showToast("إعدادات موفر الطاقة");
    }

    private void openDataSyncSettings() {
        Intent intent = new Intent(this, DataSyncActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void showToast(String message) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
