package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.PrayerTimes;
import com.alrbea.androidapp.utils.ResponsiveLayoutManager;
import com.alrbea.androidapp.utils.ScreenUtils;
import java.util.ArrayList;
import java.util.List;

public class PrayerTimesAdapter extends RecyclerView.Adapter<PrayerTimesAdapter.PrayerViewHolder> {
    private List<PrayerTimes> prayerTimesList;

    public PrayerTimesAdapter() {
        this.prayerTimesList = new ArrayList<>();
    }

    @NonNull
    @Override
    public PrayerViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_prayer_times, parent, false);
        return new PrayerViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PrayerViewHolder holder, int position) {
        PrayerTimes prayerTimes = prayerTimesList.get(position);
        holder.bind(prayerTimes);

        // Apply responsive styling
        holder.applyResponsiveLayout();
    }

    @Override
    public int getItemCount() {
        return prayerTimesList.size();
    }

    public void updatePrayerTimes(List<PrayerTimes> newPrayerTimes) {
        this.prayerTimesList.clear();
        this.prayerTimesList.addAll(newPrayerTimes);
        notifyDataSetChanged();
    }

    static class PrayerViewHolder extends RecyclerView.ViewHolder {
        private TextView dateTextView;
        private TextView hijriDateTextView;
        private TextView fajrTextView;
        private TextView sunriseTextView;
        private TextView dhuhrTextView;
        private TextView asrTextView;
        private TextView maghribTextView;
        private TextView ishaTextView;

        public PrayerViewHolder(@NonNull View itemView) {
            super(itemView);
            dateTextView = itemView.findViewById(R.id.tv_date);
            hijriDateTextView = itemView.findViewById(R.id.tv_hijri_date);
            fajrTextView = itemView.findViewById(R.id.tv_fajr);
            sunriseTextView = itemView.findViewById(R.id.tv_sunrise);
            dhuhrTextView = itemView.findViewById(R.id.tv_dhuhr);
            asrTextView = itemView.findViewById(R.id.tv_asr);
            maghribTextView = itemView.findViewById(R.id.tv_maghrib);
            ishaTextView = itemView.findViewById(R.id.tv_isha);
        }

        public void bind(PrayerTimes prayerTimes) {
            dateTextView.setText(prayerTimes.getDate());
            // Hijri date support: if available in model, show it, else hide
            if (prayerTimes.getClass().getDeclaredMethods() != null) {
                try {
                    String hijri = (String) prayerTimes.getClass().getMethod("getHijriDate").invoke(prayerTimes);
                    if (hijri != null && !hijri.isEmpty()) {
                        hijriDateTextView.setText(hijri);
                        hijriDateTextView.setVisibility(View.VISIBLE);
                    } else {
                        hijriDateTextView.setVisibility(View.GONE);
                    }
                } catch (Exception e) {
                    hijriDateTextView.setVisibility(View.GONE);
                }
            } else {
                hijriDateTextView.setVisibility(View.GONE);
            }
            fajrTextView.setText("الفجر: " + prayerTimes.getFajr());
            sunriseTextView.setText("الشروق: " + prayerTimes.getSunrise());
            dhuhrTextView.setText("الظهر: " + prayerTimes.getDhuhr());
            asrTextView.setText("العصر: " + prayerTimes.getAsr());
            maghribTextView.setText("المغرب: " + prayerTimes.getMaghrib());
            ishaTextView.setText("العشاء: " + prayerTimes.getIsha());
        }

        public void applyResponsiveLayout() {
            Context context = itemView.getContext();
            ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(context);

            // Apply responsive padding
            int padding = ResponsiveLayoutManager.getResponsivePadding(context, screenType);
            itemView.setPadding(padding, padding, padding, padding);

            // Apply responsive text sizes
            float textMultiplier = ScreenUtils.getTextSizeMultiplier(context);

            ResponsiveLayoutManager.applyResponsiveTextSize(context, dateTextView, 18f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, fajrTextView, 14f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, sunriseTextView, 14f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, dhuhrTextView, 14f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, asrTextView, 14f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, maghribTextView, 14f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, ishaTextView, 14f);
        }
    }
}
