package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.view.MenuItem;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;

import com.alrbea.androidapp.R;

public class DeviceSettingsActivity extends BaseActivity {

    private Toolbar toolbar;
    private CardView cardMainDeviceSettings;
    private CardView cardNetworkSettings;
    private CardView cardDatetimeSettings;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_device_settings);

        initViews();
        setupToolbar();
        setupClickListeners();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        cardMainDeviceSettings = findViewById(R.id.card_main_device_settings);
        cardNetworkSettings = findViewById(R.id.card_network_settings);
        cardDatetimeSettings = findViewById(R.id.card_datetime_settings);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("إعدادات الجهاز");
        }
    }

    private void setupClickListeners() {
        cardMainDeviceSettings.setOnClickListener(v -> openMainDeviceSettings());
        cardNetworkSettings.setOnClickListener(v -> openNetworkSettings());
        cardDatetimeSettings.setOnClickListener(v -> openDateTimeSettings());
    }

    private void openMainDeviceSettings() {
        try {
            Intent intent = new Intent(Settings.ACTION_SETTINGS);
            startActivity(intent);
        } catch (Exception e) {
            // Handle error
        }
    }

    private void openNetworkSettings() {
        try {
            Intent intent = new Intent(Settings.ACTION_WIFI_SETTINGS);
            startActivity(intent);
        } catch (Exception e) {
            // Fallback to general settings
            try {
                Intent fallbackIntent = new Intent(Settings.ACTION_WIRELESS_SETTINGS);
                startActivity(fallbackIntent);
            } catch (Exception ex) {
                // Final fallback
                Intent finalIntent = new Intent(Settings.ACTION_SETTINGS);
                startActivity(finalIntent);
            }
        }
    }

    private void openDateTimeSettings() {
        try {
            Intent intent = new Intent(Settings.ACTION_DATE_SETTINGS);
            startActivity(intent);
        } catch (Exception e) {
            // Handle error
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
