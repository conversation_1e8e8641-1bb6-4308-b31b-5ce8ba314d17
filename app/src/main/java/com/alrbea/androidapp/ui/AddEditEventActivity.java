package com.alrbea.androidapp.ui;

import android.app.DatePickerDialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.appcompat.widget.Toolbar;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.Event;
import com.alrbea.androidapp.utils.EventsManager;
import com.google.android.material.textfield.TextInputEditText;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

public class AddEditEventActivity extends BaseActivity {
    
    public static final String EXTRA_EVENT_ID = "event_id";
    public static final int RESULT_EVENT_SAVED = 100;
    
    private Toolbar toolbar;
    private TextInputEditText editTitle;
    private TextInputEditText editMessage;
    private Spinner spinnerTemplateMessages;
    private Spinner spinnerPrayerSelection;
    private TextInputEditText editStartDate;
    private TextInputEditText editEndDate;
    private Button btnSave;
    
    private EventsManager eventsManager;
    private Event currentEvent;
    private SimpleDateFormat dateFormat;
    private Calendar startCalendar;
    private Calendar endCalendar;
    
    // Template messages for events
    private String[] templateMessages = {
        "اختر رسالة جاهزة...",
        "رمضان كريم وكل عام وأنتم بخير",
        "عيد الفطر المبارك - تقبل الله منا ومنكم",
        "عيد الأضحى المبارك - عيد مبارك وكل عام وأنتم بخير",
        "الجمعة المباركة - لا تنسوا صلاة الجمعة",
        "ليلة القدر المباركة - ادعوا الله في هذه الليلة المباركة",
        "يوم عرفة - يوم مغفرة الذنوب",
        "ليلة الإسراء والمعراج المباركة",
        "مولد النبي الكريم صلى الله عليه وسلم"
    };
    
    // Prayer names
    private String[] prayerNames = {
        "اختر الصلاة...",
        "الفجر",
        "الظهر", 
        "العصر",
        "المغرب",
        "العشاء",
        "الجمعة"
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_edit_event);
        
        initializeViews();
        setupToolbar();
        setupEventsManager();
        setupSpinners();
        setupDatePickers();
        setupListeners();
        loadEventData();
    }
    
    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        editTitle = findViewById(R.id.edit_event_title);
        editMessage = findViewById(R.id.edit_event_message);
        spinnerTemplateMessages = findViewById(R.id.spinner_template_messages);
        spinnerPrayerSelection = findViewById(R.id.spinner_prayer_selection);
        editStartDate = findViewById(R.id.edit_start_date);
        editEndDate = findViewById(R.id.edit_end_date);
        btnSave = findViewById(R.id.btn_save);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }
    }
    
    private void setupEventsManager() {
        eventsManager = new EventsManager(this);
        dateFormat = new SimpleDateFormat("yyyy/MM/dd", Locale.getDefault());
        startCalendar = Calendar.getInstance();
        endCalendar = Calendar.getInstance();
        endCalendar.add(Calendar.DAY_OF_MONTH, 7); // Default to 7 days from now
    }
    
    private void setupSpinners() {
        // Setup template messages spinner
        ArrayAdapter<String> templateAdapter = new ArrayAdapter<>(
            this, 
            android.R.layout.simple_spinner_item, 
            templateMessages
        );
        templateAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerTemplateMessages.setAdapter(templateAdapter);
        
        // Setup prayer selection spinner
        ArrayAdapter<String> prayerAdapter = new ArrayAdapter<>(
            this, 
            android.R.layout.simple_spinner_item, 
            prayerNames
        );
        prayerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerPrayerSelection.setAdapter(prayerAdapter);
    }
    
    private void setupDatePickers() {
        editStartDate.setOnClickListener(v -> showDatePicker(startCalendar, editStartDate));
        editEndDate.setOnClickListener(v -> showDatePicker(endCalendar, editEndDate));
    }
    
    private void setupListeners() {
        // Template spinner listener
        spinnerTemplateMessages.setOnItemSelectedListener(new android.widget.AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(android.widget.AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) {
                    editMessage.setText(templateMessages[position]);
                }
            }
            
            @Override
            public void onNothingSelected(android.widget.AdapterView<?> parent) {}
        });
        
        btnSave.setOnClickListener(v -> saveEvent());
    }
    
    private void loadEventData() {
        String eventId = getIntent().getStringExtra(EXTRA_EVENT_ID);
        
        if (eventId != null) {
            // Edit mode
            currentEvent = eventsManager.getEventById(eventId);
            if (currentEvent != null) {
                toolbar.setTitle(R.string.edit_event);
                populateFields();
            } else {
                showToast("Event not found");
                finish();
            }
        } else {
            // Add mode
            toolbar.setTitle(R.string.add_event);
            currentEvent = null;
        }
    }
    
    private void populateFields() {
        if (currentEvent == null) return;
        
        editTitle.setText(currentEvent.getTitle());
        editMessage.setText(currentEvent.getMessage());
        
        if (currentEvent.getStartDate() != null) {
            editStartDate.setText(currentEvent.getStartDate());
        }
        
        if (currentEvent.getEndDate() != null) {
            editEndDate.setText(currentEvent.getEndDate());
        }
        
        // Set prayer selection
        for (int i = 0; i < prayerNames.length; i++) {
            if (prayerNames[i].equals(currentEvent.getPrayer())) {
                spinnerPrayerSelection.setSelection(i);
                break;
            }
        }
    }
    
    private void showDatePicker(Calendar calendar, TextInputEditText editText) {
        DatePickerDialog datePickerDialog = new DatePickerDialog(
            this,
            (view, year, month, dayOfMonth) -> {
                calendar.set(year, month, dayOfMonth);
                String formattedDate = dateFormat.format(calendar.getTime());
                editText.setText(formattedDate);
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        );
        datePickerDialog.show();
    }
    
    private void saveEvent() {
        String title = editTitle.getText().toString().trim();
        String message = editMessage.getText().toString().trim();
        String startDate = editStartDate.getText().toString().trim();
        String endDate = editEndDate.getText().toString().trim();
        int prayerPosition = spinnerPrayerSelection.getSelectedItemPosition();
        
        // Validation
        if (title.isEmpty()) {
            showToast(getString(R.string.please_enter_event_title));
            editTitle.requestFocus();
            return;
        }
        
        if (message.isEmpty()) {
            showToast(getString(R.string.please_enter_event_message));
            editMessage.requestFocus();
            return;
        }
        
        if (prayerPosition <= 0) {
            showToast(getString(R.string.please_select_prayer));
            return;
        }
        
        String selectedPrayer = prayerNames[prayerPosition];
        
        if (currentEvent != null) {
            // Update existing event
            currentEvent.setTitle(title);
            currentEvent.setMessage(message);
            currentEvent.setPrayer(selectedPrayer);
            currentEvent.setStartDate(startDate.isEmpty() ? null : startDate);
            currentEvent.setEndDate(endDate.isEmpty() ? null : endDate);
            eventsManager.updateEvent(currentEvent);
        } else {
            // Create new event
            Event newEvent = new Event(title, message, selectedPrayer, 
                                     startDate.isEmpty() ? null : startDate, 
                                     endDate.isEmpty() ? null : endDate);
            eventsManager.addEvent(newEvent);
        }
        
        showToast(getString(R.string.event_message_saved));
        setResult(RESULT_EVENT_SAVED);
        finish();
    }
    
    public void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
