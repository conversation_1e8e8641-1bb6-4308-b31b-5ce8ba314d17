package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.AdvertisementSetting;
import com.alrbea.androidapp.utils.LocaleHelper;
import com.alrbea.androidapp.utils.ResponsiveLayoutManager;
import com.alrbea.androidapp.utils.ScreenUtils;

import java.util.List;

public class AdvertisementSettingsAdapter extends RecyclerView.Adapter<AdvertisementSettingsAdapter.SettingViewHolder> {
    
    public interface OnSettingChangeListener {
        void onSettingChanged(AdvertisementSetting setting, boolean isEnabled);
    }
    
    private List<AdvertisementSetting> settings;
    private OnSettingChangeListener changeListener;
    private Context context;

    public AdvertisementSettingsAdapter(List<AdvertisementSetting> settings, OnSettingChangeListener changeListener) {
        this.settings = settings;
        this.changeListener = changeListener;
    }

    @NonNull
    @Override
    public SettingViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context)
                .inflate(R.layout.item_advertisement_setting, parent, false);
        return new SettingViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SettingViewHolder holder, int position) {
        AdvertisementSetting setting = settings.get(position);
        holder.bind(setting);
    }

    @Override
    public int getItemCount() {
        return settings.size();
    }

    class SettingViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private ImageView iconImageView;
        private TextView titleTextView;
        private TextView descriptionTextView;

        public SettingViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_setting);
            iconImageView = itemView.findViewById(R.id.iv_setting_icon);
            titleTextView = itemView.findViewById(R.id.tv_setting_title);
            descriptionTextView = itemView.findViewById(R.id.tv_setting_description);
        }

        public void bind(AdvertisementSetting setting) {
            String language = LocaleHelper.getLanguage(context);
            
            // Set setting data
            iconImageView.setImageResource(setting.getIconRes());
            titleTextView.setText(setting.getDisplayTitle(language));
            descriptionTextView.setText(setting.getDisplayDescription(language));

            // Apply responsive styling
            applyResponsiveLayout();
            
            // Set click listener for expansion
            cardView.setOnClickListener(v -> {
                if (setting.getType() == AdvertisementSetting.SettingType.EXPANDABLE_TOGGLE && setting.isEnabled()) {
                    setting.setExpanded(!setting.isExpanded());

                    if (changeListener != null) {
                        changeListener.onSettingChanged(setting, setting.isEnabled());
                    }
                }
            });
            
            // Apply enabled/disabled styling
            float alpha = setting.isEnabled() ? 1.0f : 0.6f;
            iconImageView.setAlpha(alpha);
            titleTextView.setAlpha(alpha);
            descriptionTextView.setAlpha(alpha);
        }
        
        private void applyResponsiveLayout() {
            ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(context);
            
            // Apply responsive padding
            int padding = ResponsiveLayoutManager.getResponsivePadding(context, screenType);
            cardView.setContentPadding(padding, padding, padding, padding);
            
            // Apply responsive text sizes
            ResponsiveLayoutManager.applyResponsiveTextSize(context, titleTextView, 16f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, descriptionTextView, 14f);
            
            // Apply responsive icon size
            int iconSize = getResponsiveIconSize(screenType);
            ViewGroup.LayoutParams iconParams = iconImageView.getLayoutParams();
            iconParams.width = iconSize;
            iconParams.height = iconSize;
            iconImageView.setLayoutParams(iconParams);
        }
        
        private int getResponsiveIconSize(ScreenUtils.ScreenType screenType) {
            switch (screenType) {
                case PHONE_PORTRAIT:
                case PHONE_LANDSCAPE:
                    return ScreenUtils.dpToPx(context, 40);
                case TABLET_PORTRAIT:
                case TABLET_LANDSCAPE:
                    return ScreenUtils.dpToPx(context, 48);
                case TV:
                    return ScreenUtils.dpToPx(context, 56);
                default:
                    return ScreenUtils.dpToPx(context, 40);
            }
        }
    }
}
