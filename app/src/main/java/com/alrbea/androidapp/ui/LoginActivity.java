package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.lifecycle.ViewModelProvider;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.DeviceInfo;
import com.alrbea.androidapp.ui.viewmodel.AuthViewModel;

public class LoginActivity extends BaseActivity {

    private static final String PREFS_NAME = "PrayerAppPrefs";

    private EditText etActivationCode;
    private Button btnActivate;
    private Button btnContactWhatsApp;
    private Button btnContactTelegram;
    private Button btnContactEmail;
    private ProgressBar progressBar;
    private TextView tvDeviceInfo;

    private AuthViewModel authViewModel;
    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        initViews();
        initViewModel();
        setupClickListeners();
        observeViewModel();
        displayDeviceInfo();
    }

    private void initViews() {
        etActivationCode = findViewById(R.id.et_activation_code);
        btnActivate = findViewById(R.id.btn_activate);
        btnContactWhatsApp = findViewById(R.id.btn_contact_whatsapp);
        btnContactTelegram = findViewById(R.id.btn_contact_telegram);
        btnContactEmail = findViewById(R.id.btn_contact_email);
        progressBar = findViewById(R.id.progress_bar);
        tvDeviceInfo = findViewById(R.id.tv_device_info);

        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
    }

    private void initViewModel() {
        authViewModel = new ViewModelProvider(this).get(AuthViewModel.class);
    }

    private void setupClickListeners() {
        btnActivate.setOnClickListener(v -> {
            String activationCode = etActivationCode.getText().toString().trim();
            if (validateInput(activationCode)) {
                performLogin(activationCode);
            }
        });

        btnContactWhatsApp.setOnClickListener(v -> {
            openWhatsApp();
        });

        btnContactTelegram.setOnClickListener(v -> {
            openTelegram();
        });

        btnContactEmail.setOnClickListener(v -> {
            openEmail();
        });
    }

    private void observeViewModel() {
        authViewModel.getLoginResult().observe(this, loginResult -> {
            hideLoading();
            if (loginResult != null && loginResult.isSuccess()) {
                onLoginSuccess();
            } else {
                String errorMessage = loginResult != null ? loginResult.getErrorMessage() : "فشل في تسجيل الدخول";
                showError(errorMessage);
            }
        });

        authViewModel.getLoading().observe(this, isLoading -> {
            if (isLoading) {
                showLoading();
            } else {
                hideLoading();
            }
        });

        authViewModel.getError().observe(this, error -> {
            if (error != null) {
                hideLoading();
                showError(error);
            }
        });
    }

    private boolean validateInput(String activationCode) {
        if (TextUtils.isEmpty(activationCode)) {
            etActivationCode.setError("يرجى إدخال كود التفعيل");
            etActivationCode.requestFocus();
            return false;
        }

        if (activationCode.length() < 6) {
            etActivationCode.setError("كود التفعيل يجب أن يكون 6 أرقام على الأقل");
            etActivationCode.requestFocus();
            return false;
        }

        return true;
    }

    private void performLogin(String activationCode) {
        DeviceInfo deviceInfo = collectDeviceInfo();
        authViewModel.login(activationCode, deviceInfo);
    }

    private DeviceInfo collectDeviceInfo() {
        DeviceInfo deviceInfo = new DeviceInfo();
        deviceInfo.setDeviceId(Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID));
        deviceInfo.setDeviceModel(Build.MODEL);
        deviceInfo.setDeviceBrand(Build.BRAND);
        deviceInfo.setAndroidVersion(Build.VERSION.RELEASE);
        deviceInfo.setAppVersion(getAppVersion());
        deviceInfo.setScreenResolution(getScreenResolution());

        return deviceInfo;
    }

    private String getAppVersion() {
        try {
            return getPackageManager().getPackageInfo(getPackageName(), 0).versionName;
        } catch (Exception e) {
            return "Unknown";
        }
    }

    private String getScreenResolution() {
        int width = getResources().getDisplayMetrics().widthPixels;
        int height = getResources().getDisplayMetrics().heightPixels;
        return width + "x" + height;
    }

    private void displayDeviceInfo() {
        DeviceInfo deviceInfo = collectDeviceInfo();
        String deviceInfoText = String.format(
                "معرف الجهاز: %s\nنوع الجهاز: %s %s\nإصدار الأندرويد: %s",
                deviceInfo.getDeviceId().substring(0, 8) + "...",
                deviceInfo.getDeviceBrand(),
                deviceInfo.getDeviceModel(),
                deviceInfo.getAndroidVersion());
        tvDeviceInfo.setText(deviceInfoText);
    }

    private void onLoginSuccess() {
        // Save login state
        sharedPreferences.edit()
                .putBoolean("is_logged_in", true)
                .apply();

        // Navigate to location setup first
        boolean isLocationSetupComplete = sharedPreferences.getBoolean("location_setup_complete", false);
        boolean isOrientationSet = sharedPreferences.getBoolean("orientation_set", false);

        Intent intent;
        if (!isLocationSetupComplete) {
            intent = new Intent(this, LocationSetupActivity.class);
        } else if (!isOrientationSet) {
            intent = new Intent(this, OrientationSetupActivity.class);
        } else {
            intent = new Intent(this, com.alrbea.androidapp.MainActivity.class);
        }

        startActivity(intent);
        finish();
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void showLoading() {
        progressBar.setVisibility(View.VISIBLE);
        btnActivate.setEnabled(false);
        btnActivate.setText("جاري التحقق...");
    }

    private void hideLoading() {
        progressBar.setVisibility(View.GONE);
        btnActivate.setEnabled(true);
        btnActivate.setText("تفعيل");
    }

    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    private void openWhatsApp() {
        String phoneNumber = "+966500000000"; // Replace with actual admin number
        String message = "مرحباً، أحتاج إلى كود تفعيل لتطبيق مواقيت الصلاة\n" +
                "معرف الجهاز: " + Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);

        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse("https://wa.me/" + phoneNumber.replace("+", "") + "?text=" + Uri.encode(message)));
            startActivity(intent);
        } catch (Exception e) {
            Toast.makeText(this, "لم يتم العثور على تطبيق واتساب", Toast.LENGTH_SHORT).show();
        }
    }

    private void openTelegram() {
        String username = "prayer_app_admin"; // Replace with actual admin username
        String message = "مرحباً، أحتاج إلى كود تفعيل لتطبيق مواقيت الصلاة\n" +
                "معرف الجهاز: " + Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);

        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse("https://t.me/" + username + "?text=" + Uri.encode(message)));
            startActivity(intent);
        } catch (Exception e) {
            Toast.makeText(this, "لم يتم العثور على تطبيق تيليجرام", Toast.LENGTH_SHORT).show();
        }
    }

    private void openEmail() {
        String email = "<EMAIL>"; // Replace with actual admin email
        String subject = "طلب كود تفعيل - تطبيق مواقيت الصلاة";
        String message = "مرحباً،\n\nأحتاج إلى كود تفعيل لتطبيق مواقيت الصلاة\n\n" +
                "تفاصيل الجهاز:\n" +
                "معرف الجهاز: " + Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID) + "\n" +
                "نوع الجهاز: " + Build.BRAND + " " + Build.MODEL + "\n" +
                "إصدار الأندرويد: " + Build.VERSION.RELEASE + "\n\n" +
                "شكراً لكم";

        try {
            Intent intent = new Intent(Intent.ACTION_SENDTO);
            intent.setData(Uri.parse("mailto:" + email));
            intent.putExtra(Intent.EXTRA_SUBJECT, subject);
            intent.putExtra(Intent.EXTRA_TEXT, message);
            startActivity(intent);
        } catch (Exception e) {
            Toast.makeText(this, "لم يتم العثور على تطبيق بريد إلكتروني", Toast.LENGTH_SHORT).show();
        }
    }
}
