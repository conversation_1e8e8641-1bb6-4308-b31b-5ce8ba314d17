package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Toast;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.AdvertisementSetting;
import com.alrbea.androidapp.ui.adapter.AdvertisementSettingsAdapter;
import com.alrbea.androidapp.utils.ResponsiveItemDecoration;
import com.alrbea.androidapp.utils.ScreenUtils;

import java.util.ArrayList;
import java.util.List;

public class AdvertisementsSettingsActivity extends BaseActivity {
    
    private static final String PREFS_NAME = "AdvertisementSettings";
    
    private RecyclerView recyclerViewSettings;
    private AdvertisementSettingsAdapter adapter;
    private Toolbar toolbar;
    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_advertisements_settings);
        
        initViews();
        setupToolbar();
        setupRecyclerView();
        loadAdvertisementSettings();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        recyclerViewSettings = findViewById(R.id.recycler_view_settings);
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("إعدادات الإعلانات");
        }
    }
    
    private void setupRecyclerView() {
        int columns = getColumnCount();
        
        GridLayoutManager layoutManager = new GridLayoutManager(this, columns);
        recyclerViewSettings.setLayoutManager(layoutManager);
        
        ResponsiveItemDecoration itemDecoration = new ResponsiveItemDecoration(this, true);
        recyclerViewSettings.addItemDecoration(itemDecoration);
        
        int padding = ScreenUtils.getScreenPadding(this);
        recyclerViewSettings.setPadding(padding, padding, padding, padding);
    }
    
    private int getColumnCount() {
        ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(this);
        
        switch (screenType) {
            case PHONE_PORTRAIT:
                return 1;
            case PHONE_LANDSCAPE:
                return 2;
            case TABLET_PORTRAIT:
                return 2;
            case TABLET_LANDSCAPE:
                return 3;
            case TV:
                return 4;
            default:
                return 1;
        }
    }
    
    private void loadAdvertisementSettings() {
        List<AdvertisementSetting> settings = createAdvertisementSettings();
        
        adapter = new AdvertisementSettingsAdapter(settings, this::onSettingChanged);
        recyclerViewSettings.setAdapter(adapter);
    }
    
    private List<AdvertisementSetting> createAdvertisementSettings() {
        List<AdvertisementSetting> settings = new ArrayList<>();
        
        // 1. الشريط الإخباري
        AdvertisementSetting newsBanner = new AdvertisementSetting(
            "news_banner",
            "الشريط الإخباري",
            "News Banner",
            "عرض الأخبار والإعلانات في الصفحة الرئيسية",
            "Display news and announcements on main screen",
            R.drawable.ic_news,
            AdvertisementSetting.SettingType.EXPANDABLE_TOGGLE
        );
        newsBanner.setEnabled(sharedPreferences.getBoolean("news_banner_enabled", false));
        settings.add(newsBanner);
        
        // 2. الأحداث والمناسبات
        AdvertisementSetting events = new AdvertisementSetting(
            "events",
            "الأحداث والمناسبات",
            "Events & Occasions",
            "عرض المناسبات الدينية والأحداث المهمة",
            "Display religious occasions and important events",
            R.drawable.ic_events,
            AdvertisementSetting.SettingType.EXPANDABLE_TOGGLE
        );
        events.setEnabled(sharedPreferences.getBoolean("events_enabled", false));
        settings.add(events);
        
        // 3. رسائل الجنازة
        AdvertisementSetting funeralMessages = new AdvertisementSetting(
            "funeral_messages",
            "رسائل الجنازة",
            "Funeral Messages",
            "عرض إعلانات الصلاة على الميت",
            "Display funeral prayer announcements",
            R.drawable.ic_funeral,
            AdvertisementSetting.SettingType.EXPANDABLE_TOGGLE
        );
        funeralMessages.setEnabled(sharedPreferences.getBoolean("funeral_messages_enabled", false));
        settings.add(funeralMessages);
        
        // 4. عنوان خطبة الجمعة
        AdvertisementSetting fridaySermon = new AdvertisementSetting(
            "friday_sermon",
            "عنوان خطبة الجمعة",
            "Friday Sermon Title",
            "عرض عنوان خطبة الجمعة",
            "Display Friday sermon title",
            R.drawable.ic_sermon,
            AdvertisementSetting.SettingType.EXPANDABLE_TOGGLE
        );
        fridaySermon.setEnabled(sharedPreferences.getBoolean("friday_sermon_enabled", false));
        settings.add(fridaySermon);
        
        return settings;
    }
    
    private void onSettingChanged(AdvertisementSetting setting, boolean isEnabled) {
        // Save setting state
        sharedPreferences.edit()
                .putBoolean(setting.getId() + "_enabled", isEnabled)
                .apply();
        
        setting.setEnabled(isEnabled);
        
        if (isEnabled) {
            openDetailedSettings(setting);
        }
        
        showToast(setting.getDisplayTitle(getCurrentLanguage()) + 
                 (isEnabled ? " تم التفعيل" : " تم الإلغاء"));
    }
    
    private void openDetailedSettings(AdvertisementSetting setting) {
        switch (setting.getId()) {
            case "news_banner":
                openNewsBannerSettings();
                break;
            case "events":
                openEventsSettings();
                break;
            case "funeral_messages":
                openFuneralMessagesSettings();
                break;
            case "friday_sermon":
                openFridaySermonSettings();
                break;
        }
    }
    
    private void openNewsBannerSettings() {
        Intent intent = new Intent(this, NewsBannerSettingsActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }
    
    private void openEventsSettings() {
        Intent intent = new Intent(this, EventsSettingsActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }
    
    private void openFuneralMessagesSettings() {
        Intent intent = new Intent(this, FuneralPrayerSettingsActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }
    
    private void openFridaySermonSettings() {
        Intent intent = new Intent(this, SermonTitleSettingsActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }
    
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
