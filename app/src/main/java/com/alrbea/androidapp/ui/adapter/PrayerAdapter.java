package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.Prayer;
import com.alrbea.androidapp.utils.LocaleHelper;

import java.util.List;

public class PrayerAdapter extends RecyclerView.Adapter<PrayerAdapter.PrayerViewHolder> {
    
    public interface OnPrayerClickListener {
        void onPrayerClick(Prayer prayer);
    }
    
    private List<Prayer> prayers;
    private OnPrayerClickListener clickListener;
    private Context context;

    public PrayerAdapter(List<Prayer> prayers, OnPrayerClickListener clickListener) {
        this.prayers = prayers;
        this.clickListener = clickListener;
    }

    @NonNull
    @Override
    public PrayerViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context)
                .inflate(R.layout.item_prayer, parent, false);
        return new PrayerViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PrayerViewHolder holder, int position) {
        Prayer prayer = prayers.get(position);
        holder.bind(prayer);
    }

    @Override
    public int getItemCount() {
        return prayers.size();
    }

    class PrayerViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private ImageView iconPrayer;
        private TextView textPrayerName;
        private TextView textPrayerTime;

        public PrayerViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = (CardView) itemView;
            iconPrayer = itemView.findViewById(R.id.icon_prayer);
            textPrayerName = itemView.findViewById(R.id.text_prayer_name);
            textPrayerTime = itemView.findViewById(R.id.text_prayer_time);
        }

        public void bind(Prayer prayer) {
            String language = LocaleHelper.getLanguage(context);
            
            // Set prayer data
            iconPrayer.setImageResource(prayer.getIconRes());
            textPrayerName.setText(prayer.getDisplayName(language));
            textPrayerTime.setText(prayer.getTime());
            
            // Set click listener
            cardView.setOnClickListener(v -> {
                if (clickListener != null) {
                    clickListener.onPrayerClick(prayer);
                }
            });
        }
    }
}
