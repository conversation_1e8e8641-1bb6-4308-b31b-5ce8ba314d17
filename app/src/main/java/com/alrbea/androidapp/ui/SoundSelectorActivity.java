package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Toast;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.ui.adapter.SoundAdapter;

import java.util.ArrayList;
import java.util.List;

public class SoundSelectorActivity extends BaseActivity implements SoundAdapter.OnSoundSelectedListener {

    private Toolbar toolbar;
    private RecyclerView recyclerSounds;
    private SoundAdapter soundAdapter;
    
    private String soundType;
    private String currentSelection;
    private MediaPlayer mediaPlayer;
    private List<SoundItem> soundsList = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sound_selector);

        getIntentData();
        initViews();
        setupToolbar();
        setupRecyclerView();
        loadSounds();
    }

    private void getIntentData() {
        soundType = getIntent().getStringExtra("sound_type");
        currentSelection = getIntent().getStringExtra("current_selection");
        
        if (soundType == null) {
            soundType = "azan";
        }
        if (currentSelection == null) {
            currentSelection = "";
        }
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        recyclerSounds = findViewById(R.id.recycler_sounds);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            
            String title = "اختيار نغمة ";
            if ("azan".equals(soundType)) {
                title += "الأذان";
            } else if ("iqama".equals(soundType)) {
                title += "الإقامة";
            }
            getSupportActionBar().setTitle(title);
        }
    }

    private void setupRecyclerView() {
        recyclerSounds.setLayoutManager(new LinearLayoutManager(this));
        soundAdapter = new SoundAdapter(soundsList, this, currentSelection);
        recyclerSounds.setAdapter(soundAdapter);
    }

    private void loadSounds() {
        soundsList.clear();
        
        if ("azan".equals(soundType)) {
            loadAzanSounds();
        } else if ("iqama".equals(soundType)) {
            loadIqamaSounds();
        }
        
        soundAdapter.notifyDataSetChanged();
    }

    private void loadAzanSounds() {
        soundsList.add(new SoundItem("الشيخ محمد رفعت", "azan_rifaat", 0));
        soundsList.add(new SoundItem("الشيخ عبد الباسط عبد الصمد", "azan_abdulbasit", 0));
        soundsList.add(new SoundItem("الشيخ محمد صديق المنشاوي", "azan_minshawi", 0));
        soundsList.add(new SoundItem("الشيخ مشاري العفاسي", "azan_afasy", 0));
        soundsList.add(new SoundItem("الشيخ ماهر المعيقلي", "azan_maher", 0));
        soundsList.add(new SoundItem("النغمة الافتراضية", "azan_default", 0));
    }

    private void loadIqamaSounds() {
        soundsList.add(new SoundItem("النغمة الافتراضية", "iqama_default", 0));
        soundsList.add(new SoundItem("نغمة الإقامة الكلاسيكية", "iqama_classic", 0));
        soundsList.add(new SoundItem("نغمة الإقامة الحديثة", "iqama_modern", 0));
    }

    @Override
    public void onSoundSelected(SoundItem soundItem) {
        Intent resultIntent = new Intent();
        resultIntent.putExtra("selected_sound", soundItem.getName());
        resultIntent.putExtra("selected_sound_id", soundItem.getId());
        setResult(RESULT_OK, resultIntent);
        finish();
    }

    @Override
    public void onSoundPlay(SoundItem soundItem) {
        playSound(soundItem.getResourceId());
    }

    @Override
    public void onSoundStop() {
        stopSound();
    }

    private void playSound(int resourceId) {
        try {
            stopSound(); // Stop any currently playing sound

            if (resourceId != 0) {
                mediaPlayer = MediaPlayer.create(this, resourceId);
                if (mediaPlayer != null) {
                    mediaPlayer.setOnCompletionListener(mp -> stopSound());
                    mediaPlayer.start();
                }
            } else {
                // Show message that sound file is not available
                Toast.makeText(this, "ملف الصوت غير متوفر حالياً", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Toast.makeText(this, "خطأ في تشغيل الصوت", Toast.LENGTH_SHORT).show();
        }
    }

    private void stopSound() {
        if (mediaPlayer != null) {
            try {
                if (mediaPlayer.isPlaying()) {
                    mediaPlayer.stop();
                }
                mediaPlayer.release();
            } catch (Exception e) {
                // Handle error
            } finally {
                mediaPlayer = null;
            }
        }
    }

    @Override
    protected void onDestroy() {
        stopSound();
        super.onDestroy();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    // Inner class for sound items
    public static class SoundItem {
        private String name;
        private String id;
        private int resourceId;

        public SoundItem(String name, String id, int resourceId) {
            this.name = name;
            this.id = id;
            this.resourceId = resourceId;
        }

        public String getName() {
            return name;
        }

        public String getId() {
            return id;
        }

        public int getResourceId() {
            return resourceId;
        }
    }
}
