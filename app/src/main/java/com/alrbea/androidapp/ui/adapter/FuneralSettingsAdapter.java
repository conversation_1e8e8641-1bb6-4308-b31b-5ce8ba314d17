package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.FuneralSetting;
import com.alrbea.androidapp.utils.LocaleHelper;
import com.alrbea.androidapp.utils.ResponsiveLayoutManager;
import com.alrbea.androidapp.utils.ScreenUtils;

import java.util.List;

public class FuneralSettingsAdapter extends RecyclerView.Adapter<FuneralSettingsAdapter.SettingViewHolder> {
    
    public interface OnSettingChangeListener {
        void onSettingChanged(FuneralSetting setting, Object value);
    }
    
    private List<FuneralSetting> settings;
    private OnSettingChangeListener changeListener;
    private Context context;

    public FuneralSettingsAdapter(List<FuneralSetting> settings, OnSettingChangeListener changeListener) {
        this.settings = settings;
        this.changeListener = changeListener;
    }

    @NonNull
    @Override
    public SettingViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context)
                .inflate(R.layout.item_funeral_setting, parent, false);
        return new SettingViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SettingViewHolder holder, int position) {
        FuneralSetting setting = settings.get(position);
        holder.bind(setting);
    }

    @Override
    public int getItemCount() {
        return settings.size();
    }

    class SettingViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private ImageView iconImageView;
        private TextView titleTextView;
        private TextView descriptionTextView;
        private TextView valueTextView;
        private Switch toggleSwitch;

        public SettingViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_setting);
            iconImageView = itemView.findViewById(R.id.iv_setting_icon);
            titleTextView = itemView.findViewById(R.id.tv_setting_title);
            descriptionTextView = itemView.findViewById(R.id.tv_setting_description);
            valueTextView = itemView.findViewById(R.id.tv_setting_value);
            toggleSwitch = itemView.findViewById(R.id.switch_setting);
        }

        public void bind(FuneralSetting setting) {
            String language = LocaleHelper.getLanguage(context);
            
            // Set setting data
            iconImageView.setImageResource(setting.getIconRes());
            titleTextView.setText(setting.getDisplayTitle(language));
            descriptionTextView.setText(setting.getDisplayDescription(language));
            
            // Hide all controls first
            toggleSwitch.setVisibility(View.GONE);
            valueTextView.setVisibility(View.GONE);
            
            // Show appropriate control based on setting type
            switch (setting.getType()) {
                case TOGGLE:
                    toggleSwitch.setVisibility(View.VISIBLE);
                    toggleSwitch.setOnCheckedChangeListener(null);
                    toggleSwitch.setChecked(setting.isEnabled());
                    toggleSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                        if (changeListener != null) {
                            changeListener.onSettingChanged(setting, isChecked);
                        }
                    });
                    break;
                    
                case TEXT_TEMPLATE:
                    valueTextView.setVisibility(View.VISIBLE);
                    String textValue = setting.getValue() != null ? setting.getValue() : "اضغط للتعديل";
                    valueTextView.setText(textValue);
                    cardView.setOnClickListener(v -> {
                        // TODO: Show text edit dialog
                        if (changeListener != null) {
                            changeListener.onSettingChanged(setting, textValue);
                        }
                    });
                    break;
                    
                case PRAYER_SELECTOR:
                    valueTextView.setVisibility(View.VISIBLE);
                    String prayerValue = setting.getValue() != null ? setting.getValue() : "المغرب";
                    valueTextView.setText(getPrayerDisplayName(prayerValue));
                    cardView.setOnClickListener(v -> {
                        // TODO: Show prayer selection dialog
                        if (changeListener != null) {
                            changeListener.onSettingChanged(setting, prayerValue);
                        }
                    });
                    break;
                    
                case NUMBER_INPUT:
                    valueTextView.setVisibility(View.VISIBLE);
                    String numberValue = setting.getValue() != null ? setting.getValue() : "30";
                    valueTextView.setText(numberValue + " دقيقة");
                    cardView.setOnClickListener(v -> {
                        // TODO: Show number input dialog
                        if (changeListener != null) {
                            changeListener.onSettingChanged(setting, numberValue);
                        }
                    });
                    break;
            }
            
            // Apply responsive styling
            applyResponsiveLayout();
        }
        
        private String getPrayerDisplayName(String value) {
            switch (value) {
                case "fajr": return "الفجر";
                case "dhuhr": return "الظهر";
                case "asr": return "العصر";
                case "maghrib": return "المغرب";
                case "isha": return "العشاء";
                default: return "المغرب";
            }
        }
        
        private void applyResponsiveLayout() {
            ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(context);
            
            // Apply responsive padding
            int padding = ResponsiveLayoutManager.getResponsivePadding(context, screenType);
            cardView.setContentPadding(padding, padding, padding, padding);
            
            // Apply responsive text sizes
            ResponsiveLayoutManager.applyResponsiveTextSize(context, titleTextView, 16f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, descriptionTextView, 14f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, valueTextView, 14f);
            
            // Apply responsive icon size
            int iconSize = getResponsiveIconSize(screenType);
            ViewGroup.LayoutParams iconParams = iconImageView.getLayoutParams();
            iconParams.width = iconSize;
            iconParams.height = iconSize;
            iconImageView.setLayoutParams(iconParams);
        }
        
        private int getResponsiveIconSize(ScreenUtils.ScreenType screenType) {
            switch (screenType) {
                case PHONE_PORTRAIT:
                case PHONE_LANDSCAPE:
                    return ScreenUtils.dpToPx(context, 40);
                case TABLET_PORTRAIT:
                case TABLET_LANDSCAPE:
                    return ScreenUtils.dpToPx(context, 48);
                case TV:
                    return ScreenUtils.dpToPx(context, 56);
                default:
                    return ScreenUtils.dpToPx(context, 40);
            }
        }
    }
}
