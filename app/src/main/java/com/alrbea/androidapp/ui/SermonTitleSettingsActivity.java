package com.alrbea.androidapp.ui;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;

import com.alrbea.androidapp.R;
import com.google.android.material.textfield.TextInputEditText;

public class SermonTitleSettingsActivity extends BaseActivity {
    
    private Toolbar toolbar;
    private SwitchCompat switchEnableSermonTitle;
    private LinearLayout layoutSettingsContainer;
    private TextInputEditText editSermonTitle;
    private Button btnSave;

    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sermon_title_settings);
        
        initializeViews();
        setupToolbar();
        setupSharedPreferences();
        setupListeners();
        loadSavedSettings();
    }
    
    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        switchEnableSermonTitle = findViewById(R.id.switch_enable_sermon_title);
        layoutSettingsContainer = findViewById(R.id.layout_settings_container);
        editSermonTitle = findViewById(R.id.edit_sermon_title);
        btnSave = findViewById(R.id.btn_save);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }
    }
    
    private void setupSharedPreferences() {
        sharedPreferences = getSharedPreferences("sermon_title_prefs", MODE_PRIVATE);
    }
    
    private void setupListeners() {
        switchEnableSermonTitle.setOnCheckedChangeListener((buttonView, isChecked) -> {
            layoutSettingsContainer.setVisibility(isChecked ? View.VISIBLE : View.GONE);
            if (!isChecked) {
                // Clear the sermon title when disabled
                sharedPreferences.edit()
                    .putBoolean("sermon_title_enabled", false)
                    .apply();
            }
        });

        btnSave.setOnClickListener(v -> saveSermonTitleSettings());
    }
    
    private void saveSermonTitleSettings() {
        String sermonTitle = editSermonTitle.getText().toString().trim();

        // Validation
        if (sermonTitle.isEmpty()) {
            showToast(getString(R.string.please_enter_sermon_title));
            editSermonTitle.requestFocus();
            return;
        }

        // Save settings
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putBoolean("sermon_title_enabled", switchEnableSermonTitle.isChecked());
        editor.putString("sermon_title_text", sermonTitle);
        editor.apply();

        showToast(getString(R.string.sermon_title_saved));
        finish();
    }
    
    private void loadSavedSettings() {
        boolean isEnabled = sharedPreferences.getBoolean("sermon_title_enabled", false);
        switchEnableSermonTitle.setChecked(isEnabled);
        layoutSettingsContainer.setVisibility(isEnabled ? View.VISIBLE : View.GONE);

        String savedTitle = sharedPreferences.getString("sermon_title_text", "");
        editSermonTitle.setText(savedTitle);
    }
    
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
