package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Toast;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.AzkarSetting;
import com.alrbea.androidapp.ui.adapter.AzkarSettingsAdapter;
import com.alrbea.androidapp.utils.ResponsiveItemDecoration;
import com.alrbea.androidapp.utils.ScreenUtils;

import java.util.ArrayList;
import java.util.List;

public class AzkarSettingsActivity extends BaseActivity {
    
    private static final String PREFS_NAME = "AzkarSettings";
    
    private RecyclerView recyclerViewSettings;
    private AzkarSettingsAdapter adapter;
    private Toolbar toolbar;
    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_azkar_settings);
        
        initViews();
        setupToolbar();
        setupRecyclerView();
        loadAzkarSettings();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        recyclerViewSettings = findViewById(R.id.recycler_view_settings);
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("إعدادات الأذكار");
        }
    }
    
    private void setupRecyclerView() {
        int columns = getColumnCount();
        
        GridLayoutManager layoutManager = new GridLayoutManager(this, columns);
        recyclerViewSettings.setLayoutManager(layoutManager);
        
        ResponsiveItemDecoration itemDecoration = new ResponsiveItemDecoration(this, true);
        recyclerViewSettings.addItemDecoration(itemDecoration);
        
        int padding = ScreenUtils.getScreenPadding(this);
        recyclerViewSettings.setPadding(padding, padding, padding, padding);
    }
    
    private int getColumnCount() {
        ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(this);
        
        switch (screenType) {
            case PHONE_PORTRAIT:
                return 1;
            case PHONE_LANDSCAPE:
                return 2;
            case TABLET_PORTRAIT:
                return 2;
            case TABLET_LANDSCAPE:
                return 3;
            case TV:
                return 4;
            default:
                return 1;
        }
    }
    
    private void loadAzkarSettings() {
        List<AzkarSetting> settings = createAzkarSettings();
        
        adapter = new AzkarSettingsAdapter(settings, this::onSettingChanged);
        recyclerViewSettings.setAdapter(adapter);
    }
    
    private List<AzkarSetting> createAzkarSettings() {
        List<AzkarSetting> settings = new ArrayList<>();

        // 1. أذكار الصباح والمساء
        AzkarSetting morningEvening = new AzkarSetting(
            "morning_evening_azkar",
            getString(R.string.morning_evening_azkar),
            "Morning & Evening Azkar",
            getString(R.string.morning_evening_azkar_subtitle),
            "Configure morning and evening azkar and their display times",
            R.drawable.ic_sun_moon,
            AzkarSetting.SettingType.EXPANDABLE_TOGGLE
        );
        morningEvening.setEnabled(sharedPreferences.getBoolean("morning_evening_enabled", false));
        settings.add(morningEvening);

        // 2. أذكار بعد الصلاة
        AzkarSetting afterPrayer = new AzkarSetting(
            "after_prayer_azkar",
            getString(R.string.post_prayer_azkar),
            "After Prayer Azkar",
            getString(R.string.post_prayer_azkar_subtitle),
            "Configure azkar after the five daily prayers",
            R.drawable.ic_prayer_beads,
            AzkarSetting.SettingType.EXPANDABLE_TOGGLE
        );
        afterPrayer.setEnabled(sharedPreferences.getBoolean("after_prayer_enabled", false));
        settings.add(afterPrayer);
        
        return settings;
    }
    
    private void onSettingChanged(AzkarSetting setting, Object value) {
        switch (setting.getId()) {
            case "morning_evening_azkar":
                openMorningEveningAzkarSettings();

                break;

            case "after_prayer_azkar":
                openPostPrayerAzkarSettings();

                break;
        }
    }

    private void openMorningEveningAzkarSettings() {
        Intent intent = new Intent(this, MorningEveningAzkarActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void openPostPrayerAzkarSettings() {
        Intent intent = new Intent(this, PostPrayerAzkarActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
