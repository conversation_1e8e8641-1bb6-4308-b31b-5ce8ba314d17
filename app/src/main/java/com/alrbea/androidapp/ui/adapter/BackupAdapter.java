package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.BackupData;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

public class BackupAdapter extends RecyclerView.Adapter<BackupAdapter.BackupViewHolder> {

    public interface OnBackupActionListener {
        void onRestoreBackup(BackupData backup);
        void onPreviewBackup(BackupData backup);
        void onDeleteBackup(BackupData backup);
    }

    private List<BackupData> backups;
    private OnBackupActionListener actionListener;
    private Context context;

    public BackupAdapter(List<BackupData> backups, OnBackupActionListener actionListener) {
        this.backups = backups;
        this.actionListener = actionListener;
    }

    @NonNull
    @Override
    public BackupViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context).inflate(R.layout.item_backup, parent, false);
        return new BackupViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull BackupViewHolder holder, int position) {
        BackupData backup = backups.get(position);
        holder.bind(backup);
    }

    @Override
    public int getItemCount() {
        return backups.size();
    }

    public void updateBackups(List<BackupData> newBackups) {
        this.backups = newBackups;
        notifyDataSetChanged();
    }

    public void removeBackup(int position) {
        if (position >= 0 && position < backups.size()) {
            backups.remove(position);
            notifyItemRemoved(position);
        }
    }

    class BackupViewHolder extends RecyclerView.ViewHolder {
        private TextView textBackupName;
        private TextView textBackupDate;
        private TextView textBackupSize;
        private TextView textBackupItems;
        private TextView textBackupVersion;
        private Button btnRestoreBackup;
        private Button btnPreviewBackup;
        private Button btnDeleteBackup;

        public BackupViewHolder(@NonNull View itemView) {
            super(itemView);
            textBackupName = itemView.findViewById(R.id.text_backup_name);
            textBackupDate = itemView.findViewById(R.id.text_backup_date);
            textBackupSize = itemView.findViewById(R.id.text_backup_size);
            textBackupItems = itemView.findViewById(R.id.text_backup_items);
            textBackupVersion = itemView.findViewById(R.id.text_backup_version);
            btnRestoreBackup = itemView.findViewById(R.id.btn_restore_backup);
            btnPreviewBackup = itemView.findViewById(R.id.btn_preview_backup);
            btnDeleteBackup = itemView.findViewById(R.id.btn_delete_backup);
        }

        public void bind(BackupData backup) {
            textBackupName.setText(backup.getName());
            
            // Format date
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd - HH:mm", Locale.getDefault());
            textBackupDate.setText(dateFormat.format(backup.getCreatedAt()));
            
            // Format size
            textBackupSize.setText(backup.getFormattedSize());
            
            // Format items count
            textBackupItems.setText(backup.getCategoryCount() + " فئة إعدادات");
            
            // Version
            textBackupVersion.setText("v" + backup.getVersion());
            
            // Set click listeners
            btnRestoreBackup.setOnClickListener(v -> {
                if (actionListener != null) {
                    actionListener.onRestoreBackup(backup);
                }
            });
            
            btnPreviewBackup.setOnClickListener(v -> {
                if (actionListener != null) {
                    actionListener.onPreviewBackup(backup);
                }
            });
            
            btnDeleteBackup.setOnClickListener(v -> {
                if (actionListener != null) {
                    actionListener.onDeleteBackup(backup);
                }
            });
        }
    }
}
