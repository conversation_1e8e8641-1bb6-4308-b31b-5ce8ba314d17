package com.alrbea.androidapp.ui;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.content.pm.ActivityInfo;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import com.alrbea.androidapp.MyApp;
import com.alrbea.androidapp.utils.LocaleHelper;

public abstract class BaseActivity extends AppCompatActivity {

    @Override
    protected void attachBaseContext(Context newBase) {
        String language = LocaleHelper.getLanguage(newBase);
        Context context = LocaleHelper.setLocale(newBase, language);
        super.attachBaseContext(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        setRequestedOrientation(MyApp.savedOrientation);
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        );
        getWindow().setStatusBarColor(Color.TRANSPARENT); // اختياري لجعل الخلفية شفافة
        super.onCreate(savedInstanceState);
    }

    // LANGUAGE CONTROL

    protected void changeLanguage(String language) {
        LocaleHelper.setLocale(this, language);
        recreate();
    }

    protected String getCurrentLanguage() {
        return LocaleHelper.getLanguage(this);
    }

    protected boolean isRTL() {
        return LocaleHelper.isRTL(this);
    }

    protected void toggleLanguage() {
        String newLanguage = LocaleHelper.toggleLanguage(this);
        recreate();
    }

    protected void showMessage(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
}
