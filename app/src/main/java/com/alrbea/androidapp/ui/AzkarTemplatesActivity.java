package com.alrbea.androidapp.ui;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.ui.adapter.AzkarTemplateAdapter;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import pl.droidsonroids.gif.GifImageView;

import java.util.ArrayList;
import java.util.List;

public class AzkarTemplatesActivity extends BaseActivity implements AzkarTemplateAdapter.OnTemplateActionListener {

    private Toolbar toolbar;
    private GifImageView gifTemplatePreview;
    private ProgressBar progressLoading;
    private LinearLayout layoutDownloadStatus;
    private ImageView iconDownloadStatus;
    private TextView textDownloadStatus;
    private ProgressBar progressDownload;
    private TextView textTemplateName;
    private TextView textTemplateDescription;
    private ImageView iconTemplateStatus;
    
    private ViewPager2 viewpagerTemplates;
    private LinearLayout layoutIndicators;
    private Button btnDownloadTemplate;
    private Button btnPreviewTemplate;
    private Button btnApplyTemplate;
    private FloatingActionButton fabRefresh;
    
    private AzkarTemplateAdapter templateAdapter;
    private List<AzkarTemplate> templatesList = new ArrayList<>();
    private int currentSelectedPosition = 0;
    
    private SharedPreferences sharedPreferences;
    private static final String PREFS_NAME = "AzkarTemplateSettings";
    private static final String KEY_SELECTED_TEMPLATE = "selected_template";
    private static final String KEY_ORIENTATION = "orientation_setting";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_azkar_templates);

        initViews();
        setupToolbar();
        loadTemplates();
        setupViewPager();
        setupClickListeners();
        updatePreview();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        gifTemplatePreview = findViewById(R.id.img_azkar_preview);
        progressLoading = findViewById(R.id.progress_loading);
        layoutDownloadStatus = findViewById(R.id.layout_download_status);
        iconDownloadStatus = findViewById(R.id.icon_download_status);
        textDownloadStatus = findViewById(R.id.text_download_status);
        progressDownload = findViewById(R.id.progress_download);
        textTemplateName = findViewById(R.id.text_azkar_name);
        textTemplateDescription = findViewById(R.id.text_azkar_description);
        iconTemplateStatus = findViewById(R.id.icon_azkar_status);

        viewpagerTemplates = findViewById(R.id.viewpager_azkar);
        layoutIndicators = findViewById(R.id.layout_indicators);
        btnDownloadTemplate = findViewById(R.id.btn_download_azkar);
        btnPreviewTemplate = findViewById(R.id.btn_preview_azkar);
        btnApplyTemplate = findViewById(R.id.btn_apply_azkar);
        fabRefresh = findViewById(R.id.fab_refresh);

        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("قوالب الأذكار");
        }
    }

    private void loadTemplates() {
        templatesList.clear();
        
        // Get orientation setting
        String orientation = getOrientationSetting();
        
        // Load local templates
        AzkarTemplate template1 = new AzkarTemplate("1", "أذكار الصباح الكلاسيكية", "قالب كلاسيكي لأذكار الصباح");
        template1.setPreviewImage(R.drawable.ic_template);
        template1.setLandscapeImage(R.drawable.ic_template);
        template1.setPortraitImage(R.drawable.ic_template);
        template1.setType(AzkarTemplate.AzkarType.MORNING);
        template1.setCategory(AzkarTemplate.AzkarCategory.LOCAL);
        template1.setDownloaded(true);
        templatesList.add(template1);

        AzkarTemplate template2 = new AzkarTemplate("2", "أذكار المساء الذهبية", "قالب ذهبي لأذكار المساء");
        template2.setPreviewImage(R.drawable.ic_template);
        template2.setLandscapeImage(R.drawable.ic_template);
        template2.setPortraitImage(R.drawable.ic_template);
        template2.setType(AzkarTemplate.AzkarType.EVENING);
        template2.setCategory(AzkarTemplate.AzkarCategory.LOCAL);
        template2.setDownloaded(true);
        templatesList.add(template2);
        
        // Load API templates (simulated)
        loadApiTemplates(orientation);
    }

    private void loadApiTemplates(String orientation) {
        // Simulate API templates
        AzkarTemplate apiTemplate1 = new AzkarTemplate("api_1", "قالب متقدم من الخادم", "قالب متقدم من الخادم");
        apiTemplate1.setPreviewImage(R.drawable.ic_template);
        apiTemplate1.setType(AzkarTemplate.AzkarType.MORNING);
        apiTemplate1.setCategory(AzkarTemplate.AzkarCategory.API);
        apiTemplate1.setDownloaded(false);
        templatesList.add(apiTemplate1);

        AzkarTemplate apiTemplate2 = new AzkarTemplate("api_2", "قالب ذهبي فاخر", "قالب ذهبي فاخر من الخادم");
        apiTemplate2.setPreviewImage(R.drawable.ic_template);
        apiTemplate2.setType(AzkarTemplate.AzkarType.EVENING);
        apiTemplate2.setCategory(AzkarTemplate.AzkarCategory.API);
        apiTemplate2.setDownloaded(false);
        templatesList.add(apiTemplate2);
    }

    private String getOrientationSetting() {
        return sharedPreferences.getString(KEY_ORIENTATION, "portrait");
    }

    private void setupViewPager() {
        templateAdapter = new AzkarTemplateAdapter(templatesList, this);
        viewpagerTemplates.setAdapter(templateAdapter);
        
        // Setup page change callback
        viewpagerTemplates.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                currentSelectedPosition = position;
                updatePreview();
                updateIndicators();
            }
        });
        
        // Setup indicators
        setupIndicators();
        updateIndicators();
    }

    private void setupIndicators() {
        layoutIndicators.removeAllViews();
        
        for (int i = 0; i < templatesList.size(); i++) {
            View indicator = new View(this);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                getResources().getDimensionPixelSize(R.dimen._8sdp),
                getResources().getDimensionPixelSize(R.dimen._8sdp)
            );
            params.setMargins(
                getResources().getDimensionPixelSize(R.dimen._4sdp),
                0,
                getResources().getDimensionPixelSize(R.dimen._4sdp),
                0
            );
            indicator.setLayoutParams(params);
            indicator.setBackgroundResource(R.drawable.indicator_inactive);
            layoutIndicators.addView(indicator);
        }
    }

    private void updateIndicators() {
        for (int i = 0; i < layoutIndicators.getChildCount(); i++) {
            View indicator = layoutIndicators.getChildAt(i);
            if (i == currentSelectedPosition) {
                indicator.setBackgroundResource(R.drawable.indicator_active);
            } else {
                indicator.setBackgroundResource(R.drawable.indicator_inactive);
            }
        }
    }

    private void updatePreview() {
        if (currentSelectedPosition >= 0 && currentSelectedPosition < templatesList.size()) {
            AzkarTemplate template = templatesList.get(currentSelectedPosition);
            
            textTemplateName.setText(template.getName());
            textTemplateDescription.setText(template.getDescription());
            
            if (template.isDownloaded()) {
                // Show template preview
                layoutDownloadStatus.setVisibility(View.GONE);
                iconTemplateStatus.setVisibility(View.GONE);

                // Load appropriate image based on orientation
                int imageResource = template.getPreviewImage();
                if (imageResource != 0) {
                    gifTemplatePreview.setImageResource(imageResource);
                }

                btnDownloadTemplate.setVisibility(View.GONE);
                btnPreviewTemplate.setEnabled(true);
                btnApplyTemplate.setEnabled(true);

            } else {
                // Show download status
                layoutDownloadStatus.setVisibility(View.VISIBLE);
                iconTemplateStatus.setVisibility(View.VISIBLE);
                iconTemplateStatus.setImageResource(R.drawable.ic_download);

                btnDownloadTemplate.setVisibility(View.VISIBLE);
                btnPreviewTemplate.setEnabled(false);
                btnApplyTemplate.setEnabled(false);
            }
        }
    }

    private void setupClickListeners() {
        layoutDownloadStatus.setOnClickListener(v -> downloadCurrentTemplate());
        
        btnDownloadTemplate.setOnClickListener(v -> downloadCurrentTemplate());
        
        btnPreviewTemplate.setOnClickListener(v -> previewCurrentTemplate());
        
        btnApplyTemplate.setOnClickListener(v -> applyCurrentTemplate());
        
        fabRefresh.setOnClickListener(v -> refreshTemplates());
    }

    private void downloadCurrentTemplate() {
        if (currentSelectedPosition >= 0 && currentSelectedPosition < templatesList.size()) {
            AzkarTemplate template = templatesList.get(currentSelectedPosition);
            if (!template.isDownloaded()) {
                startDownload(template);
            }
        }
    }

    private void startDownload(AzkarTemplate template) {
        // Show download progress
        iconDownloadStatus.setImageResource(R.drawable.ic_download);
        textDownloadStatus.setText("جاري التحميل...");
        progressDownload.setVisibility(View.VISIBLE);
        progressDownload.setProgress(0);
        
        // Simulate download progress
        simulateDownload(template);
    }

    private void simulateDownload(AzkarTemplate template) {
        // This would be replaced with actual download logic
        new Thread(() -> {
            for (int i = 0; i <= 100; i += 10) {
                final int progress = i;
                runOnUiThread(() -> progressDownload.setProgress(progress));
                
                try {
                    Thread.sleep(200);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            
            runOnUiThread(() -> {
                template.setDownloaded(true);
                // Set downloaded resources
                template.setLandscapeImage(R.drawable.ic_template);
                template.setPortraitImage(R.drawable.ic_template);
                template.setLayoutResource("downloaded_layout");

                updatePreview();
                templateAdapter.notifyItemChanged(currentSelectedPosition);
                Toast.makeText(this, "تم تحميل القالب بنجاح", Toast.LENGTH_SHORT).show();
            });
        }).start();
    }

    private void previewCurrentTemplate() {
        if (currentSelectedPosition >= 0 && currentSelectedPosition < templatesList.size()) {
            AzkarTemplate template = templatesList.get(currentSelectedPosition);
            if (template.isDownloaded()) {
                Toast.makeText(this, "معاينة القالب: " + template.getName(), Toast.LENGTH_SHORT).show();
                // Here you would implement actual preview functionality
            }
        }
    }

    private void applyCurrentTemplate() {
        if (currentSelectedPosition >= 0 && currentSelectedPosition < templatesList.size()) {
            AzkarTemplate template = templatesList.get(currentSelectedPosition);
            if (template.isDownloaded()) {
                // Save selected template
                sharedPreferences.edit().putString(KEY_SELECTED_TEMPLATE, template.getId()).apply();
                Toast.makeText(this, "تم تطبيق القالب: " + template.getName(), Toast.LENGTH_SHORT).show();
                finish();
            }
        }
    }

    private void refreshTemplates() {
        Toast.makeText(this, "جاري تحديث القوالب...", Toast.LENGTH_SHORT).show();
        loadTemplates();
        templateAdapter.notifyDataSetChanged();
        setupIndicators();
        updateIndicators();
        updatePreview();
    }

    @Override
    public void onTemplateSelected(AzkarTemplate template, int position) {
        currentSelectedPosition = position;
        viewpagerTemplates.setCurrentItem(position, true);
    }

    @Override
    public void onTemplateDownload(AzkarTemplate template, int position) {
        if (!template.isDownloaded()) {
            startDownload(template);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    // Inner class for azkar template data
    public static class AzkarTemplate {
        public enum AzkarType {
            MORNING, EVENING, POST_PRAYER, CUSTOM
        }

        public enum AzkarCategory {
            LOCAL, API, CUSTOM
        }

        private String id;
        private String name;
        private String description;
        private int previewImage;
        private int landscapeImage;
        private int portraitImage;
        private String layoutResource;
        private AzkarType type;
        private AzkarCategory category;
        private boolean isDownloaded;

        public AzkarTemplate(String id, String name, String description) {
            this.id = id;
            this.name = name;
            this.description = description;
        }

        // Getters and setters
        public String getId() { return id; }
        public String getName() { return name; }
        public String getDescription() { return description; }
        public int getPreviewImage() { return previewImage; }
        public void setPreviewImage(int previewImage) { this.previewImage = previewImage; }
        public int getLandscapeImage() { return landscapeImage; }
        public void setLandscapeImage(int landscapeImage) { this.landscapeImage = landscapeImage; }
        public int getPortraitImage() { return portraitImage; }
        public void setPortraitImage(int portraitImage) { this.portraitImage = portraitImage; }
        public String getLayoutResource() { return layoutResource; }
        public void setLayoutResource(String layoutResource) { this.layoutResource = layoutResource; }
        public AzkarType getType() { return type; }
        public void setType(AzkarType type) { this.type = type; }
        public AzkarCategory getCategory() { return category; }
        public void setCategory(AzkarCategory category) { this.category = category; }
        public boolean isDownloaded() { return isDownloaded; }
        public void setDownloaded(boolean downloaded) { isDownloaded = downloaded; }
    }
}
