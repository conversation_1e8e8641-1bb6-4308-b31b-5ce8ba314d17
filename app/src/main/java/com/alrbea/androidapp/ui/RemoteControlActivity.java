
package com.alrbea.androidapp.ui;

import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.Cursor;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.Toolbar;

import com.alrbea.androidapp.R;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class RemoteControlActivity extends BaseActivity {

    private Toolbar toolbar;

    // App 1 buttons and progress
    private Button btnDownloadApp1;
    private Button btnInstallApp1;
    private Button btnOpenApp1;
    private ProgressBar progressApp1;
    private TextView textProgressApp1;

    // App 2 buttons and progress
    private Button btnDownloadApp2;
    private Button btnInstallApp2;
    private Button btnOpenApp2;
    private ProgressBar progressApp2;
    private TextView textProgressApp2;

    // App 3 buttons and progress
    private Button btnDownloadApp3;
    private Button btnInstallApp3;
    private Button btnOpenApp3;
    private ProgressBar progressApp3;
    private TextView textProgressApp3;

    // Download Manager
    private DownloadManager downloadManager;
    private Map<Long, Integer> downloadIds = new HashMap<>();
    private Handler progressHandler = new Handler();
    private boolean isProgressRunning = false;

    // App URLs (replace with your server URLs)
    private static final String APP1_URL = "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/SupportApk%2FTeamViewer15.59.587.apk?alt=media&token=9fdbb542-938b-4eb5-828a-05c4196d108d";
    private static final String APP2_URL = "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/SupportApk%2FTeamViewer15.59.587.apk?alt=media&token=9fdbb542-938b-4eb5-828a-05c4196d108d";
    private static final String APP3_URL = "https://yourserver.com/apps/text-control.apk";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_remote_control_simple);

        initViews();
        setupToolbar();
        setupClickListeners();
        setupDownloadManager();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);

        // App 1 buttons and progress
        btnDownloadApp1 = findViewById(R.id.btn_download_app1);
        btnInstallApp1 = findViewById(R.id.btn_install_app1);
        btnOpenApp1 = findViewById(R.id.btn_open_app1);
        progressApp1 = findViewById(R.id.progress_app1);
        textProgressApp1 = findViewById(R.id.text_progress_percent_app1);

        // App 2 buttons and progress
        btnDownloadApp2 = findViewById(R.id.btn_download_app2);
        btnInstallApp2 = findViewById(R.id.btn_install_app2);
        btnOpenApp2 = findViewById(R.id.btn_open_app2);
        progressApp2 = findViewById(R.id.progress_app2);
        textProgressApp2 = findViewById(R.id.text_progress_percent_app2);

        // App 3 buttons and progress
        btnDownloadApp3 = findViewById(R.id.btn_download_app3);
        btnInstallApp3 = findViewById(R.id.btn_install_app3);
        btnOpenApp3 = findViewById(R.id.btn_open_app3);
        progressApp3 = findViewById(R.id.progress_app3);
        textProgressApp3 = findViewById(R.id.text_progress_percent_app3);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("التحكم عن بُعد");
        }
    }
    
    private void setupClickListeners() {
        // App 1: تحكم الصلاة
        btnDownloadApp1.setOnClickListener(v -> downloadAppWithManager(1, "تحكم الصلاة", APP1_URL, "prayer-control.apk"));
        btnInstallApp1.setOnClickListener(v -> installApp("تحكم الصلاة", "com.alrbea.prayercontrol"));
        btnOpenApp1.setOnClickListener(v -> openApp("تحكم الصلاة", "com.alrbea.prayercontrol"));

        // App 2: تحكم المعرض
        btnDownloadApp2.setOnClickListener(v -> downloadAppWithManager(2, "تحكم المعرض", APP2_URL, "gallery-control.apk"));
        btnInstallApp2.setOnClickListener(v -> installApp("تحكم المعرض", "com.alrbea.gallerycontrol"));
        btnOpenApp2.setOnClickListener(v -> openApp("تحكم المعرض", "com.alrbea.gallerycontrol"));

        // App 3: تحكم النصوص
        btnDownloadApp3.setOnClickListener(v -> downloadAppWithManager(3, "تحكم النصوص", APP3_URL, "text-control.apk"));
        btnInstallApp3.setOnClickListener(v -> installApp("تحكم النصوص", "com.alrbea.textcontrol"));
        btnOpenApp3.setOnClickListener(v -> openApp("تحكم النصوص", "com.alrbea.textcontrol"));
    }

    private void setupDownloadManager() {
        downloadManager = (DownloadManager) getSystemService(Context.DOWNLOAD_SERVICE);

        // Register broadcast receiver for download completion
        IntentFilter filter = new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE);

        // For Android 13+ (API 33+), we need to specify RECEIVER_NOT_EXPORTED
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(downloadReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            registerReceiver(downloadReceiver, filter);
        }
    }
    
    private void downloadAppWithManager(int appId, String appName, String downloadUrl, String fileName) {
        // Check internet connection
        if (!isInternetConnected()) {
            showNoInternetDialog();
            return;
        }

        try {
            // Create download request
            DownloadManager.Request request = new DownloadManager.Request(Uri.parse(downloadUrl));
            request.setTitle("تحميل " + appName);
            request.setDescription("جاري تحميل " + appName + "...");
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
            request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileName);
            request.setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI | DownloadManager.Request.NETWORK_MOBILE);
            request.setAllowedOverRoaming(false);

            // Start download
            long downloadId = downloadManager.enqueue(request);
            downloadIds.put(downloadId, appId);

            // Show progress
            showDownloadProgress(appId, true);
            updateDownloadButton(appId, "جاري التحميل...", false);

            // Start progress tracking
            startProgressTracking(downloadId, appId);

            Toast.makeText(this, "بدء تحميل " + appName, Toast.LENGTH_SHORT).show();

        } catch (Exception e) {
            Toast.makeText(this, "حدث خطأ في التحميل: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    private void installApp(String appName, String packageName) {
        try {
            // Check if app is already installed
            if (isAppInstalled(packageName)) {
                Toast.makeText(this, appName + " مثبت بالفعل", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // Try to install from Play Store
            Intent playStoreIntent = new Intent(Intent.ACTION_VIEW, 
                Uri.parse("market://details?id=" + packageName));
            
            if (playStoreIntent.resolveActivity(getPackageManager()) != null) {
                startActivity(playStoreIntent);
                Toast.makeText(this, "جاري فتح متجر التطبيقات لتثبيت " + appName, Toast.LENGTH_SHORT).show();
            } else {
                // Fallback to browser
                Intent browserIntent = new Intent(Intent.ACTION_VIEW, 
                    Uri.parse("https://play.google.com/store/apps/details?id=" + packageName));
                startActivity(browserIntent);
                Toast.makeText(this, "جاري فتح متجر التطبيقات", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Toast.makeText(this, "حدث خطأ في التثبيت", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void openApp(String appName, String packageName) {
        try {
            if (!isAppInstalled(packageName)) {
                Toast.makeText(this, appName + " غير مثبت. يرجى تثبيته أولاً", Toast.LENGTH_SHORT).show();
                return;
            }
            
            Intent launchIntent = getPackageManager().getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                startActivity(launchIntent);
                Toast.makeText(this, "جاري فتح " + appName, Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "لا يمكن فتح " + appName, Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Toast.makeText(this, "حدث خطأ في فتح التطبيق", Toast.LENGTH_SHORT).show();
        }
    }
    
    private boolean isAppInstalled(String packageName) {
        try {
            getPackageManager().getPackageInfo(packageName, 0);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isInternetConnected() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetwork = connectivityManager.getActiveNetworkInfo();
            return activeNetwork != null && activeNetwork.isConnectedOrConnecting();
        }
        return false;
    }

    private void showNoInternetDialog() {
        new AlertDialog.Builder(this)
                .setTitle("لا يوجد اتصال بالإنترنت")
                .setMessage("يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى")
                .setPositiveButton("موافق", null)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show();
    }

    private void showDownloadProgress(int appId, boolean show) {
        int visibility = show ? android.view.View.VISIBLE : android.view.View.GONE;

        switch (appId) {
            case 1:
                findViewById(R.id.layout_progress_app1).setVisibility(visibility);
                break;
            case 2:
                findViewById(R.id.layout_progress_app2).setVisibility(visibility);
                break;
            case 3:
                findViewById(R.id.layout_progress_app3).setVisibility(visibility);
                break;
        }
    }

    private void updateDownloadButton(int appId, String text, boolean enabled) {
        Button button = null;
        switch (appId) {
            case 1:
                button = btnDownloadApp1;
                break;
            case 2:
                button = btnDownloadApp2;
                break;
            case 3:
                button = btnDownloadApp3;
                break;
        }

        if (button != null) {
            button.setText(text);
            button.setEnabled(enabled);
            button.setAlpha(enabled ? 1.0f : 0.6f);
        }
    }
    
    private void startProgressTracking(long downloadId, int appId) {
        if (isProgressRunning) return;

        isProgressRunning = true;
        progressHandler.post(new Runnable() {
            @Override
            public void run() {
                updateProgress(downloadId, appId);
                if (isProgressRunning) {
                    progressHandler.postDelayed(this, 1000); // Update every second
                }
            }
        });
    }

    private void updateProgress(long downloadId, int appId) {
        DownloadManager.Query query = new DownloadManager.Query();
        query.setFilterById(downloadId);

        try (Cursor cursor = downloadManager.query(query)) {
            if (cursor.moveToFirst()) {
                int bytesDownloaded = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR));
                int bytesTotal = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES));
                int status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS));

                if (bytesTotal > 0) {
                    int progress = (int) ((bytesDownloaded * 100L) / bytesTotal);
                    updateProgressUI(appId, progress);
                }

                if (status == DownloadManager.STATUS_SUCCESSFUL ||
                    status == DownloadManager.STATUS_FAILED) {
                    isProgressRunning = false;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void updateProgressUI(int appId, int progress) {
        ProgressBar progressBar = null;
        TextView textProgress = null;

        switch (appId) {
            case 1:
                progressBar = progressApp1;
                textProgress = textProgressApp1;
                break;
            case 2:
                progressBar = progressApp2;
                textProgress = textProgressApp2;
                break;
            case 3:
                progressBar = progressApp3;
                textProgress = textProgressApp3;
                break;
        }

        if (progressBar != null && textProgress != null) {
            progressBar.setProgress(progress);
            textProgress.setText(progress + "%");
        }
    }

    private final BroadcastReceiver downloadReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            long downloadId = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1);

            if (downloadIds.containsKey(downloadId)) {
                int appId = downloadIds.get(downloadId);

                DownloadManager.Query query = new DownloadManager.Query();
                query.setFilterById(downloadId);

                try (Cursor cursor = downloadManager.query(query)) {
                    if (cursor.moveToFirst()) {
                        int status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS));

                        if (status == DownloadManager.STATUS_SUCCESSFUL) {
                            showDownloadProgress(appId, false);
                            updateDownloadButton(appId, "تم التحميل", true);
                            updateProgressUI(appId, 100);
                            Toast.makeText(RemoteControlActivity.this, "تم التحميل بنجاح", Toast.LENGTH_SHORT).show();
                        } else if (status == DownloadManager.STATUS_FAILED) {
                            showDownloadProgress(appId, false);
                            updateDownloadButton(appId, "إعادة المحاولة", true);
                            Toast.makeText(RemoteControlActivity.this, "فشل في التحميل", Toast.LENGTH_SHORT).show();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                downloadIds.remove(downloadId);
                isProgressRunning = false;
            }
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        try {
            unregisterReceiver(downloadReceiver);
        } catch (Exception e) {
            // Receiver not registered
        }
        isProgressRunning = false;
        progressHandler.removeCallbacksAndMessages(null);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}

