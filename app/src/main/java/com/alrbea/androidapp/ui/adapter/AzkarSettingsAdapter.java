package com.alrbea.androidapp.ui.adapter;

import android.app.TimePickerDialog;
import android.content.Context;
import android.text.InputType;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.AzkarSetting;
import com.alrbea.androidapp.utils.LocaleHelper;
import com.alrbea.androidapp.utils.ResponsiveLayoutManager;
import com.alrbea.androidapp.utils.ScreenUtils;

import java.util.Calendar;
import java.util.List;

public class AzkarSettingsAdapter extends RecyclerView.Adapter<AzkarSettingsAdapter.SettingViewHolder> {
    
    public interface OnSettingChangeListener {
        void onSettingChanged(AzkarSetting setting, Object value);
    }
    
    private List<AzkarSetting> settings;
    private OnSettingChangeListener changeListener;
    private Context context;

    public AzkarSettingsAdapter(List<AzkarSetting> settings, OnSettingChangeListener changeListener) {
        this.settings = settings;
        this.changeListener = changeListener;
    }

    @NonNull
    @Override
    public SettingViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context)
                .inflate(R.layout.item_azkar_setting, parent, false);
        return new SettingViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SettingViewHolder holder, int position) {
        AzkarSetting setting = settings.get(position);
        holder.bind(setting);
    }

    @Override
    public int getItemCount() {
        return settings.size();
    }

    class SettingViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private ImageView iconImageView;
        private TextView titleTextView;
        private TextView descriptionTextView;
        private TextView valueTextView;
        private Switch toggleSwitch;
        private ImageView expandIcon;

        public SettingViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_setting);
            iconImageView = itemView.findViewById(R.id.iv_setting_icon);
            titleTextView = itemView.findViewById(R.id.tv_setting_title);
            descriptionTextView = itemView.findViewById(R.id.tv_setting_description);
            valueTextView = itemView.findViewById(R.id.tv_setting_value);
            toggleSwitch = itemView.findViewById(R.id.switch_setting);
            expandIcon = itemView.findViewById(R.id.iv_expand_icon);
        }

        public void bind(AzkarSetting setting) {
            String language = LocaleHelper.getLanguage(context);
            
            // Set setting data
            iconImageView.setImageResource(setting.getIconRes());
            titleTextView.setText(setting.getDisplayTitle(language));
            descriptionTextView.setText(setting.getDisplayDescription(language));
            
            // Hide all controls first
            toggleSwitch.setVisibility(View.GONE);
            valueTextView.setVisibility(View.GONE);
            expandIcon.setVisibility(View.GONE);
            
            // Show appropriate control based on setting type
            switch (setting.getType()) {
                case TOGGLE:
                case EXPANDABLE_TOGGLE:
                    toggleSwitch.setVisibility(View.VISIBLE);
                    toggleSwitch.setOnCheckedChangeListener(null);
                    toggleSwitch.setChecked(setting.isEnabled());
                    toggleSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                        if (changeListener != null) {
                            changeListener.onSettingChanged(setting, isChecked);
                        }
                    });
                    
                    if (setting.getType() == AzkarSetting.SettingType.EXPANDABLE_TOGGLE) {
                        expandIcon.setVisibility(View.VISIBLE);
                        expandIcon.setRotation(setting.isExpanded() ? 180f : 0f);
                    }
                    break;
                    
                case TIME_PICKER:
                    valueTextView.setVisibility(View.VISIBLE);
                    String timeValue = setting.getValue() != null ? setting.getValue() : "06:00";
                    valueTextView.setText(timeValue);
                    cardView.setOnClickListener(v -> showTimePickerDialog(setting));
                    break;
                    
                case NUMBER_INPUT:
                    valueTextView.setVisibility(View.VISIBLE);
                    String numberValue = setting.getValue() != null ? setting.getValue() : "5";
                    String unit = setting.getId().contains("duration") && !setting.getId().contains("azkar_duration") ? " دقائق" : " ثانية";
                    valueTextView.setText(numberValue + unit);
                    cardView.setOnClickListener(v -> showNumberInputDialog(setting));
                    break;
                    
                case PRAYER_SELECTOR:
                    valueTextView.setVisibility(View.VISIBLE);
                    String prayerValue = setting.getValue() != null ? setting.getValue() : "all";
                    valueTextView.setText(getPrayerDisplayName(prayerValue));
                    cardView.setOnClickListener(v -> showPrayerSelectionDialog(setting));
                    break;
                    
                case SELECTION:
                    valueTextView.setVisibility(View.VISIBLE);
                    String selectionValue = setting.getValue() != null ? setting.getValue() : "fade";
                    valueTextView.setText(getTransitionDisplayName(selectionValue));
                    cardView.setOnClickListener(v -> showSelectionDialog(setting));
                    break;
            }
            
            // Apply responsive styling
            applyResponsiveLayout();
            
            // Apply enabled/disabled styling
            float alpha = setting.isEnabled() || setting.getType() != AzkarSetting.SettingType.EXPANDABLE_TOGGLE ? 1.0f : 0.6f;
            iconImageView.setAlpha(alpha);
            titleTextView.setAlpha(alpha);
            descriptionTextView.setAlpha(alpha);
        }
        
        private void showTimePickerDialog(AzkarSetting setting) {
            Calendar calendar = Calendar.getInstance();
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            
            // Parse current value if exists
            if (setting.getValue() != null) {
                String[] timeParts = setting.getValue().split(":");
                if (timeParts.length == 2) {
                    hour = Integer.parseInt(timeParts[0]);
                    minute = Integer.parseInt(timeParts[1]);
                }
            }
            
            TimePickerDialog timePickerDialog = new TimePickerDialog(context,
                    (view, selectedHour, selectedMinute) -> {
                        String timeValue = String.format("%02d:%02d", selectedHour, selectedMinute);
                        if (changeListener != null) {
                            changeListener.onSettingChanged(setting, timeValue);
                        }
                        valueTextView.setText(timeValue);
                    }, hour, minute, true);
            
            timePickerDialog.show();
        }
        
        private void showNumberInputDialog(AzkarSetting setting) {
            EditText editText = new EditText(context);
            editText.setInputType(InputType.TYPE_CLASS_NUMBER);
            
            String hint = setting.getId().contains("duration") && !setting.getId().contains("azkar_duration") ? 
                         "أدخل المدة بالدقائق" : "أدخل المدة بالثواني";
            editText.setHint(hint);
            editText.setText(setting.getValue() != null ? setting.getValue() : "5");
            
            new AlertDialog.Builder(context)
                    .setTitle(setting.getDisplayTitle(LocaleHelper.getLanguage(context)))
                    .setView(editText)
                    .setPositiveButton("حفظ", (dialog, which) -> {
                        String value = editText.getText().toString();
                        if (!value.isEmpty()) {
                            if (changeListener != null) {
                                changeListener.onSettingChanged(setting, value);
                            }
                            String unit = setting.getId().contains("duration") && !setting.getId().contains("azkar_duration") ? 
                                         " دقائق" : " ثانية";
                            valueTextView.setText(value + unit);
                        }
                    })
                    .setNegativeButton("إلغاء", null)
                    .show();
        }
        
        private void showPrayerSelectionDialog(AzkarSetting setting) {
            String[] options = {"all", "fajr", "dhuhr", "asr", "maghrib", "isha", "custom"};
            String[] displayNames = {"جميع الصلوات", "الفجر فقط", "الظهر فقط", "العصر فقط", "المغرب فقط", "العشاء فقط", "مخصص"};
            
            boolean[] checkedItems = new boolean[options.length];
            String currentValue = setting.getValue() != null ? setting.getValue() : "all";
            
            if ("all".equals(currentValue)) {
                checkedItems[0] = true;
            } else {
                String[] selectedPrayers = currentValue.split(",");
                for (String prayer : selectedPrayers) {
                    for (int i = 1; i < options.length - 1; i++) {
                        if (options[i].equals(prayer.trim())) {
                            checkedItems[i] = true;
                            break;
                        }
                    }
                }
            }
            
            new AlertDialog.Builder(context)
                    .setTitle("اختر الصلوات")
                    .setMultiChoiceItems(displayNames, checkedItems, (dialog, which, isChecked) -> {
                        checkedItems[which] = isChecked;
                        if (which == 0 && isChecked) { // "جميع الصلوات" selected
                            for (int i = 1; i < checkedItems.length; i++) {
                                checkedItems[i] = false;
                            }
                        } else if (which > 0 && isChecked) { // Individual prayer selected
                            checkedItems[0] = false;
                        }
                    })
                    .setPositiveButton("حفظ", (dialog, which) -> {
                        StringBuilder selectedValue = new StringBuilder();
                        if (checkedItems[0]) {
                            selectedValue.append("all");
                        } else {
                            for (int i = 1; i < checkedItems.length - 1; i++) {
                                if (checkedItems[i]) {
                                    if (selectedValue.length() > 0) {
                                        selectedValue.append(",");
                                    }
                                    selectedValue.append(options[i]);
                                }
                            }
                        }
                        
                        String finalValue = selectedValue.toString().isEmpty() ? "all" : selectedValue.toString();
                        if (changeListener != null) {
                            changeListener.onSettingChanged(setting, finalValue);
                        }
                        valueTextView.setText(getPrayerDisplayName(finalValue));
                    })
                    .setNegativeButton("إلغاء", null)
                    .show();
        }
        
        private void showSelectionDialog(AzkarSetting setting) {
            String[] options = {"fade", "slide", "zoom", "flip"};
            String[] displayNames = {"تلاشي", "انزلاق", "تكبير", "قلب"};
            
            int selectedIndex = 0;
            if (setting.getValue() != null) {
                for (int i = 0; i < options.length; i++) {
                    if (options[i].equals(setting.getValue())) {
                        selectedIndex = i;
                        break;
                    }
                }
            }
            
            new AlertDialog.Builder(context)
                    .setTitle("اختر تأثير الانتقال")
                    .setSingleChoiceItems(displayNames, selectedIndex, (dialog, which) -> {
                        String selectedValue = options[which];
                        if (changeListener != null) {
                            changeListener.onSettingChanged(setting, selectedValue);
                        }
                        valueTextView.setText(displayNames[which]);
                        dialog.dismiss();
                    })
                    .setNegativeButton("إلغاء", null)
                    .show();
        }
        
        private String getPrayerDisplayName(String value) {
            if ("all".equals(value)) {
                return "جميع الصلوات";
            } else {
                String[] prayers = value.split(",");
                StringBuilder display = new StringBuilder();
                for (String prayer : prayers) {
                    if (display.length() > 0) {
                        display.append("، ");
                    }
                    switch (prayer.trim()) {
                        case "fajr": display.append("الفجر"); break;
                        case "dhuhr": display.append("الظهر"); break;
                        case "asr": display.append("العصر"); break;
                        case "maghrib": display.append("المغرب"); break;
                        case "isha": display.append("العشاء"); break;
                    }
                }
                return display.toString();
            }
        }
        
        private String getTransitionDisplayName(String value) {
            switch (value) {
                case "fade": return "تلاشي";
                case "slide": return "انزلاق";
                case "zoom": return "تكبير";
                case "flip": return "قلب";
                default: return "تلاشي";
            }
        }
        
        private void applyResponsiveLayout() {
            ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(context);
            
            // Apply responsive padding
            int padding = ResponsiveLayoutManager.getResponsivePadding(context, screenType);
            cardView.setContentPadding(padding, padding, padding, padding);
            
            // Apply responsive text sizes
            ResponsiveLayoutManager.applyResponsiveTextSize(context, titleTextView, 16f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, descriptionTextView, 14f);
            ResponsiveLayoutManager.applyResponsiveTextSize(context, valueTextView, 14f);
            
            // Apply responsive icon size
            int iconSize = getResponsiveIconSize(screenType);
            ViewGroup.LayoutParams iconParams = iconImageView.getLayoutParams();
            iconParams.width = iconSize;
            iconParams.height = iconSize;
            iconImageView.setLayoutParams(iconParams);
        }
        
        private int getResponsiveIconSize(ScreenUtils.ScreenType screenType) {
            switch (screenType) {
                case PHONE_PORTRAIT:
                case PHONE_LANDSCAPE:
                    return ScreenUtils.dpToPx(context, 40);
                case TABLET_PORTRAIT:
                case TABLET_LANDSCAPE:
                    return ScreenUtils.dpToPx(context, 48);
                case TV:
                    return ScreenUtils.dpToPx(context, 56);
                default:
                    return ScreenUtils.dpToPx(context, 40);
            }
        }
    }
}
