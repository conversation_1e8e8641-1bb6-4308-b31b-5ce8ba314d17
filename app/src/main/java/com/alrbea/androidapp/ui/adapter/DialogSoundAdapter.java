package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.ui.NotificationSettingsActivity;

import java.util.List;

public class DialogSoundAdapter extends RecyclerView.Adapter<DialogSoundAdapter.SoundViewHolder> {

    public interface OnSoundActionListener {
        void onSoundSelected(NotificationSettingsActivity.SoundItem soundItem, int position);
        void onSoundPlay(NotificationSettingsActivity.SoundItem soundItem, int position);
        void onSoundStop();
    }

    private List<NotificationSettingsActivity.SoundItem> sounds;
    private OnSoundActionListener listener;
    private String currentSelection;
    private Context context;
    private int selectedPosition = -1;
    private int playingPosition = -1;

    public DialogSoundAdapter(List<NotificationSettingsActivity.SoundItem> sounds, OnSoundActionListener listener, String currentSelection) {
        this.sounds = sounds;
        this.listener = listener;
        this.currentSelection = currentSelection;
        
        // Find current selection position
        for (int i = 0; i < sounds.size(); i++) {
            if (sounds.get(i).getName().equals(currentSelection)) {
                selectedPosition = i;
                break;
            }
        }
    }

    @NonNull
    @Override
    public SoundViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context).inflate(R.layout.item_dialog_sound, parent, false);
        return new SoundViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SoundViewHolder holder, int position) {
        NotificationSettingsActivity.SoundItem sound = sounds.get(position);
        holder.bind(sound, position);
    }

    @Override
    public int getItemCount() {
        return sounds.size();
    }

    public void setSelectedPosition(int position) {
        int previousSelected = selectedPosition;
        selectedPosition = position;
        
        if (previousSelected != -1) {
            notifyItemChanged(previousSelected);
        }
        if (position != -1) {
            notifyItemChanged(position);
        }
    }

    public void setPlayingPosition(int position) {
        int previousPlaying = playingPosition;
        playingPosition = position;
        
        if (previousPlaying != -1) {
            notifyItemChanged(previousPlaying);
        }
        if (position != -1) {
            notifyItemChanged(position);
        }
    }

    public void stopPlaying() {
        setPlayingPosition(-1);
    }

    public NotificationSettingsActivity.SoundItem getSelectedSound() {
        if (selectedPosition >= 0 && selectedPosition < sounds.size()) {
            return sounds.get(selectedPosition);
        }
        return null;
    }

    class SoundViewHolder extends RecyclerView.ViewHolder {
        private RadioButton radioSound;
        private TextView textSoundName;
        private ImageView btnPlaySound;

        public SoundViewHolder(@NonNull View itemView) {
            super(itemView);
            radioSound = itemView.findViewById(R.id.radio_sound);
            textSoundName = itemView.findViewById(R.id.text_sound_name);
            btnPlaySound = itemView.findViewById(R.id.btn_play_sound);
        }

        public void bind(NotificationSettingsActivity.SoundItem sound, int position) {
            textSoundName.setText(sound.getName());
            
            // Set radio button state
            radioSound.setChecked(position == selectedPosition);
            
            // Set play button icon
            boolean isPlaying = playingPosition == position;
            btnPlaySound.setImageResource(isPlaying ? R.drawable.ic_stop : R.drawable.ic_play);
            
            // Radio button click
            radioSound.setOnClickListener(v -> {
                setSelectedPosition(position);
                if (listener != null) {
                    listener.onSoundSelected(sound, position);
                }
            });
            
            // Item click to select
            itemView.setOnClickListener(v -> {
                setSelectedPosition(position);
                if (listener != null) {
                    listener.onSoundSelected(sound, position);
                }
            });
            
            // Play button click
            btnPlaySound.setOnClickListener(v -> {
                if (isPlaying) {
                    listener.onSoundStop();
                    setPlayingPosition(-1);
                } else {
                    listener.onSoundPlay(sound, position);
                    setPlayingPosition(position);
                }
            });
        }
    }
}
