package com.alrbea.androidapp.ui;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.Event;
import com.alrbea.androidapp.ui.adapter.EventsAdapter;
import com.alrbea.androidapp.utils.EventsManager;

import java.util.List;

public class EventsSettingsActivity extends BaseActivity implements EventsAdapter.OnEventActionListener {

    private Toolbar toolbar;
    private SwitchCompat switchEnableEvents;
    private LinearLayout layoutSettingsContainer;
    private Button btnAddEvent;
    private RecyclerView recyclerViewEvents;
    private LinearLayout layoutEmptyState;

    private EventsManager eventsManager;
    private EventsAdapter eventsAdapter;
    private List<Event> eventsList;
    private ActivityResultLauncher<Intent> addEditEventLauncher;
    


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_events_settings);

        initializeViews();
        setupToolbar();
        setupEventsManager();
        setupRecyclerView();
        setupListeners();
        loadEvents();
    }

    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        switchEnableEvents = findViewById(R.id.switch_enable_events);
        layoutSettingsContainer = findViewById(R.id.layout_settings_container);
        btnAddEvent = findViewById(R.id.btn_add_event);
        recyclerViewEvents = findViewById(R.id.recycler_view_events);
        layoutEmptyState = findViewById(R.id.layout_empty_state);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }
    }

    private void setupEventsManager() {
        eventsManager = new EventsManager(this);

        // Setup activity result launcher
        addEditEventLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == AddEditEventActivity.RESULT_EVENT_SAVED) {
                    loadEvents();
                }
            }
        );
    }

    private void setupRecyclerView() {
        eventsList = eventsManager.getAllEvents();
        eventsAdapter = new EventsAdapter(eventsList, this);
        recyclerViewEvents.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewEvents.setAdapter(eventsAdapter);
    }
    
    private void setupListeners() {
        switchEnableEvents.setOnCheckedChangeListener((buttonView, isChecked) -> {
            layoutSettingsContainer.setVisibility(isChecked ? View.VISIBLE : View.GONE);
            eventsManager.setEventsFeatureEnabled(isChecked);
        });

        btnAddEvent.setOnClickListener(v -> openAddEventActivity());
    }
    
    private void loadEvents() {
        boolean isEnabled = eventsManager.isEventsFeatureEnabled();
        switchEnableEvents.setChecked(isEnabled);
        layoutSettingsContainer.setVisibility(isEnabled ? View.VISIBLE : View.GONE);

        eventsList = eventsManager.getAllEvents();
        eventsAdapter.updateEvents(eventsList);
        updateEmptyState();
    }

    private void updateEmptyState() {
        if (eventsList.isEmpty()) {
            recyclerViewEvents.setVisibility(View.GONE);
            layoutEmptyState.setVisibility(View.VISIBLE);
        } else {
            recyclerViewEvents.setVisibility(View.VISIBLE);
            layoutEmptyState.setVisibility(View.GONE);
        }
    }
    
    private void openAddEventActivity() {
        Intent intent = new Intent(this, AddEditEventActivity.class);
        addEditEventLauncher.launch(intent);
    }

    private void openEditEventActivity(Event event) {
        Intent intent = new Intent(this, AddEditEventActivity.class);
        intent.putExtra(AddEditEventActivity.EXTRA_EVENT_ID, event.getId());
        addEditEventLauncher.launch(intent);
    }

    // EventsAdapter.OnEventActionListener implementation
    @Override
    public void onEventToggled(Event event, boolean isEnabled) {
        eventsManager.toggleEventEnabled(event.getId(), isEnabled);
        loadEvents();
    }

    @Override
    public void onEventEdit(Event event) {
        openEditEventActivity(event);
    }

    @Override
    public void onEventDelete(Event event) {
        new AlertDialog.Builder(this)
                .setTitle(R.string.delete_event)
                .setMessage(R.string.confirm_delete_event)
                .setPositiveButton(R.string.delete_event, (dialog, which) -> {
                    eventsManager.deleteEvent(event.getId());
                    loadEvents();
                    showToast(getString(R.string.event_deleted));
                })
                .setNegativeButton(R.string.cancel, null)
                .show();
    }

    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
