package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.ThemeTemplate;
import com.alrbea.androidapp.ui.ThemeTemplatesActivity;

import java.util.List;

public class ThemeTemplateAdapter extends RecyclerView.Adapter<ThemeTemplateAdapter.ThemeViewHolder> {

    public interface OnThemeActionListener {
        void onThemeSelected(ThemeTemplate theme, int position);
        void onThemeDownload(ThemeTemplate theme, int position);
    }

    private List<ThemeTemplate> themes;
    private OnThemeActionListener listener;
    private Context context;
    private int selectedPosition = -1;
    private String orientation = "portrait";

    public ThemeTemplateAdapter(List<ThemeTemplate> themes, OnThemeActionListener listener, String orientation) {
        this.themes = themes;
        this.listener = listener;
        this.orientation = orientation;
    }

    @NonNull
    @Override
    public ThemeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context).inflate(R.layout.item_theme_grid_image, parent, false);
        return new ThemeViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ThemeViewHolder holder, int position) {
        ThemeTemplate theme = themes.get(position);
        holder.bind(theme, position);
    }

    @Override
    public int getItemCount() {
        return themes.size();
    }

    public void setSelectedPosition(int position) {
        int previousSelected = selectedPosition;
        selectedPosition = position;

        if (previousSelected != -1) {
            notifyItemChanged(previousSelected);
        }
        if (position != -1) {
            notifyItemChanged(position);
        }
    }

    public void setOrientation(String orientation) {
        this.orientation = orientation;
        notifyDataSetChanged();
    }

    class ThemeViewHolder extends RecyclerView.ViewHolder {
        private ImageView imgThemeThumbnail;
        private View selectionBorder;
        public ThemeViewHolder(@NonNull View itemView) {
            super(itemView);
            imgThemeThumbnail = itemView.findViewById(R.id.img_theme_thumbnail);
            // Add a border view programmatically for selection
            selectionBorder = new View(itemView.getContext());
            selectionBorder.setBackgroundResource(R.drawable.selection_border);
            ((androidx.cardview.widget.CardView)itemView).addView(selectionBorder,
                new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            selectionBorder.setVisibility(View.GONE);
        }
        public void bind(ThemeTemplate theme, int position) {
            int imageResource = "landscape".equals(orientation) ? theme.getLandscapeImage() : theme.getPortraitImage();
            if (imageResource != 0) {
                imgThemeThumbnail.setImageResource(imageResource);
            } else {
                imgThemeThumbnail.setImageResource(imageResource);
            }
            // Show border if selected
            if (position == selectedPosition) {
                selectionBorder.setVisibility(View.VISIBLE);
                ((androidx.cardview.widget.CardView)itemView).setCardElevation(
                    itemView.getResources().getDimension(R.dimen._12sdp));
            } else {
                selectionBorder.setVisibility(View.GONE);
                ((androidx.cardview.widget.CardView)itemView).setCardElevation(
                    itemView.getResources().getDimension(R.dimen._4sdp));
            }
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onThemeSelected(theme, position);
                }
            });
        }
    }
}
