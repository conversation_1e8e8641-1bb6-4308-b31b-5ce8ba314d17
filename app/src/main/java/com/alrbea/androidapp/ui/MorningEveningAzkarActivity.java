package com.alrbea.androidapp.ui;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.SeekBar;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;

public class MorningEveningAzkarActivity extends BaseActivity {
    
    private Toolbar toolbar;
    private Switch checkBoxMorning;
    private TextView textViewMorning;
    private TextView textViewMorningDescribe;
    private TextView textViewMorningTime;
    private SeekBar seekBarMorningTime;
    private Button buttonEditMorning;
    private RecyclerView recyclerViewMorning;

    private Switch checkBoxEvening;
    private TextView textViewEvening;
    private TextView textViewEveningDescribe;
    private TextView textViewEveningTime;
    private SeekBar seekBarEveningTime;
    private Button buttonEditEvening;
    private RecyclerView recyclerViewEvening;
    
    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_morning_evening_azkar);
        
        initializeViews();
        setupToolbar();
        setupSharedPreferences();
        setupListeners();
        loadSavedSettings();
    }
    
    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);

        // Morning Azkar views (now using Switch instead of CheckBox)
        checkBoxMorning = findViewById(R.id.citationForMorning_CheckBox_AthkarFragment);
        textViewMorning = findViewById(R.id.citationForMorning_TextView_AthkarFragment);
        textViewMorningDescribe = findViewById(R.id.citationForMorningDescribe_TextView_AthkarFragment);
        textViewMorningTime = findViewById(R.id.citationForMorningTime_TextView_AthkarFragment);
        seekBarMorningTime = findViewById(R.id.citationForMorningTime_SeekBar_AthkarFragment);
        buttonEditMorning = findViewById(R.id.b1);
        recyclerViewMorning = findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingFragment);

        // Evening Azkar views (now using Switch instead of CheckBox)
        checkBoxEvening = findViewById(R.id.citationForEvening_CheckBox_AthkarFragment);
        textViewEvening = findViewById(R.id.citationForEvening_TextView_AthkarFragment);
        textViewEveningDescribe = findViewById(R.id.citationForEveningDescribe_TextView_AthkarFragment);
        textViewEveningTime = findViewById(R.id.citationForEveningTime_TextView_AthkarFragment);
        seekBarEveningTime = findViewById(R.id.citationForEveningTime_SeekBar_AthkarFragment);
        buttonEditEvening = findViewById(R.id.b2);
        recyclerViewEvening = findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingFragmentE);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle(getString(R.string.morning_evening_azkar));
        }
    }
    
    private void setupSharedPreferences() {
        sharedPreferences = getSharedPreferences("morning_evening_azkar_prefs", MODE_PRIVATE);
    }
    
    private void setupListeners() {
        checkBoxMorning.setOnCheckedChangeListener((buttonView, isChecked) -> {
            sharedPreferences.edit()
                .putBoolean("morning_azkar_enabled", isChecked)
                .apply();
            updateMorningVisibility(isChecked);
        });
        
        checkBoxEvening.setOnCheckedChangeListener((buttonView, isChecked) -> {
            sharedPreferences.edit()
                .putBoolean("evening_azkar_enabled", isChecked)
                .apply();
            updateEveningVisibility(isChecked);
        });
        
        seekBarMorningTime.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    int minutes = progress - 50; // -50 to +50 minutes
                    String timeText = minutes >= 0 ? 
                        "بعد شروق الشمس بـ " + minutes + " دقيقة" :
                        "قبل شروق الشمس بـ " + Math.abs(minutes) + " دقيقة";
                    textViewMorningTime.setText(timeText);
                }
            }
            
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}
            
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                sharedPreferences.edit()
                    .putInt("morning_time_offset", seekBar.getProgress() - 50)
                    .apply();
            }
        });
        
        seekBarEveningTime.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    int minutes = progress - 50; // -50 to +50 minutes
                    String timeText = minutes >= 0 ? 
                        "بعد غروب الشمس بـ " + minutes + " دقيقة" :
                        "قبل غروب الشمس بـ " + Math.abs(minutes) + " دقيقة";
                    textViewEveningTime.setText(timeText);
                }
            }
            
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}
            
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                sharedPreferences.edit()
                    .putInt("evening_time_offset", seekBar.getProgress() - 50)
                    .apply();
            }
        });
        
        buttonEditMorning.setOnClickListener(v -> {
            seekBarMorningTime.setVisibility(
                seekBarMorningTime.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE
            );
        });
        
        buttonEditEvening.setOnClickListener(v -> {
            seekBarEveningTime.setVisibility(
                seekBarEveningTime.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE
            );
        });
    }
    
    private void loadSavedSettings() {
        boolean morningEnabled = sharedPreferences.getBoolean("morning_azkar_enabled", true);
        boolean eveningEnabled = sharedPreferences.getBoolean("evening_azkar_enabled", true);
        
        checkBoxMorning.setChecked(morningEnabled);
        checkBoxEvening.setChecked(eveningEnabled);
        
        updateMorningVisibility(morningEnabled);
        updateEveningVisibility(eveningEnabled);
        
        // Load time offsets
        int morningOffset = sharedPreferences.getInt("morning_time_offset", 0);
        int eveningOffset = sharedPreferences.getInt("evening_time_offset", 0);
        
        seekBarMorningTime.setProgress(morningOffset + 50);
        seekBarEveningTime.setProgress(eveningOffset + 50);
        
        // Update time text
        String morningTimeText = morningOffset >= 0 ? 
            "بعد شروق الشمس بـ " + morningOffset + " دقيقة" :
            "قبل شروق الشمس بـ " + Math.abs(morningOffset) + " دقيقة";
        textViewMorningTime.setText(morningTimeText);
        
        String eveningTimeText = eveningOffset >= 0 ? 
            "بعد غروب الشمس بـ " + eveningOffset + " دقيقة" :
            "قبل غروب الشمس بـ " + Math.abs(eveningOffset) + " دقيقة";
        textViewEveningTime.setText(eveningTimeText);
        
        // Setup RecyclerViews (placeholder for now)
        setupRecyclerViews();
    }
    
    private void updateMorningVisibility(boolean enabled) {
        View morningTimeControls = findViewById(R.id.morning_time_controls);
        if (morningTimeControls != null) {
            morningTimeControls.setVisibility(enabled ? View.VISIBLE : View.GONE);
        }
    }

    private void updateEveningVisibility(boolean enabled) {
        View eveningTimeControls = findViewById(R.id.evening_time_controls);
        if (eveningTimeControls != null) {
            eveningTimeControls.setVisibility(enabled ? View.VISIBLE : View.GONE);
        }
    }
    
    private void setupRecyclerViews() {
        // Setup morning RecyclerView
        recyclerViewMorning.setLayoutManager(new LinearLayoutManager(this));
        // TODO: Add adapter for morning azkar options

        // Setup evening RecyclerView
        recyclerViewEvening.setLayoutManager(new LinearLayoutManager(this));
        // TODO: Add adapter for evening azkar options
    }
    
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
