package com.alrbea.androidapp.ui;

import android.app.AlertDialog;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.view.MenuItem;
import android.view.View;
import android.widget.Switch;
import android.widget.Toast;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.Gallery;
import com.alrbea.androidapp.ui.adapter.GalleryAdapter;
import com.alrbea.androidapp.utils.GalleryManager;

import java.util.ArrayList;
import java.util.List;

public class PhotoGallerySettingsActivity extends BaseActivity implements GalleryAdapter.OnGalleryActionListener {

    private static final String PREFS_NAME = "PhotoGallerySettings";
    private static final int REQUEST_ADD_EDIT_GALLERY = 1001;

    private Toolbar toolbar;
    private Switch switchEnableGallery;
    private CardView cardCreateGallery;
    private RecyclerView recyclerViewGalleries;
    private GalleryAdapter galleryAdapter;
    private GalleryManager galleryManager;
    private SharedPreferences sharedPreferences;

    private ActivityResultLauncher<Intent> addEditGalleryLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_photo_gallery_settings);

        galleryManager = new GalleryManager(this);

        initViews();
        setupToolbar();
        setupActivityLauncher();
        setupRecyclerView();
        setupClickListeners();
        loadGalleries();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        switchEnableGallery = findViewById(R.id.switch_enable_gallery);
        cardCreateGallery = findViewById(R.id.card_create_gallery);
        recyclerViewGalleries = findViewById(R.id.recycler_view_galleries);
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("إعدادات معرض الصور");
        }
    }
    
    private void setupActivityLauncher() {
        addEditGalleryLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == RESULT_OK) {
                    // Refresh galleries list
                    loadGalleries();
                }
            }
        );
    }
    
    private void setupRecyclerView() {
        galleryAdapter = new GalleryAdapter(new ArrayList<>(), this);
        recyclerViewGalleries.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewGalleries.setAdapter(galleryAdapter);
    }

    private void setupClickListeners() {
        // Gallery enable/disable switch
        switchEnableGallery.setOnCheckedChangeListener((buttonView, isChecked) -> {
            galleryManager.setGalleryFeatureEnabled(isChecked);
            updateGalleryListVisibility(isChecked);
        });

        // Create gallery card click
        cardCreateGallery.setOnClickListener(v -> openAddGalleryActivity());
    }

    private void loadGalleries() {
        // Load gallery feature enabled state
        boolean isEnabled = galleryManager.isGalleryFeatureEnabled();
        switchEnableGallery.setOnCheckedChangeListener(null);
        switchEnableGallery.setChecked(isEnabled);
        switchEnableGallery.setOnCheckedChangeListener((buttonView, isChecked) -> {
            galleryManager.setGalleryFeatureEnabled(isChecked);
            updateGalleryListVisibility(isChecked);
        });

        // Load galleries
        List<Gallery> galleries = galleryManager.getAllGalleries();
        galleryAdapter.updateGalleries(galleries);

        updateGalleryListVisibility(isEnabled);
    }

    private void updateGalleryListVisibility(boolean enabled) {
        int visibility = enabled ? View.VISIBLE : View.GONE;
        cardCreateGallery.setVisibility(visibility);
        recyclerViewGalleries.setVisibility(visibility);
    }

    private void openAddGalleryActivity() {
        Intent intent = new Intent(this, AddEditGalleryActivity.class);
        intent.putExtra(AddEditGalleryActivity.EXTRA_IS_EDIT_MODE, false);
        addEditGalleryLauncher.launch(intent);
    }

    private void openEditGalleryActivity(Gallery gallery) {
        Intent intent = new Intent(this, AddEditGalleryActivity.class);
        intent.putExtra(AddEditGalleryActivity.EXTRA_GALLERY_ID, gallery.getId());
        intent.putExtra(AddEditGalleryActivity.EXTRA_IS_EDIT_MODE, true);
        addEditGalleryLauncher.launch(intent);
    }

    // GalleryAdapter.OnGalleryActionListener implementation
    @Override
    public void onGalleryToggle(Gallery gallery, boolean enabled) {
        if (galleryManager.updateGalleryEnabledState(gallery.getId(), enabled)) {
            Toast.makeText(this, enabled ? "تم تفعيل المعرض" : "تم إلغاء تفعيل المعرض", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(this, "فشل في تحديث حالة المعرض", Toast.LENGTH_SHORT).show();
            // Refresh to revert the switch state
            loadGalleries();
        }
    }

    @Override
    public void onGalleryEdit(Gallery gallery) {
        openEditGalleryActivity(gallery);
    }

    @Override
    public void onGalleryDelete(Gallery gallery) {
        showDeleteConfirmation(gallery);
    }

    private void showDeleteConfirmation(Gallery gallery) {
        new AlertDialog.Builder(this)
            .setTitle("حذف المعرض")
            .setMessage("هل أنت متأكد من حذف معرض \"" + gallery.getName() + "\"؟")
            .setPositiveButton("حذف", (dialog, which) -> deleteGallery(gallery))
            .setNegativeButton("إلغاء", null)
            .show();
    }

    private void deleteGallery(Gallery gallery) {
        if (galleryManager.deleteGallery(gallery.getId())) {
            Toast.makeText(this, "تم حذف المعرض بنجاح", Toast.LENGTH_SHORT).show();
            loadGalleries(); // Refresh the list
        } else {
            Toast.makeText(this, "فشل في حذف المعرض", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    // Remove all old unused methods
    /*
    private int getColumnCount() {
        ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(this);
        
        switch (screenType) {
            case PHONE_PORTRAIT:
                return 1;
            case PHONE_LANDSCAPE:
                return 2;
            case TABLET_PORTRAIT:
                return 2;
            case TABLET_LANDSCAPE:
                return 3;
            case TV:
                return 4;
            default:
                return 1;
        }
    }
    
    private void loadPhotoGallerySettings() {
        List<PhotoGallerySetting> settings = createPhotoGallerySettings();
        
        adapter = new PhotoGallerySettingsAdapter(settings, this::onSettingChanged);
        recyclerViewSettings.setAdapter(adapter);
    }
    
    private List<PhotoGallerySetting> createPhotoGallerySettings() {
        List<PhotoGallerySetting> settings = new ArrayList<>();
        
        // 1. تفعيل المعرض
        PhotoGallerySetting enableGallery = new PhotoGallerySetting(
            "gallery_enable",
            "تفعيل معرض الصور",
            "Enable Photo Gallery",
            "تفعيل عرض الصور في الصفحة الرئيسية",
            "Enable photo display on main screen",
            R.drawable.ic_toggle,
            PhotoGallerySetting.SettingType.TOGGLE
        );
        enableGallery.setEnabled(sharedPreferences.getBoolean("gallery_enabled", false));
        settings.add(enableGallery);
        
        // 2. إضافة صور
        PhotoGallerySetting addPhotos = new PhotoGallerySetting(
            "add_photos",
            "إضافة صور",
            "Add Photos",
            "اختيار صور من الهاتف لعرضها",
            "Select photos from device to display",
            R.drawable.ic_add_photo,
            PhotoGallerySetting.SettingType.ACTION_BUTTON
        );
        settings.add(addPhotos);
        
        // 3. مدة عرض الصورة الواحدة
        PhotoGallerySetting photoDuration = new PhotoGallerySetting(
            "photo_duration",
            "مدة عرض الصورة",
            "Photo Display Duration",
            "المدة بالثواني لعرض كل صورة",
            "Duration in seconds for each photo",
            R.drawable.ic_timer,
            PhotoGallerySetting.SettingType.NUMBER_INPUT
        );
        photoDuration.setValue(sharedPreferences.getString("photo_duration", "10"));
        settings.add(photoDuration);
        
        // 4. وقت بداية العرض
        PhotoGallerySetting startTime = new PhotoGallerySetting(
            "start_time",
            "وقت بداية العرض",
            "Display Start Time",
            "الوقت الذي يبدأ فيه عرض الصور",
            "Time when photo display starts",
            R.drawable.ic_schedule,
            PhotoGallerySetting.SettingType.TIME_PICKER
        );
        startTime.setValue(sharedPreferences.getString("start_time", "06:00"));
        settings.add(startTime);
        
        // 5. وقت انتهاء العرض
        PhotoGallerySetting endTime = new PhotoGallerySetting(
            "end_time",
            "وقت انتهاء العرض",
            "Display End Time",
            "الوقت الذي ينتهي فيه عرض الصور",
            "Time when photo display ends",
            R.drawable.ic_schedule,
            PhotoGallerySetting.SettingType.TIME_PICKER
        );
        endTime.setValue(sharedPreferences.getString("end_time", "22:00"));
        settings.add(endTime);
        
        // 6. تأثيرات الانتقال
        PhotoGallerySetting transitions = new PhotoGallerySetting(
            "transitions",
            "تأثيرات الانتقال",
            "Transition Effects",
            "نوع التأثير عند الانتقال بين الصور",
            "Type of effect when transitioning between photos",
            R.drawable.ic_animation,
            PhotoGallerySetting.SettingType.SELECTION
        );
        transitions.setValue(sharedPreferences.getString("transitions", "fade"));
        settings.add(transitions);
        
        return settings;
    }
    
    private void onSettingChanged(PhotoGallerySetting setting, Object value) {
        switch (setting.getId()) {
            case "gallery_enable":
                boolean isEnabled = (Boolean) value;
                sharedPreferences.edit()
                        .putBoolean("gallery_enabled", isEnabled)
                        .apply();
                setting.setEnabled(isEnabled);
                showToast(isEnabled ? "تم تفعيل معرض الصور" : "تم إلغاء تفعيل معرض الصور");
                break;
                
            case "add_photos":
                openImagePicker();
                break;
                
            case "photo_duration":
                String duration = (String) value;
                sharedPreferences.edit()
                        .putString("photo_duration", duration)
                        .apply();
                setting.setValue(duration);
                showToast("تم حفظ مدة العرض: " + duration + " ثانية");
                break;
                
            case "start_time":
                String startTimeValue = (String) value;
                sharedPreferences.edit()
                        .putString("start_time", startTimeValue)
                        .apply();
                setting.setValue(startTimeValue);
                showToast("تم حفظ وقت البداية: " + startTimeValue);
                break;
                
            case "end_time":
                String endTimeValue = (String) value;
                sharedPreferences.edit()
                        .putString("end_time", endTimeValue)
                        .apply();
                setting.setValue(endTimeValue);
                showToast("تم حفظ وقت النهاية: " + endTimeValue);
                break;
                
            case "transitions":
                String transitionValue = (String) value;
                sharedPreferences.edit()
                        .putString("transitions", transitionValue)
                        .apply();
                setting.setValue(transitionValue);
                showToast("تم حفظ تأثير الانتقال: " + transitionValue);
                break;
        }
    }
    
    private void openImagePicker() {
        Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        intent.setType("image/*");
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
        imagePickerLauncher.launch(Intent.createChooser(intent, "اختر الصور"));
    }
    
    private void handleSelectedImage(Uri imageUri) {
        // TODO: Save selected image URI and add to gallery
        showToast("تم إضافة الصورة إلى المعرض");
    }
    
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    */
}
