package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.view.MenuItem;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;

import com.alrbea.androidapp.R;

public class AppSettingsActivity extends BaseActivity {

    private Toolbar toolbar;
    private CardView cardMainSettings;
    private CardView cardDesignOptions;
    private CardView cardNotifications;
    private CardView cardLocation;
    private CardView cardDeviceSettings;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_app_settings);

        initViews();
        setupToolbar();
        setupClickListeners();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        cardMainSettings = findViewById(R.id.card_main_settings);
        cardDesignOptions = findViewById(R.id.card_design_options);
        cardNotifications = findViewById(R.id.card_notifications);
        cardLocation = findViewById(R.id.card_location);
        cardDeviceSettings = findViewById(R.id.card_device_settings);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("إعدادات التطبيق");
        }
    }

    private void setupClickListeners() {
        cardMainSettings.setOnClickListener(v -> openDeviceSettings());
        cardDesignOptions.setOnClickListener(v -> openDesignOptionsActivity());
        cardNotifications.setOnClickListener(v -> openNotificationsActivity());
        cardLocation.setOnClickListener(v -> openLocationActivity());
        cardDeviceSettings.setOnClickListener(v -> openDeviceSettingsActivity());
    }

    private void openDeviceSettings() {
        try {
            Intent intent = new Intent(Settings.ACTION_SETTINGS);
            startActivity(intent);
        } catch (Exception e) {
            // Handle error
        }
    }

    private void openDesignOptionsActivity() {
        Intent intent = new Intent(this, DesignOptionsActivity.class);
        startActivity(intent);
    }

    private void openNotificationsActivity() {
        Intent intent = new Intent(this, NotificationSettingsActivity.class);
        startActivity(intent);
    }

    private void openLocationActivity() {
//        Intent intent = new Intent(this, CountryActivity.class);
//        startActivity(intent);
    }

    private void openDeviceSettingsActivity() {
        Intent intent = new Intent(this, DeviceSettingsActivity.class);
        startActivity(intent);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
