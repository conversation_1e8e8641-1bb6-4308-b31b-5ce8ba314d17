package com.alrbea.androidapp.ui;

import android.Manifest;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AlertDialog;
import androidx.core.content.ContextCompat;

import com.alrbea.androidapp.MainActivity;
import com.alrbea.androidapp.R;

public class SplashActivity extends BaseActivity {

    private static final int SPLASH_DURATION = 2000; // 2 seconds
    private static final String PREFS_NAME = "PrayerAppPrefs";
    private static final String KEY_FIRST_RUN = "first_run";
    private static final String KEY_PERMISSIONS_GRANTED = "permissions_granted";

    private SharedPreferences sharedPreferences;
    private boolean isFirstRun;

    // Permission launchers
    private ActivityResultLauncher<String> locationPermissionLauncher;
    private ActivityResultLauncher<Intent> overlayPermissionLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        // Initialize SharedPreferences
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        isFirstRun = sharedPreferences.getBoolean(KEY_FIRST_RUN, true);

        // Initialize permission launchers
        initializePermissionLaunchers();

        // Keep splash screen on screen longer
        // splashScreen.setKeepOnScreenCondition(() -> true);

        // Start the app flow
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (isFirstRun) {
                requestPermissions();
            } else {
                proceedToNextScreen();
            }
        }, SPLASH_DURATION);
    }

    private void initializePermissionLaunchers() {
        // Location permission launcher
        locationPermissionLauncher = registerForActivityResult(
                new ActivityResultContracts.RequestPermission(),
                isGranted -> {
                    if (isGranted) {
                        requestOverlayPermission();
                    } else {
                        showPermissionDeniedDialog();
                    }
                });

        // Overlay permission launcher
        overlayPermissionLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (Settings.canDrawOverlays(this)) {
                        onAllPermissionsGranted();
                    } else {
                        showOverlayPermissionDeniedDialog();
                    }
                });
    }

    private void requestPermissions() {
        // Check if location permission is already granted
        if (ContextCompat.checkSelfPermission(this,
                Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
            requestOverlayPermission();
        } else {
            showLocationPermissionDialog();
        }
    }

    private void showLocationPermissionDialog() {
        new AlertDialog.Builder(this)
                .setTitle("إذن الموقع مطلوب")
                .setMessage("يحتاج التطبيق إلى إذن الموقع لتحديد مواقيت الصلاة بدقة حسب موقعك الجغرافي")
                .setPositiveButton("موافق", (dialog, which) -> {
                    locationPermissionLauncher.launch(Manifest.permission.ACCESS_FINE_LOCATION);
                })
                .setNegativeButton("إلغاء", (dialog, which) -> {
                    showPermissionDeniedDialog();
                })
                .setCancelable(false)
                .show();
    }

    private void requestOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                showOverlayPermissionDialog();
            } else {
                onAllPermissionsGranted();
            }
        } else {
            onAllPermissionsGranted();
        }
    }

    private void showOverlayPermissionDialog() {
        new AlertDialog.Builder(this)
                .setTitle("إذن الظهور فوق التطبيقات")
                .setMessage("يحتاج التطبيق إلى إذن الظهور فوق التطبيقات الأخرى لعرض تنبيهات الصلاة")
                .setPositiveButton("موافق", (dialog, which) -> {
                    Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                            Uri.parse("package:" + getPackageName()));
                    overlayPermissionLauncher.launch(intent);
                })
                .setNegativeButton("تخطي", (dialog, which) -> {
                    onAllPermissionsGranted();
                })
                .setCancelable(false)
                .show();
    }

    private void showPermissionDeniedDialog() {
        new AlertDialog.Builder(this)
                .setTitle("الإذن مطلوب")
                .setMessage("بعض الميزات قد لا تعمل بشكل صحيح بدون الأذونات المطلوبة")
                .setPositiveButton("إعادة المحاولة", (dialog, which) -> {
                    requestPermissions();
                })
                .setNegativeButton("متابعة", (dialog, which) -> {
                    proceedToNextScreen();
                })
                .setCancelable(false)
                .show();
    }

    private void showOverlayPermissionDeniedDialog() {
        new AlertDialog.Builder(this)
                .setTitle("إذن الظهور فوق التطبيقات")
                .setMessage("لن تتمكن من رؤية تنبيهات الصلاة فوق التطبيقات الأخرى")
                .setPositiveButton("موافق", (dialog, which) -> {
                    onAllPermissionsGranted();
                })
                .setCancelable(false)
                .show();
    }

    private void onAllPermissionsGranted() {
        // Mark permissions as granted and first run as complete
        sharedPreferences.edit()
                .putBoolean(KEY_FIRST_RUN, false)
                .putBoolean(KEY_PERMISSIONS_GRANTED, true)
                .apply();

        proceedToNextScreen();
    }

    private void proceedToNextScreen() {
        // Check if user is logged in
        boolean isLoggedIn = sharedPreferences.getBoolean("is_logged_in", false);

        Intent intent;
        if (isLoggedIn) {
            // Check setup completion in order
            boolean isLocationSetupComplete = sharedPreferences.getBoolean("location_setup_complete", false);
            boolean isOrientationSet = sharedPreferences.getBoolean("orientation_set", false);

            if (!isLocationSetupComplete) {
                intent = new Intent(this, LocationSetupActivity.class);
            } else if (!isOrientationSet) {
                intent = new Intent(this, OrientationSetupActivity.class);
            } else {
                intent = new Intent(this, MainActivity.class);
            }
        } else {
            intent = new Intent(this, LoginActivity.class);
        }

        startActivity(intent);
        finish();

        // Apply transition animation
        overridePendingTransition(R.anim.fade_in, android.R.anim.fade_out);
    }
}
