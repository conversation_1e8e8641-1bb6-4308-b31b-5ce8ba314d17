package com.alrbea.androidapp.ui;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.PrayerAzkarSetting;
import com.alrbea.androidapp.ui.adapter.PostPrayerAzkarAdapter;

import java.util.ArrayList;
import java.util.List;

public class PostPrayerAzkarActivity extends BaseActivity {
    
    private Toolbar toolbar;
    private RecyclerView recyclerViewPrayers;
    private PostPrayerAzkarAdapter adapter;
    private SharedPreferences sharedPreferences;
    
    private List<PrayerAzkarSetting> prayerSettings;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_post_prayer_azkar);
        
        initializeViews();
        setupToolbar();
        setupSharedPreferences();
        setupRecyclerView();
        loadPrayerSettings();
    }
    
    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        recyclerViewPrayers = findViewById(R.id.recycler_view_prayers);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle(getString(R.string.post_prayer_azkar));
        }
    }
    
    private void setupSharedPreferences() {
        sharedPreferences = getSharedPreferences("post_prayer_azkar_prefs", MODE_PRIVATE);
    }
    
    private void setupRecyclerView() {
        recyclerViewPrayers.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewPrayers.setNestedScrollingEnabled(false);
    }
    
    private void loadPrayerSettings() {
        prayerSettings = createPrayerSettings();
        adapter = new PostPrayerAzkarAdapter(prayerSettings, this::onPrayerSettingChanged);
        recyclerViewPrayers.setAdapter(adapter);
    }
    
    private List<PrayerAzkarSetting> createPrayerSettings() {
        List<PrayerAzkarSetting> settings = new ArrayList<>();
        
        // Fajr Prayer
        PrayerAzkarSetting fajr = new PrayerAzkarSetting(
            "fajr",
            getString(R.string.fajr_prayer),
            R.drawable.ic_prayer_times,
            sharedPreferences.getBoolean("fajr_enabled", true),
            sharedPreferences.getInt("fajr_duration", 5)
        );
        settings.add(fajr);

        // Dhuhr Prayer
        PrayerAzkarSetting dhuhr = new PrayerAzkarSetting(
            "dhuhr",
            getString(R.string.dhuhr_prayer),
            R.drawable.ic_prayer_times,
            sharedPreferences.getBoolean("dhuhr_enabled", true),
            sharedPreferences.getInt("dhuhr_duration", 5)
        );
        settings.add(dhuhr);

        // Asr Prayer
        PrayerAzkarSetting asr = new PrayerAzkarSetting(
            "asr",
            getString(R.string.asr_prayer),
            R.drawable.ic_prayer_times,
            sharedPreferences.getBoolean("asr_enabled", true),
            sharedPreferences.getInt("asr_duration", 5)
        );
        settings.add(asr);

        // Maghrib Prayer
        PrayerAzkarSetting maghrib = new PrayerAzkarSetting(
            "maghrib",
            getString(R.string.maghrib_prayer),
            R.drawable.ic_prayer_times,
            sharedPreferences.getBoolean("maghrib_enabled", true),
            sharedPreferences.getInt("maghrib_duration", 5)
        );
        settings.add(maghrib);

        // Isha Prayer
        PrayerAzkarSetting isha = new PrayerAzkarSetting(
            "isha",
            getString(R.string.isha_prayer),
            R.drawable.ic_prayer_times,
            sharedPreferences.getBoolean("isha_enabled", true),
            sharedPreferences.getInt("isha_duration", 5)
        );
        settings.add(isha);

        // Friday Prayer
        PrayerAzkarSetting friday = new PrayerAzkarSetting(
            "friday",
            getString(R.string.friday_prayer),
            R.drawable.ic_prayer_times,
            sharedPreferences.getBoolean("friday_enabled", true),
            sharedPreferences.getInt("friday_duration", 10)
        );
        settings.add(friday);
        
        return settings;
    }
    
    private void onPrayerSettingChanged(PrayerAzkarSetting setting, String action, Object value) {
        switch (action) {
            case "enabled":
                boolean enabled = (Boolean) value;
                sharedPreferences.edit()
                    .putBoolean(setting.getId() + "_enabled", enabled)
                    .apply();
                setting.setEnabled(enabled);
                showToast(enabled ? 
                    "تم تفعيل أذكار " + setting.getName() : 
                    "تم إلغاء تفعيل أذكار " + setting.getName());
                break;
                
            case "duration":
                int duration = (Integer) value;
                sharedPreferences.edit()
                    .putInt(setting.getId() + "_duration", duration)
                    .apply();
                setting.setDuration(duration);
                showToast("تم تحديث مدة عرض أذكار " + setting.getName() + " إلى " + duration + " دقائق");
                break;
        }
    }
    
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
