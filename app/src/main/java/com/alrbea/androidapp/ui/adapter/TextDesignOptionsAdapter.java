package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.alrbea.androidapp.R;

import java.util.List;

public class TextDesignOptionsAdapter extends BaseAdapter {
    
    private Context context;
    private List<String> options;
    private LayoutInflater inflater;
    
    public TextDesignOptionsAdapter(Context context, List<String> options) {
        this.context = context;
        this.options = options;
        this.inflater = LayoutInflater.from(context);
    }
    
    @Override
    public int getCount() {
        return options.size();
    }
    
    @Override
    public Object getItem(int position) {
        return options.get(position);
    }
    
    @Override
    public long getItemId(int position) {
        return position;
    }
    
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        return createView(position, convertView, parent, false);
    }
    
    @Override
    public View getDropDownView(int position, View convertView, ViewGroup parent) {
        return createView(position, convertView, parent, true);
    }
    
    private View createView(int position, View convertView, ViewGroup parent, boolean isDropDown) {
        View view = convertView;
        if (view == null) {
            int layoutId = isDropDown ? R.layout.item_option_dropdown : R.layout.item_option_spinner;
            view = inflater.inflate(layoutId, parent, false);
        }
        
        TextView textView = view.findViewById(R.id.text_option);
        String option = options.get(position);
        textView.setText(option);
        
        return view;
    }
}
