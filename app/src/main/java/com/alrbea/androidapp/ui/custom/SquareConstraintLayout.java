package com.alrbea.androidapp.ui.custom;

import android.content.Context;
import android.util.AttributeSet;

import androidx.constraintlayout.widget.ConstraintLayout;

/**
 * Custom ConstraintLayout that maintains a square aspect ratio
 * The height will always equal the width
 */
public class SquareConstraintLayout extends ConstraintLayout {

    public SquareConstraintLayout(Context context) {
        super(context);
    }

    public SquareConstraintLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public SquareConstraintLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        // Get the width measurement
        int width = MeasureSpec.getSize(widthMeasureSpec);
        int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        
        // Create a square by setting height equal to width
        int squareSpec = MeasureSpec.makeMeasureSpec(width, widthMode);
        
        // Call super with width for both dimensions to create a square
        super.onMeasure(squareSpec, squareSpec);
    }
}
