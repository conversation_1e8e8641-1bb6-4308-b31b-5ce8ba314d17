package com.alrbea.androidapp.ui;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Toast;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.FuneralSetting;
import com.alrbea.androidapp.ui.adapter.FuneralSettingsAdapter;
import com.alrbea.androidapp.utils.ResponsiveItemDecoration;
import com.alrbea.androidapp.utils.ScreenUtils;

import java.util.ArrayList;
import java.util.List;

public class FuneralAnnouncementActivity extends BaseActivity {
    
    private static final String PREFS_NAME = "FuneralSettings";
    
    private RecyclerView recyclerViewSettings;
    private FuneralSettingsAdapter adapter;
    private Toolbar toolbar;
    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_funeral_announcement);
        
        initViews();
        setupToolbar();
        setupRecyclerView();
        loadFuneralSettings();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        recyclerViewSettings = findViewById(R.id.recycler_view_settings);
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("إعلانات الجنازة");
        }
    }
    
    private void setupRecyclerView() {
        int columns = getColumnCount();
        
        GridLayoutManager layoutManager = new GridLayoutManager(this, columns);
        recyclerViewSettings.setLayoutManager(layoutManager);
        
        ResponsiveItemDecoration itemDecoration = new ResponsiveItemDecoration(this, true);
        recyclerViewSettings.addItemDecoration(itemDecoration);
        
        int padding = ScreenUtils.getScreenPadding(this);
        recyclerViewSettings.setPadding(padding, padding, padding, padding);
    }
    
    private int getColumnCount() {
        ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(this);
        
        switch (screenType) {
            case PHONE_PORTRAIT:
                return 1;
            case PHONE_LANDSCAPE:
                return 2;
            case TABLET_PORTRAIT:
                return 2;
            case TABLET_LANDSCAPE:
                return 3;
            case TV:
                return 4;
            default:
                return 1;
        }
    }
    
    private void loadFuneralSettings() {
        List<FuneralSetting> settings = createFuneralSettings();
        
        adapter = new FuneralSettingsAdapter(settings, this::onSettingChanged);
        recyclerViewSettings.setAdapter(adapter);
    }
    
    private List<FuneralSetting> createFuneralSettings() {
        List<FuneralSetting> settings = new ArrayList<>();
        
        // 1. تفعيل إعلانات الجنازة
        FuneralSetting enableFuneral = new FuneralSetting(
            "funeral_enable",
            "تفعيل إعلانات الجنازة",
            "Enable Funeral Announcements",
            "تفعيل عرض إعلانات الصلاة على الميت",
            "Enable funeral prayer announcements display",
            R.drawable.ic_toggle,
            FuneralSetting.SettingType.TOGGLE
        );
        enableFuneral.setEnabled(sharedPreferences.getBoolean("funeral_enabled", false));
        settings.add(enableFuneral);
        
        // 2. نوع الجنازة - رجل
        FuneralSetting funeralMan = new FuneralSetting(
            "funeral_man",
            "الصلاة على رجل",
            "Prayer for Man",
            "نص إعلان الصلاة على الرجل",
            "Funeral prayer announcement text for man",
            R.drawable.ic_person,
            FuneralSetting.SettingType.TEXT_TEMPLATE
        );
        funeralMan.setValue(sharedPreferences.getString("funeral_man_text", 
            "يُعلن للإخوة المصلين أنه سيُصلى على الأخ المتوفى {} بعد صلاة {} اليوم"));
        settings.add(funeralMan);
        
        // 3. نوع الجنازة - امرأة
        FuneralSetting funeralWoman = new FuneralSetting(
            "funeral_woman",
            "الصلاة على امرأة",
            "Prayer for Woman",
            "نص إعلان الصلاة على المرأة",
            "Funeral prayer announcement text for woman",
            R.drawable.ic_person_female,
            FuneralSetting.SettingType.TEXT_TEMPLATE
        );
        funeralWoman.setValue(sharedPreferences.getString("funeral_woman_text", 
            "يُعلن للإخوة المصلين أنه سيُصلى على الأخت المتوفاة {} بعد صلاة {} اليوم"));
        settings.add(funeralWoman);
        
        // 4. نوع الجنازة - طفل ذكر
        FuneralSetting funeralBoy = new FuneralSetting(
            "funeral_boy",
            "الصلاة على طفل ذكر",
            "Prayer for Boy",
            "نص إعلان الصلاة على الطفل الذكر",
            "Funeral prayer announcement text for boy",
            R.drawable.ic_child,
            FuneralSetting.SettingType.TEXT_TEMPLATE
        );
        funeralBoy.setValue(sharedPreferences.getString("funeral_boy_text", 
            "يُعلن للإخوة المصلين أنه سيُصلى على الطفل المتوفى {} بعد صلاة {} اليوم"));
        settings.add(funeralBoy);
        
        // 5. نوع الجنازة - طفلة أنثى
        FuneralSetting funeralGirl = new FuneralSetting(
            "funeral_girl",
            "الصلاة على طفلة أنثى",
            "Prayer for Girl",
            "نص إعلان الصلاة على الطفلة الأنثى",
            "Funeral prayer announcement text for girl",
            R.drawable.ic_child_female,
            FuneralSetting.SettingType.TEXT_TEMPLATE
        );
        funeralGirl.setValue(sharedPreferences.getString("funeral_girl_text", 
            "يُعلن للإخوة المصلين أنه سيُصلى على الطفلة المتوفاة {} بعد صلاة {} اليوم"));
        settings.add(funeralGirl);
        
        // 6. وقت الصلاة المفضل
        FuneralSetting prayerTime = new FuneralSetting(
            "preferred_prayer_time",
            "وقت الصلاة المفضل",
            "Preferred Prayer Time",
            "الوقت المفضل للصلاة على الميت",
            "Preferred time for funeral prayer",
            R.drawable.ic_prayer_times,
            FuneralSetting.SettingType.PRAYER_SELECTOR
        );
        prayerTime.setValue(sharedPreferences.getString("preferred_prayer_time", "maghrib"));
        settings.add(prayerTime);
        
        // 7. مدة عرض الإعلان
        FuneralSetting displayDuration = new FuneralSetting(
            "display_duration",
            "مدة عرض الإعلان",
            "Display Duration",
            "المدة بالدقائق لعرض إعلان الجنازة",
            "Duration in minutes to display funeral announcement",
            R.drawable.ic_timer,
            FuneralSetting.SettingType.NUMBER_INPUT
        );
        displayDuration.setValue(sharedPreferences.getString("display_duration", "30"));
        settings.add(displayDuration);
        
        // 8. صوت التنبيه
        FuneralSetting notificationSound = new FuneralSetting(
            "notification_sound",
            "صوت التنبيه",
            "Notification Sound",
            "تفعيل صوت التنبيه عند عرض إعلان الجنازة",
            "Enable notification sound for funeral announcements",
            R.drawable.ic_notifications,
            FuneralSetting.SettingType.TOGGLE
        );
        notificationSound.setEnabled(sharedPreferences.getBoolean("notification_sound_enabled", true));
        settings.add(notificationSound);
        
        return settings;
    }
    
    private void onSettingChanged(FuneralSetting setting, Object value) {
        switch (setting.getId()) {
            case "funeral_enable":
                boolean isEnabled = (Boolean) value;
                sharedPreferences.edit()
                        .putBoolean("funeral_enabled", isEnabled)
                        .apply();
                setting.setEnabled(isEnabled);
                showToast(isEnabled ? "تم تفعيل إعلانات الجنازة" : "تم إلغاء تفعيل إعلانات الجنازة");
                break;
                
            case "funeral_man":
            case "funeral_woman":
            case "funeral_boy":
            case "funeral_girl":
                String textValue = (String) value;
                sharedPreferences.edit()
                        .putString(setting.getId() + "_text", textValue)
                        .apply();
                setting.setValue(textValue);
                showToast("تم حفظ نص الإعلان");
                break;
                
            case "preferred_prayer_time":
                String prayerValue = (String) value;
                sharedPreferences.edit()
                        .putString("preferred_prayer_time", prayerValue)
                        .apply();
                setting.setValue(prayerValue);
                showToast("تم حفظ وقت الصلاة المفضل");
                break;
                
            case "display_duration":
                String durationValue = (String) value;
                sharedPreferences.edit()
                        .putString("display_duration", durationValue)
                        .apply();
                setting.setValue(durationValue);
                showToast("تم حفظ مدة العرض: " + durationValue + " دقيقة");
                break;
                
            case "notification_sound":
                boolean soundEnabled = (Boolean) value;
                sharedPreferences.edit()
                        .putBoolean("notification_sound_enabled", soundEnabled)
                        .apply();
                setting.setEnabled(soundEnabled);
                showToast(soundEnabled ? "تم تفعيل صوت التنبيه" : "تم إلغاء صوت التنبيه");
                break;
        }
    }
    
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
