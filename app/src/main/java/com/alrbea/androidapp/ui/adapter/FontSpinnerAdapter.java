package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.utils.FontManager;

import java.util.List;

public class FontSpinnerAdapter extends BaseAdapter {
    
    private Context context;
    private List<String> fonts;
    private FontManager fontManager;
    private LayoutInflater inflater;
    
    public FontSpinnerAdapter(Context context, List<String> fonts) {
        this.context = context;
        this.fonts = fonts;
        this.fontManager = new FontManager(context);
        this.inflater = LayoutInflater.from(context);
    }
    
    @Override
    public int getCount() {
        return fonts.size();
    }
    
    @Override
    public Object getItem(int position) {
        return fonts.get(position);
    }
    
    @Override
    public long getItemId(int position) {
        return position;
    }
    
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        return createView(position, convertView, parent, false);
    }
    
    @Override
    public View getDropDownView(int position, View convertView, ViewGroup parent) {
        return createView(position, convertView, parent, true);
    }
    
    private View createView(int position, View convertView, ViewGroup parent, boolean isDropDown) {
        View view = convertView;
        if (view == null) {
            int layoutId = isDropDown ? R.layout.item_font_dropdown : R.layout.item_font_spinner;
            view = inflater.inflate(layoutId, parent, false);
        }
        
        TextView textView = view.findViewById(R.id.text_font_name);
        String fontFamily = fonts.get(position);
        
        // Set font display name
        String displayName = fontManager.getFontDisplayName(fontFamily);
        textView.setText(displayName);
        
        // Apply the actual font to show preview
        if (isDropDown) {
            try {
                Typeface typeface = fontManager.getTypeface(fontFamily);
                textView.setTypeface(typeface);
            } catch (Exception e) {
                // Use default font if custom font fails
                textView.setTypeface(Typeface.DEFAULT);
            }
        }
        
        return view;
    }
}
