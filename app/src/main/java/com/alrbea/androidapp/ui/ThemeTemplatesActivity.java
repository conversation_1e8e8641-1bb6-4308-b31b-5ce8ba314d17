package com.alrbea.androidapp.ui;

import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.ThemeTemplate;
import com.alrbea.androidapp.ui.adapter.ThemeTemplateAdapter;
import com.alrbea.androidapp.ui.viewmodel.ThemeViewModel;

import java.util.ArrayList;
import java.util.List;

public class ThemeTemplatesActivity extends BaseActivity implements ThemeTemplateAdapter.OnThemeActionListener {

    private Toolbar toolbar;
    private RecyclerView recyclerThemes;
    private ThemeTemplateAdapter themeAdapter;
    private ThemeViewModel themeViewModel;
    private List<ThemeTemplate> themesList = new ArrayList<>();
    private String currentOrientation = "portrait";
    private int selectedPosition = -1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_theme_templates);

        initializeViews();
        initializeViewModel();
        setupRecyclerView();
        observeViewModel();
    }

    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("قوالب الثيمات");
        }
        recyclerThemes = findViewById(R.id.recycler_themes);
    }

    private void initializeViewModel() {
        themeViewModel = new ViewModelProvider(this).get(ThemeViewModel.class);
    }

    private void observeViewModel() {
        // Observe available themes
        themeViewModel.getAvailableThemes().observe(this, themes -> {
            if (themes != null) {
                themesList.clear();
                themesList.addAll(themes);
                if (themeAdapter != null) {
                    themeAdapter.notifyDataSetChanged();
                }
            }
        });

        // Observe current theme to update selection
        themeViewModel.getCurrentTheme().observe(this, currentTheme -> {
            if (currentTheme != null && themeAdapter != null) {
                // Find the position of current theme and update selection
                for (int i = 0; i < themesList.size(); i++) {
                    if (themesList.get(i).getId().equals(currentTheme.getId())) {
                        selectedPosition = i;
                        themeAdapter.setSelectedPosition(i);
                        break;
                    }
                }
            }
        });

        // Observe loading state
        themeViewModel.getLoadingState().observe(this, isLoading -> {
            // Handle loading state if needed (e.g., show/hide progress bar)
        });

        // Observe error messages
        themeViewModel.getErrorMessage().observe(this, errorMessage -> {
            if (errorMessage != null && !errorMessage.isEmpty()) {
                Toast.makeText(this, errorMessage, Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void setupRecyclerView() {
        themeAdapter = new ThemeTemplateAdapter(themesList, new ThemeTemplateAdapter.OnThemeActionListener() {
            @Override
            public void onThemeSelected(ThemeTemplate theme, int position) {
                selectedPosition = position;
                themeAdapter.setSelectedPosition(position);
                // Use ViewModel to select theme
                themeViewModel.selectTheme(theme);
            }
            @Override
            public void onThemeDownload(ThemeTemplate theme, int position) {
                // Handle theme download if needed
            }
        }, currentOrientation);
        themeAdapter.setSelectedPosition(selectedPosition);
        recyclerThemes.setLayoutManager(new GridLayoutManager(this, 2));
        recyclerThemes.setAdapter(themeAdapter);
    }

    @Override
    public void onThemeSelected(ThemeTemplate theme, int position) {
        // No action needed for selection
    }

    @Override
    public void onThemeDownload(ThemeTemplate theme, int position) {
        // No action needed for download
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
