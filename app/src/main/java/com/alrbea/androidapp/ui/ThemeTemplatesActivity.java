package com.alrbea.androidapp.ui;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.ThemeTemplate;
import com.alrbea.androidapp.ui.adapter.ThemeTemplateAdapter;
import com.google.android.material.button.MaterialButtonToggleGroup;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.ArrayList;
import java.util.List;

public class ThemeTemplatesActivity extends BaseActivity implements ThemeTemplateAdapter.OnThemeActionListener {

    private Toolbar toolbar;
    private RecyclerView recyclerThemes;
    private ThemeTemplateAdapter themeAdapter;
    private List<ThemeTemplate> themesList = new ArrayList<>();
    private String currentOrientation = "portrait";
    private int selectedPosition = -1;
    private SharedPreferences sharedPreferences;
    private static final String PREFS_NAME = "ThemeTemplateSettings";
    private static final String KEY_SELECTED_THEME = "selected_theme";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_theme_templates);
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("قوالب الثيمات");
        }
        recyclerThemes = findViewById(R.id.recycler_themes);
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        String savedThemeId = sharedPreferences.getString(KEY_SELECTED_THEME, null);
        loadThemes();
        // Find saved selection
        selectedPosition = -1;
        if (savedThemeId != null) {
            for (int i = 0; i < themesList.size(); i++) {
                if (themesList.get(i).getId().equals(savedThemeId)) {
                    selectedPosition = i;
                    break;
                }
            }
        }
        setupRecyclerView();
    }

    private void loadThemes() {
        themesList.clear();
        loadLocalThemes();
        loadApiThemes();
    }

    private void loadLocalThemes() {
        // Local theme 1
        ThemeTemplate theme1 = new ThemeTemplate("local_1", "ثيم الطبيعة");
        theme1.setLandscapeImage(R.drawable.l_theme_1);
        theme1.setPortraitImage(R.drawable.p_theme_1);
        theme1.setLandscapeView(R.layout.activity_theme_templates);
        theme1.setPortraitView(R.layout.activity_theme_templates);
        theme1.setSupportsIqama(true);
        theme1.setType(ThemeTemplate.ThemeType.LOCAL);
        theme1.setDownloaded(true);
        themesList.add(theme1);

        // Local theme 2
        ThemeTemplate theme2 = new ThemeTemplate("local_2", "ثيم كلاسيكي");
        theme2.setLandscapeImage(R.drawable.l_theme_2);
        theme2.setPortraitImage(R.drawable.p_theme_2);
        theme2.setLandscapeView(R.layout.activity_theme_templates);
        theme2.setPortraitView(R.layout.activity_theme_templates);
        theme2.setSupportsIqama(true);
        theme2.setType(ThemeTemplate.ThemeType.LOCAL);
        theme2.setDownloaded(true);
        themesList.add(theme2);


        // Local theme 2
        ThemeTemplate theme3 = new ThemeTemplate("local_3", "ثيم كلاسيكي");
        theme3.setLandscapeImage(R.drawable.l_theme_3);
        theme3.setPortraitImage(R.drawable.p_theme_3);
        theme2.setLandscapeView(R.layout.activity_theme_templates);
        theme2.setPortraitView(R.layout.activity_theme_templates);
        theme3.setSupportsIqama(true);
        theme3.setType(ThemeTemplate.ThemeType.LOCAL);
        theme3.setDownloaded(true);
        themesList.add(theme3);


        // Local theme 2
        ThemeTemplate theme4 = new ThemeTemplate("local_4", "ثيم كلاسيكي");
        theme4.setLandscapeImage(R.drawable.l_theme_4);
        theme4.setPortraitImage(R.drawable.p_theme_4);
        theme4.setLandscapeView(R.layout.activity_theme_templates);
        theme4.setPortraitView(R.layout.activity_theme_templates);
        theme4.setSupportsIqama(true);
        theme4.setType(ThemeTemplate.ThemeType.LOCAL);
        theme4.setDownloaded(true);
        themesList.add(theme4);


        // Local theme 2
        ThemeTemplate theme5 = new ThemeTemplate("local_5", "ثيم كلاسيكي");
        theme5.setLandscapeImage(R.drawable.l_theme_5);
        theme5.setPortraitImage(R.drawable.p_theme_5);
        theme5.setLandscapeView(R.layout.activity_theme_templates);
        theme5.setPortraitView(R.layout.activity_theme_templates);
        theme5.setSupportsIqama(true);
        theme5.setType(ThemeTemplate.ThemeType.LOCAL);
        theme5.setDownloaded(true);
        themesList.add(theme5);



        // Local theme 2
        ThemeTemplate theme6 = new ThemeTemplate("local_6", "ثيم كلاسيكي");
        theme6.setLandscapeImage(R.drawable.l_theme_6);
        theme6.setPortraitImage(R.drawable.p_theme_6);
        theme5.setLandscapeView(R.layout.activity_theme_templates);
        theme5.setPortraitView(R.layout.activity_theme_templates);
        theme6.setSupportsIqama(true);
        theme6.setType(ThemeTemplate.ThemeType.LOCAL);
        theme6.setDownloaded(true);
        themesList.add(theme6);

    }

    private void loadApiThemes() {
        // API theme 1
//        ThemeTemplate apiTheme1 = new ThemeTemplate("api_1", "ثيم ذهبي", "تصميم ذهبي فاخر");
//        apiTheme1.setPreviewImage(R.drawable.ic_template);
//        apiTheme1.setSupportsIqama(true);
//        apiTheme1.setType(ThemeTemplate.ThemeType.API);
//        apiTheme1.setDownloaded(false);
//        themesList.add(apiTheme1);
//
//        // API theme 2 - Custom
//        ThemeTemplate customTheme = new ThemeTemplate("custom_1", "ثيم مخصص", "تصميم مخصص للمستخدم");
//        customTheme.setPreviewImage(R.drawable.ic_template);
//        customTheme.setSupportsIqama(false);
//        customTheme.setType(ThemeTemplate.ThemeType.CUSTOM);
//        customTheme.setDownloaded(false);
//        themesList.add(customTheme);
    }

    private void setupRecyclerView() {
        themeAdapter = new ThemeTemplateAdapter(themesList, new ThemeTemplateAdapter.OnThemeActionListener() {
            @Override
            public void onThemeSelected(ThemeTemplate theme, int position) {
                selectedPosition = position;
                themeAdapter.setSelectedPosition(position);
                // Save selected theme ID locally
                sharedPreferences.edit().putString(KEY_SELECTED_THEME, theme.getId()).apply();
            }
            @Override
            public void onThemeDownload(ThemeTemplate theme, int position) {}
        }, currentOrientation);
        themeAdapter.setSelectedPosition(selectedPosition);
        recyclerThemes.setLayoutManager(new GridLayoutManager(this, 2));
        recyclerThemes.setAdapter(themeAdapter);
    }

    @Override
    public void onThemeSelected(ThemeTemplate theme, int position) {
        // No action needed for selection
    }

    @Override
    public void onThemeDownload(ThemeTemplate theme, int position) {
        // No action needed for download
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }


}
