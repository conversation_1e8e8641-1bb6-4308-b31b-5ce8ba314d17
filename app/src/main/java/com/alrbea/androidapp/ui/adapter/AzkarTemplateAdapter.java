package com.alrbea.androidapp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.ui.AzkarTemplatesActivity;

import pl.droidsonroids.gif.GifImageView;

import java.util.List;

public class AzkarTemplateAdapter extends RecyclerView.Adapter<AzkarTemplateAdapter.TemplateViewHolder> {

    public interface OnTemplateActionListener {
        void onTemplateSelected(AzkarTemplatesActivity.AzkarTemplate template, int position);
        void onTemplateDownload(AzkarTemplatesActivity.AzkarTemplate template, int position);
    }

    private List<AzkarTemplatesActivity.AzkarTemplate> templates;
    private OnTemplateActionListener listener;
    private Context context;
    private int selectedPosition = 0;

    public AzkarTemplateAdapter(List<AzkarTemplatesActivity.AzkarTemplate> templates, OnTemplateActionListener listener) {
        this.templates = templates;
        this.listener = listener;
    }

    @NonNull
    @Override
    public TemplateViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        context = parent.getContext();
        View view = LayoutInflater.from(context).inflate(R.layout.item_theme_slider, parent, false);
        return new TemplateViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TemplateViewHolder holder, int position) {
        AzkarTemplatesActivity.AzkarTemplate template = templates.get(position);
        holder.bind(template, position);
    }

    @Override
    public int getItemCount() {
        return templates.size();
    }

    public void setSelectedPosition(int position) {
        int previousSelected = selectedPosition;
        selectedPosition = position;
        
        if (previousSelected != -1) {
            notifyItemChanged(previousSelected);
        }
        if (position != -1) {
            notifyItemChanged(position);
        }
    }

    class TemplateViewHolder extends RecyclerView.ViewHolder {
        private CardView cardTemplate;
        private GifImageView gifTemplateThumbnail;
        private ProgressBar progressTemplateLoading;
        private LinearLayout layoutTemplateDownloadStatus;
        private ImageView iconTemplateDownload;
        private TextView textTemplateDownload;
        private TextView textTemplateTitle;
        private TextView textTemplateType;
        private ImageView iconTemplateStatusSmall;
        private View viewSelectionIndicator;

        public TemplateViewHolder(@NonNull View itemView) {
            super(itemView);
            cardTemplate = itemView.findViewById(R.id.card_template);
            gifTemplateThumbnail = itemView.findViewById(R.id.gif_template_thumbnail);
            progressTemplateLoading = itemView.findViewById(R.id.progress_template_loading);
            layoutTemplateDownloadStatus = itemView.findViewById(R.id.layout_template_download_status);
            iconTemplateDownload = itemView.findViewById(R.id.icon_template_download);
            textTemplateDownload = itemView.findViewById(R.id.text_template_download);
            textTemplateTitle = itemView.findViewById(R.id.text_template_title);
            textTemplateType = itemView.findViewById(R.id.text_template_type);
            iconTemplateStatusSmall = itemView.findViewById(R.id.icon_template_status_small);
            viewSelectionIndicator = itemView.findViewById(R.id.view_selection_indicator);
        }

        public void bind(AzkarTemplatesActivity.AzkarTemplate template, int position) {
            textTemplateTitle.setText(template.getName());

            // Set template type
            if (template.getCategory() != null) {
                switch (template.getCategory()) {
                    case LOCAL:
                        textTemplateType.setText("محلي");
                        break;
                    case API:
                        textTemplateType.setText("من الخادم");
                        break;
                    case CUSTOM:
                        textTemplateType.setText("مخصص");
                        break;
                }
            }
            
            // Show selection indicator
            if (position == selectedPosition) {
                viewSelectionIndicator.setVisibility(View.VISIBLE);
                cardTemplate.setCardElevation(context.getResources().getDimension(R.dimen._8sdp));
            } else {
                viewSelectionIndicator.setVisibility(View.GONE);
                cardTemplate.setCardElevation(context.getResources().getDimension(R.dimen._4sdp));
            }
            
            if (template.isDownloaded()) {
                // Template is downloaded
                layoutTemplateDownloadStatus.setVisibility(View.GONE);
                iconTemplateStatusSmall.setVisibility(View.VISIBLE);
                iconTemplateStatusSmall.setImageResource(R.drawable.ic_check);
                
                // Load template thumbnail
//                if (template.getLocalResourceId() != null) {
                    gifTemplateThumbnail.setImageResource(R.drawable.second_land);
//                } else {
//                    gifTemplateThumbnail.setImageResource(R.drawable.ic_template);
//                }
                
            } else {
                // Template needs to be downloaded
                layoutTemplateDownloadStatus.setVisibility(View.VISIBLE);
                iconTemplateStatusSmall.setVisibility(View.GONE);
                
                // Show placeholder or preview image
                gifTemplateThumbnail.setImageResource(R.drawable.ic_template);
                
                // Setup download overlay
                iconTemplateDownload.setImageResource(R.drawable.ic_download);
                textTemplateDownload.setText("تحميل");
            }
            
            // Click listeners
            cardTemplate.setOnClickListener(v -> {
                setSelectedPosition(position);
                if (listener != null) {
                    listener.onTemplateSelected(template, position);
                }
            });
            
            layoutTemplateDownloadStatus.setOnClickListener(v -> {
                if (!template.isDownloaded() && listener != null) {
                    listener.onTemplateDownload(template, position);
                }
            });
        }
    }
}
