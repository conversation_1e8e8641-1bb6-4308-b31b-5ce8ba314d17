package com.alrbea.androidapp.ui;

import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.Toolbar;

import com.alrbea.androidapp.R;

public class AboutActivity extends BaseActivity {
    
    private Toolbar toolbar;
    private TextView textAppVersion;
    private Button btnEmail;
    private Button btnWebsite;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_about);
        
        initViews();
        setupToolbar();
        setupClickListeners();
        loadAppInfo();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        btnEmail = findViewById(R.id.btn_email);
        btnWebsite = findViewById(R.id.btn_website);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("حول التطبيق");
        }
    }
    
    private void setupClickListeners() {
        btnEmail.setOnClickListener(v -> openEmail());
        btnWebsite.setOnClickListener(v -> openWebsite());
    }
    
    private void loadAppInfo() {
        try {
            PackageInfo packageInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
            String versionName = packageInfo.versionName;
            
            // Find and update version text if it exists in layout
            TextView versionText = findViewById(R.id.text_app_version);
            if (versionText != null) {
                versionText.setText("الإصدار " + versionName);
            }
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
    }
    
    private void openEmail() {
        try {
            Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
            emailIntent.setData(Uri.parse("mailto:"));
            emailIntent.putExtra(Intent.EXTRA_EMAIL, new String[]{"<EMAIL>"});
            emailIntent.putExtra(Intent.EXTRA_SUBJECT, "استفسار حول تطبيق الصلاة");
            emailIntent.putExtra(Intent.EXTRA_TEXT, "السلام عليكم ورحمة الله وبركاته\n\nأود الاستفسار عن:\n\n");
            
            if (emailIntent.resolveActivity(getPackageManager()) != null) {
                startActivity(Intent.createChooser(emailIntent, "إرسال بريد إلكتروني"));
            } else {
                Toast.makeText(this, "لا يوجد تطبيق بريد إلكتروني مثبت", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Toast.makeText(this, "حدث خطأ في فتح البريد الإلكتروني", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void openWebsite() {
        try {
            String websiteUrl = "https://www.alrbea.com/public";
            Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(websiteUrl));
            
            if (browserIntent.resolveActivity(getPackageManager()) != null) {
                startActivity(browserIntent);
            } else {
                Toast.makeText(this, "لا يوجد متصفح مثبت", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Toast.makeText(this, "حدث خطأ في فتح الموقع الإلكتروني", Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
