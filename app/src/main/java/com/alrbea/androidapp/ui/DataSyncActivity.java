package com.alrbea.androidapp.ui;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.BackupData;
import com.alrbea.androidapp.data.model.SettingsCategory;
import com.alrbea.androidapp.ui.adapter.BackupAdapter;
import com.alrbea.androidapp.ui.adapter.SyncCategoryAdapter;
import com.alrbea.androidapp.utils.DataSyncManager;
import com.google.android.material.textfield.TextInputEditText;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class DataSyncActivity extends BaseActivity implements 
        BackupAdapter.OnBackupActionListener,
        SyncCategoryAdapter.OnCategoryToggleListener,
        DataSyncManager.SyncCallback,
        DataSyncManager.BackupListCallback {

    private static final String PREFS_NAME = "DataSyncSettings";
    private static final String KEY_LAST_SYNC = "last_sync_time";
    private static final String KEY_CLIENT_ID = "client_id";

    private Toolbar toolbar;
    private ImageView iconSyncStatus;
    private TextView textSyncStatus;
    private TextView textLastSync;
    private ProgressBar progressSync;

    // Upload components
    private TextInputEditText editClientId;
    private TextInputEditText editBackupName;
    private Button btnUploadSettings;

    // Download components
    private TextInputEditText editDownloadClientId;
    private Button btnLoadBackups;
    private RecyclerView recyclerBackups;

    // Categories
    private RecyclerView recyclerSettingsCategories;

    // Adapters
    private BackupAdapter backupAdapter;
    private SyncCategoryAdapter categoryAdapter;

    // Data
    private DataSyncManager syncManager;
    private List<BackupData> backupsList = new ArrayList<>();
    private List<SettingsCategory> categoriesList = new ArrayList<>();
    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_data_sync);

        initViews();
        setupToolbar();
        setupRecyclerViews();
        setupClickListeners();
        loadData();
        updateSyncStatus();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        iconSyncStatus = findViewById(R.id.icon_sync_status);
        textSyncStatus = findViewById(R.id.text_sync_status);
        textLastSync = findViewById(R.id.text_last_sync);
        progressSync = findViewById(R.id.progress_sync);

        editClientId = findViewById(R.id.edit_client_id);
        editBackupName = findViewById(R.id.edit_backup_name);
        btnUploadSettings = findViewById(R.id.btn_upload_settings);

        editDownloadClientId = findViewById(R.id.edit_download_client_id);
        btnLoadBackups = findViewById(R.id.btn_load_backups);
        recyclerBackups = findViewById(R.id.recycler_backups);

        recyclerSettingsCategories = findViewById(R.id.recycler_settings_categories);

        syncManager = new DataSyncManager(this);
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("مزامنة البيانات");
        }
    }

    private void setupRecyclerViews() {
        // Setup backups recycler view
        recyclerBackups.setLayoutManager(new LinearLayoutManager(this));
        backupAdapter = new BackupAdapter(backupsList, this);
        recyclerBackups.setAdapter(backupAdapter);

        // Setup categories recycler view
        recyclerSettingsCategories.setLayoutManager(new LinearLayoutManager(this));
        categoryAdapter = new SyncCategoryAdapter(categoriesList, this);
        recyclerSettingsCategories.setAdapter(categoryAdapter);
    }

    private void setupClickListeners() {
        btnUploadSettings.setOnClickListener(v -> uploadSettings());
        btnLoadBackups.setOnClickListener(v -> loadBackupsList());
    }

    private void loadData() {
        // Load settings categories
        categoriesList = syncManager.getSettingsCategories();
        categoryAdapter.updateCategories(categoriesList);

        // Load saved client ID
        String savedClientId = sharedPreferences.getString(KEY_CLIENT_ID, "");
        editClientId.setText(savedClientId);
        editDownloadClientId.setText(savedClientId);

        // Generate default backup name
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm", Locale.getDefault());
        String defaultName = "backup_" + dateFormat.format(new Date());
        editBackupName.setText(defaultName);
    }

    private void updateSyncStatus() {
        long lastSyncTime = sharedPreferences.getLong(KEY_LAST_SYNC, 0);
        
        if (lastSyncTime == 0) {
            textSyncStatus.setText("لم تتم المزامنة");
            textLastSync.setText("لم تتم المزامنة");
            iconSyncStatus.setColorFilter(getResources().getColor(R.color.text_secondary));
        } else {
            textSyncStatus.setText("تمت المزامنة");
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
            textLastSync.setText("آخر مزامنة: " + dateFormat.format(new Date(lastSyncTime)));
            iconSyncStatus.setColorFilter(getResources().getColor(R.color.primary));
        }
    }

    private void uploadSettings() {
        String clientId = editClientId.getText().toString().trim();
        String backupName = editBackupName.getText().toString().trim();

        if (TextUtils.isEmpty(clientId)) {
            editClientId.setError("يرجى إدخال معرف العميل");
            return;
        }

        if (TextUtils.isEmpty(backupName)) {
            editBackupName.setError("يرجى إدخال اسم النسخة الاحتياطية");
            return;
        }

        // Save client ID
        sharedPreferences.edit().putString(KEY_CLIENT_ID, clientId).apply();

        // Get selected categories
        List<SettingsCategory> selectedCategories = new ArrayList<>();
        for (SettingsCategory category : categoriesList) {
            if (category.isEnabled()) {
                selectedCategories.add(category);
            }
        }

        if (selectedCategories.isEmpty()) {
            Toast.makeText(this, "يرجى اختيار فئة واحدة على الأقل", Toast.LENGTH_SHORT).show();
            return;
        }

        // Create backup
        BackupData backup = syncManager.createBackup(clientId, backupName, selectedCategories);

        // Show confirmation dialog
        showUploadConfirmationDialog(backup);
    }

    private void showUploadConfirmationDialog(BackupData backup) {
        String message = "سيتم رفع النسخة الاحتياطية التالية:\n\n" +
                "الاسم: " + backup.getName() + "\n" +
                "الحجم: " + backup.getFormattedSize() + "\n" +
                "عدد الفئات: " + backup.getCategoryCount() + "\n\n" +
                "هل تريد المتابعة؟";

        new AlertDialog.Builder(this)
                .setTitle("تأكيد الرفع")
                .setMessage(message)
                .setPositiveButton("رفع", (dialog, which) -> {
                    performUpload(backup);
                })
                .setNegativeButton("إلغاء", null)
                .show();
    }

    private void performUpload(BackupData backup) {
        btnUploadSettings.setEnabled(false);
        btnUploadSettings.setText("جاري الرفع...");
        progressSync.setVisibility(View.VISIBLE);

        syncManager.uploadBackup(backup, this);
    }

    private void loadBackupsList() {
        String clientId = editDownloadClientId.getText().toString().trim();

        if (TextUtils.isEmpty(clientId)) {
            editDownloadClientId.setError("يرجى إدخال معرف العميل");
            return;
        }

        btnLoadBackups.setEnabled(false);
        btnLoadBackups.setText("جاري التحميل...");

        syncManager.downloadBackupsList(clientId, this);
    }

    // BackupAdapter.OnBackupActionListener implementation
    @Override
    public void onRestoreBackup(BackupData backup) {
        showRestoreConfirmationDialog(backup);
    }

    @Override
    public void onPreviewBackup(BackupData backup) {
        showBackupPreviewDialog(backup);
    }

    @Override
    public void onDeleteBackup(BackupData backup) {
        showDeleteConfirmationDialog(backup);
    }

    private void showRestoreConfirmationDialog(BackupData backup) {
        String message = "سيتم استرجاع الإعدادات من النسخة الاحتياطية:\n\n" +
                backup.getName() + "\n\n" +
                "تحذير: سيتم استبدال الإعدادات الحالية!\n\n" +
                "هل تريد المتابعة؟";

        new AlertDialog.Builder(this)
                .setTitle("تأكيد الاسترجاع")
                .setMessage(message)
                .setPositiveButton("استرجاع", (dialog, which) -> {
                    performRestore(backup);
                })
                .setNegativeButton("إلغاء", null)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show();
    }

    private void performRestore(BackupData backup) {
        progressSync.setVisibility(View.VISIBLE);
        textSyncStatus.setText("جاري الاسترجاع...");

        syncManager.restoreBackup(backup, this);
    }

    private void showBackupPreviewDialog(BackupData backup) {
        StringBuilder content = new StringBuilder();
        content.append("اسم النسخة: ").append(backup.getName()).append("\n");
        content.append("التاريخ: ").append(new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(backup.getCreatedAt())).append("\n");
        content.append("الحجم: ").append(backup.getFormattedSize()).append("\n");
        content.append("الإصدار: ").append(backup.getVersion()).append("\n\n");
        content.append("الفئات المتضمنة:\n");
        
        if (backup.getCategories() != null) {
            for (String categoryId : backup.getCategories()) {
                SettingsCategory category = findCategoryById(categoryId);
                if (category != null) {
                    content.append("• ").append(category.getName()).append("\n");
                }
            }
        }

        new AlertDialog.Builder(this)
                .setTitle("معاينة النسخة الاحتياطية")
                .setMessage(content.toString())
                .setPositiveButton("موافق", null)
                .show();
    }

    private void showDeleteConfirmationDialog(BackupData backup) {
        new AlertDialog.Builder(this)
                .setTitle("حذف النسخة الاحتياطية")
                .setMessage("هل تريد حذف النسخة الاحتياطية: " + backup.getName() + "؟")
                .setPositiveButton("حذف", (dialog, which) -> {
                    syncManager.deleteBackup(backup.getId(), new DataSyncManager.SyncCallback() {
                        @Override
                        public void onSuccess(String message) {
                            runOnUiThread(() -> {
                                Toast.makeText(DataSyncActivity.this, message, Toast.LENGTH_SHORT).show();
                                backupsList.remove(backup);
                                backupAdapter.notifyDataSetChanged();
                            });
                        }

                        @Override
                        public void onError(String error) {
                            runOnUiThread(() -> {
                                Toast.makeText(DataSyncActivity.this, error, Toast.LENGTH_SHORT).show();
                            });
                        }

                        @Override
                        public void onProgress(int progress) {
                            // Not needed for delete
                        }
                    });
                })
                .setNegativeButton("إلغاء", null)
                .show();
    }

    private SettingsCategory findCategoryById(String categoryId) {
        for (SettingsCategory category : categoriesList) {
            if (category.getId().equals(categoryId)) {
                return category;
            }
        }
        return null;
    }

    // SyncCategoryAdapter.OnCategoryToggleListener implementation
    @Override
    public void onCategoryToggled(SettingsCategory category, boolean enabled) {
        // Category state is already updated in the adapter
        // You can add additional logic here if needed
    }

    // DataSyncManager.SyncCallback implementation
    @Override
    public void onSuccess(String message) {
        runOnUiThread(() -> {
            progressSync.setVisibility(View.GONE);
            btnUploadSettings.setEnabled(true);
            btnUploadSettings.setText("رفع جميع الإعدادات");
            textSyncStatus.setText("تمت المزامنة بنجاح");
            
            // Update last sync time
            sharedPreferences.edit().putLong(KEY_LAST_SYNC, System.currentTimeMillis()).apply();
            updateSyncStatus();
            
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
        });
    }

    @Override
    public void onError(String error) {
        runOnUiThread(() -> {
            progressSync.setVisibility(View.GONE);
            btnUploadSettings.setEnabled(true);
            btnUploadSettings.setText("رفع جميع الإعدادات");
            btnLoadBackups.setEnabled(true);
            btnLoadBackups.setText("تحميل قائمة النسخ الاحتياطية");
            textSyncStatus.setText("حدث خطأ في المزامنة");
            
            Toast.makeText(this, error, Toast.LENGTH_LONG).show();
        });
    }

    @Override
    public void onProgress(int progress) {
        runOnUiThread(() -> {
            progressSync.setProgress(progress);
            textSyncStatus.setText("جاري المزامنة... " + progress + "%");
        });
    }

    // DataSyncManager.BackupListCallback implementation
    @Override
    public void onSuccess(List<BackupData> backups) {
        runOnUiThread(() -> {
            btnLoadBackups.setEnabled(true);
            btnLoadBackups.setText("تحميل قائمة النسخ الاحتياطية");
            
            backupsList.clear();
            backupsList.addAll(backups);
            backupAdapter.notifyDataSetChanged();
            
            recyclerBackups.setVisibility(backups.isEmpty() ? View.GONE : View.VISIBLE);
            
            if (backups.isEmpty()) {
                Toast.makeText(this, "لا توجد نسخ احتياطية لهذا العميل", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "تم تحميل " + backups.size() + " نسخة احتياطية", Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
