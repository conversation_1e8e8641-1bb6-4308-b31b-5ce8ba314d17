package com.alrbea.androidapp.ui;

import android.app.DatePickerDialog;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;

import com.alrbea.androidapp.R;
import com.google.android.material.textfield.TextInputEditText;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

public class NewsBannerSettingsActivity extends BaseActivity {
    
    private Toolbar toolbar;
    private SwitchCompat switchEnableNews;
    private LinearLayout layoutSettingsContainer;
    private Spinner spinnerReadyTexts;
    private TextInputEditText editMessage;
    private TextInputEditText editStartDate;
    private TextInputEditText editEndDate;
    private Button btnSave;
    
    private SharedPreferences sharedPreferences;
    private Calendar startDateCalendar;
    private Calendar endDateCalendar;
    private SimpleDateFormat dateFormat;
    
    // Ready text templates
    private String[] readyTexts = {
        "اختر نص جاهز...",
        "أهلاً وسهلاً بكم في تطبيق مواقيت الصلاة",
        "لا تنسوا الدعاء في أوقات الإجابة",
        "رمضان كريم وكل عام وأنتم بخير",
        "عيد مبارك وكل عام وأنتم بخير",
        "الجمعة المباركة - لا تنسوا صلاة الجمعة",
        "ادعوا الله في الثلث الأخير من الليل"
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_news_banner_settings);
        
        initializeViews();
        setupToolbar();
        setupSharedPreferences();
        setupDateFormat();
        setupSpinner();
        setupListeners();
        loadSavedSettings();
    }
    
    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        switchEnableNews = findViewById(R.id.switch_enable_news);
        layoutSettingsContainer = findViewById(R.id.layout_settings_container);
        spinnerReadyTexts = findViewById(R.id.spinner_ready_texts);
        editMessage = findViewById(R.id.edit_message);
        editStartDate = findViewById(R.id.edit_start_date);
        editEndDate = findViewById(R.id.edit_end_date);
        btnSave = findViewById(R.id.btn_save);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }
    }
    
    private void setupSharedPreferences() {
        sharedPreferences = getSharedPreferences("news_banner_prefs", MODE_PRIVATE);
    }
    
    private void setupDateFormat() {
        dateFormat = new SimpleDateFormat("yyyy/MM/dd", Locale.getDefault());
        startDateCalendar = Calendar.getInstance();
        endDateCalendar = Calendar.getInstance();
        endDateCalendar.add(Calendar.DAY_OF_MONTH, 7); // Default to 7 days from now
    }
    
    private void setupSpinner() {
        ArrayAdapter<String> adapter = new ArrayAdapter<>(
            this, 
            android.R.layout.simple_spinner_item, 
            readyTexts
        );
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerReadyTexts.setAdapter(adapter);
    }
    
    private void setupListeners() {
        switchEnableNews.setOnCheckedChangeListener((buttonView, isChecked) -> {
            layoutSettingsContainer.setVisibility(isChecked ? View.VISIBLE : View.GONE);
            if (!isChecked) {
                // Clear the news banner when disabled
                sharedPreferences.edit()
                    .putBoolean("news_enabled", false)
                    .apply();
            }
        });
        
        spinnerReadyTexts.setOnItemSelectedListener(new android.widget.AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(android.widget.AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) { // Skip the first "Choose..." option
                    editMessage.setText(readyTexts[position]);
                }
            }
            
            @Override
            public void onNothingSelected(android.widget.AdapterView<?> parent) {
                // Do nothing
            }
        });
        
        editStartDate.setOnClickListener(v -> showDatePicker(true));
        editEndDate.setOnClickListener(v -> showDatePicker(false));
        
        btnSave.setOnClickListener(v -> saveNewsSettings());
    }
    
    private void showDatePicker(boolean isStartDate) {
        Calendar calendar = isStartDate ? startDateCalendar : endDateCalendar;
        
        DatePickerDialog datePickerDialog = new DatePickerDialog(
            this,
            (view, year, month, dayOfMonth) -> {
                calendar.set(year, month, dayOfMonth);
                String formattedDate = dateFormat.format(calendar.getTime());
                
                if (isStartDate) {
                    editStartDate.setText(formattedDate);
                } else {
                    editEndDate.setText(formattedDate);
                }
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        );
        
        datePickerDialog.show();
    }
    
    private void saveNewsSettings() {
        String message = editMessage.getText().toString().trim();
        String startDate = editStartDate.getText().toString().trim();
        String endDate = editEndDate.getText().toString().trim();
        
        // Validation
        if (message.isEmpty()) {
            showToast(getString(R.string.please_enter_message));
            return;
        }
        
        if (startDate.isEmpty() || endDate.isEmpty()) {
            showToast(getString(R.string.please_select_dates));
            return;
        }
        
        if (endDateCalendar.before(startDateCalendar)) {
            showToast(getString(R.string.end_date_must_be_after_start_date));
            return;
        }
        
        // Save settings
        sharedPreferences.edit()
            .putBoolean("news_enabled", switchEnableNews.isChecked())
            .putString("news_message", message)
            .putString("news_start_date", startDate)
            .putString("news_end_date", endDate)
            .putLong("news_start_timestamp", startDateCalendar.getTimeInMillis())
            .putLong("news_end_timestamp", endDateCalendar.getTimeInMillis())
            .apply();
        
        showToast(getString(R.string.news_message_saved));
        finish();
    }
    
    private void loadSavedSettings() {
        boolean isEnabled = sharedPreferences.getBoolean("news_enabled", false);
        switchEnableNews.setChecked(isEnabled);
        layoutSettingsContainer.setVisibility(isEnabled ? View.VISIBLE : View.GONE);
        
        String savedMessage = sharedPreferences.getString("news_message", "");
        editMessage.setText(savedMessage);
        
        String savedStartDate = sharedPreferences.getString("news_start_date", "");
        String savedEndDate = sharedPreferences.getString("news_end_date", "");
        
        if (!savedStartDate.isEmpty()) {
            editStartDate.setText(savedStartDate);
            startDateCalendar.setTimeInMillis(sharedPreferences.getLong("news_start_timestamp", System.currentTimeMillis()));
        }
        
        if (!savedEndDate.isEmpty()) {
            editEndDate.setText(savedEndDate);
            endDateCalendar.setTimeInMillis(sharedPreferences.getLong("news_end_timestamp", System.currentTimeMillis()));
        }
    }
    
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
