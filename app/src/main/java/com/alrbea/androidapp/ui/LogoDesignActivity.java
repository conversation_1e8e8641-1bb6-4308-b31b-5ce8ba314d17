package com.alrbea.androidapp.ui;

import android.app.AlertDialog;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.TextDesign;
import com.alrbea.androidapp.ui.adapter.FontSpinnerAdapter;
import com.alrbea.androidapp.ui.adapter.TextDesignOptionsAdapter;
import com.alrbea.androidapp.utils.FontManager;
import com.alrbea.androidapp.utils.TextDesignManager;
import com.google.android.material.checkbox.MaterialCheckBox;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class LogoDesignActivity extends BaseActivity {
    
    public static final String EXTRA_DESIGN_ID = "design_id";
    public static final String EXTRA_IS_EDIT_MODE = "is_edit_mode";
    
    private FrameLayout flMain;
    private ImageView ivBitmap;
    private View absMain;
    private View tdTvDemo1;
    private View tdTvDemo2;
    
    // Control elements
    private Spinner spFonts;
    private CardView vColor;
    private CardView vColor2;
    private Button btnSizeAdd;
    private Button btnSizeMinus;
    private View spTextInput;
    private Spinner optionsSpinner;
    private Spinner linesSpinner;
    private MaterialCheckBox cbShowInPrayers;
    
    private Toolbar toolbar;
    
    // Managers and utilities
    private TextDesignManager designManager;
    private FontManager fontManager;
    
    // Current design state
    private TextDesign currentDesign;
    private boolean isEditMode = false;
    private int currentTextColor = Color.BLACK;
    private int currentBackgroundColor = Color.WHITE;
    private float currentTextSize = 16f;
    private String currentFontFamily = "default";
    private String currentText = "مثال";
    
    // Touch handling for positioning
    private float lastTouchX, lastTouchY;
    private View selectedTextView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_logo_design);
        
        designManager = new TextDesignManager(this);
        fontManager = new FontManager(this);
        
        // Get extras
        String designId = getIntent().getStringExtra(EXTRA_DESIGN_ID);
        isEditMode = getIntent().getBooleanExtra(EXTRA_IS_EDIT_MODE, false);
        
        if (isEditMode && designId != null) {
            currentDesign = designManager.getTextDesign(designId);
        }
        
        if (currentDesign == null) {
            currentDesign = new TextDesign(UUID.randomUUID().toString(), "تصميم جديد");
            isEditMode = false;
        }
        
        initViews();
        setupToolbar();
        setupSpinners();
        setupClickListeners();
        setupTouchHandling();
        loadDesignData();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        flMain = findViewById(R.id.fl_main);
        ivBitmap = findViewById(R.id.iv_bitmap);
        absMain = findViewById(R.id.abs_main);
        tdTvDemo1 = findViewById(R.id.td_tv_demo_down1);
        tdTvDemo2 = findViewById(R.id.td_tv_demo_down2);
        
        spFonts = findViewById(R.id.sp_fonts);
        vColor = findViewById(R.id.v_color);
        vColor2 = findViewById(R.id.v_color2);
        btnSizeAdd = findViewById(R.id.btn_size_add);
        btnSizeMinus = findViewById(R.id.btn_size_minus);
        spTextInput = findViewById(R.id.sp_text_input);
        optionsSpinner = findViewById(R.id.options_Spinner_textdesign);
        linesSpinner = findViewById(R.id.lines_Spinner_textdesign);
        cbShowInPrayers = findViewById(R.id.td_cb_show_in_prayers);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle(isEditMode ? "تعديل التصميم" : "تصميم شعار جديد");
        }
    }
    
    private void setupSpinners() {
        // Font spinner
        List<String> fonts = fontManager.getAvailableFonts();
        FontSpinnerAdapter fontAdapter = new FontSpinnerAdapter(this, fonts);
        spFonts.setAdapter(fontAdapter);
        
        // Options spinner (text styles)
        List<String> options = new ArrayList<>();
        options.add("عادي");
        options.add("عريض");
        options.add("مائل");
        options.add("عريض مائل");
        TextDesignOptionsAdapter optionsAdapter = new TextDesignOptionsAdapter(this, options);
        optionsSpinner.setAdapter(optionsAdapter);
        
        // Lines spinner (single/multi line)
        List<String> lines = new ArrayList<>();
        lines.add("سطر واحد");
        lines.add("سطرين");
        lines.add("ثلاثة أسطر");
        TextDesignOptionsAdapter linesAdapter = new TextDesignOptionsAdapter(this, lines);
        linesSpinner.setAdapter(linesAdapter);
    }
    
    private void setupClickListeners() {
        // Font selection
        spFonts.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                currentFontFamily = fontManager.getAvailableFonts().get(position);
                updateTextAppearance();
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
        
        // Color picker
        vColor.setOnClickListener(v -> showColorPicker(true)); // Text color
        vColor2.setOnClickListener(v -> showColorPicker(false)); // Background color
        
        // Size controls
        btnSizeAdd.setOnClickListener(v -> {
            currentTextSize += 2f;
            updateTextAppearance();
        });
        
        btnSizeMinus.setOnClickListener(v -> {
            if (currentTextSize > 8f) {
                currentTextSize -= 2f;
                updateTextAppearance();
            }
        });
        
        // Text input
        spTextInput.setOnClickListener(v -> showTextInputDialog());
        
        // Options and lines spinners
        optionsSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                updateTextStyle(position);
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
        
        linesSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                updateTextLines(position + 1);
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
        
        // Show in prayers checkbox
        cbShowInPrayers.setOnCheckedChangeListener((buttonView, isChecked) -> {
            currentDesign.setShowInPrayers(isChecked);
        });
    }
    
    private void setupTouchHandling() {
        // Make text views draggable
        setupDraggableView(tdTvDemo1);
        setupDraggableView(tdTvDemo2);
    }
    
    private void setupDraggableView(View view) {
        view.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    selectedTextView = v;
                    lastTouchX = event.getRawX();
                    lastTouchY = event.getRawY();
                    return true;
                    
                case MotionEvent.ACTION_MOVE:
                    float deltaX = event.getRawX() - lastTouchX;
                    float deltaY = event.getRawY() - lastTouchY;
                    
                    v.setX(v.getX() + deltaX);
                    v.setY(v.getY() + deltaY);
                    
                    lastTouchX = event.getRawX();
                    lastTouchY = event.getRawY();
                    return true;
                    
                case MotionEvent.ACTION_UP:
                    selectedTextView = null;
                    saveCurrentPosition(v);
                    return true;
            }
            return false;
        });
    }
    
    private void showColorPicker(boolean isTextColor) {
        // Simple color picker with predefined colors
        int[] colors = {
            Color.BLACK, Color.WHITE, Color.RED, Color.GREEN, Color.BLUE,
            Color.YELLOW, Color.CYAN, Color.MAGENTA, Color.GRAY
        };

        String[] colorNames = {
            "أسود", "أبيض", "أحمر", "أخضر", "أزرق",
            "أصفر", "سماوي", "بنفسجي", "رمادي"
        };

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(isTextColor ? "لون النص" : "لون الخلفية");

        builder.setItems(colorNames, (dialog, which) -> {
            int selectedColor = colors[which];
            if (isTextColor) {
                currentTextColor = selectedColor;
                vColor.setCardBackgroundColor(selectedColor);
            } else {
                currentBackgroundColor = selectedColor;
                vColor2.setCardBackgroundColor(selectedColor);
            }
            updateTextAppearance();
        });

        builder.setNegativeButton("إلغاء", null);
        builder.show();
    }
    
    private void showTextInputDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("إدخال النص");
        
        EditText editText = new EditText(this);
        editText.setText(currentText);
        editText.setHint("اكتب النص هنا");
        builder.setView(editText);
        
        builder.setPositiveButton("حفظ", (dialog, which) -> {
            String newText = editText.getText().toString().trim();
            if (!TextUtils.isEmpty(newText)) {
                currentText = newText;
                updateTextContent();
                updateTextAppearance();
            }
        });
        
        builder.setNegativeButton("إلغاء", null);
        builder.show();
    }
    
    private void updateTextAppearance() {
        // Update both demo text views
        updateTextView(tdTvDemo1);
        updateTextView(tdTvDemo2);
    }
    
    private void updateTextView(View textView) {
        if (textView instanceof TextView) {
            TextView tv = (TextView) textView;
            tv.setTextColor(currentTextColor);
            tv.setBackgroundColor(currentBackgroundColor);
            tv.setTextSize(currentTextSize);
            
            // Apply font
            Typeface typeface = fontManager.getTypeface(currentFontFamily);
            if (typeface != null) {
                tv.setTypeface(typeface);
            }
        }
    }
    
    private void updateTextContent() {
        updateTextLines(linesSpinner.getSelectedItemPosition() + 1);
    }

    private void splitTextIntoLines(String text, int lineCount) {
        if (TextUtils.isEmpty(text)) {
            text = "مثال";
        }

        String[] words = text.split("\\s+");
        String[] lines = new String[3];

        if (lineCount == 1) {
            lines[0] = text;
            lines[1] = "";
            lines[2] = "";
        } else if (lineCount == 2) {
            int midPoint = words.length / 2;
            StringBuilder line1 = new StringBuilder();
            StringBuilder line2 = new StringBuilder();

            for (int i = 0; i < words.length; i++) {
                if (i < midPoint) {
                    if (line1.length() > 0) line1.append(" ");
                    line1.append(words[i]);
                } else {
                    if (line2.length() > 0) line2.append(" ");
                    line2.append(words[i]);
                }
            }

            lines[0] = line1.toString();
            lines[1] = line2.toString();
            lines[2] = "";
        } else if (lineCount == 3) {
            int wordsPerLine = words.length / 3;
            StringBuilder line1 = new StringBuilder();
            StringBuilder line2 = new StringBuilder();
            StringBuilder line3 = new StringBuilder();

            for (int i = 0; i < words.length; i++) {
                if (i < wordsPerLine) {
                    if (line1.length() > 0) line1.append(" ");
                    line1.append(words[i]);
                } else if (i < wordsPerLine * 2) {
                    if (line2.length() > 0) line2.append(" ");
                    line2.append(words[i]);
                } else {
                    if (line3.length() > 0) line3.append(" ");
                    line3.append(words[i]);
                }
            }

            lines[0] = line1.toString();
            lines[1] = line2.toString();
            lines[2] = line3.toString();
        }

        // Update text views with split text
        if (tdTvDemo1 instanceof TextView) {
            ((TextView) tdTvDemo1).setText(lines[0]);
        }
        if (tdTvDemo2 instanceof TextView) {
            ((TextView) tdTvDemo2).setText(lines[1]);
        }
    }
    
    private void updateTextStyle(int styleIndex) {
        int style = Typeface.NORMAL;
        switch (styleIndex) {
            case 1: style = Typeface.BOLD; break;
            case 2: style = Typeface.ITALIC; break;
            case 3: style = Typeface.BOLD_ITALIC; break;
        }
        
        Typeface typeface = fontManager.getTypeface(currentFontFamily, style);
        if (tdTvDemo1 instanceof TextView) {
            ((TextView) tdTvDemo1).setTypeface(typeface);
        }
        if (tdTvDemo2 instanceof TextView) {
            ((TextView) tdTvDemo2).setTypeface(typeface);
        }
    }
    
    private void updateTextLines(int lineCount) {
        // Split text into lines and update visibility
        splitTextIntoLines(currentText, lineCount);

        // Show/hide text views based on line count
        tdTvDemo1.setVisibility(lineCount >= 1 ? View.VISIBLE : View.INVISIBLE);
        tdTvDemo2.setVisibility(lineCount >= 2 ? View.VISIBLE : View.INVISIBLE);

        // Update text appearance after splitting
        updateTextAppearance();
    }
    
    private void saveCurrentPosition(View view) {
        // Save position to current design
        if (view == tdTvDemo1) {
            currentDesign.setLine1X(view.getX());
            currentDesign.setLine1Y(view.getY());
        } else if (view == tdTvDemo2) {
            currentDesign.setLine2X(view.getX());
            currentDesign.setLine2Y(view.getY());
        }
    }
    
    private void loadDesignData() {
        if (isEditMode && currentDesign != null) {
            // Load existing design data
            currentText = currentDesign.getText() != null ? currentDesign.getText() : "مثال";
            currentTextColor = currentDesign.getTextColor();
            currentBackgroundColor = currentDesign.getBackgroundColor();
            currentTextSize = currentDesign.getTextSize();
            currentFontFamily = currentDesign.getFontFamily() != null ? currentDesign.getFontFamily() : "default";

            // Set spinner selections
            List<String> fonts = fontManager.getAvailableFonts();
            int fontIndex = fonts.indexOf(currentFontFamily);
            if (fontIndex >= 0) {
                spFonts.setSelection(fontIndex);
            }

            optionsSpinner.setSelection(currentDesign.getTextStyle());
            linesSpinner.setSelection(currentDesign.getLineCount() - 1);

            // Update UI
            updateTextContent();
            updateTextAppearance();
            vColor.setCardBackgroundColor(currentTextColor);
            vColor2.setCardBackgroundColor(currentBackgroundColor);
            cbShowInPrayers.setChecked(currentDesign.isShowInPrayers());

            // Set positions after layout
            tdTvDemo1.post(() -> {
                tdTvDemo1.setX(currentDesign.getLine1X());
                tdTvDemo1.setY(currentDesign.getLine1Y());
            });

            tdTvDemo2.post(() -> {
                tdTvDemo2.setX(currentDesign.getLine2X());
                tdTvDemo2.setY(currentDesign.getLine2Y());
            });
        } else {
            // Set default values
            currentText = "مثال";
            updateTextContent();
            updateTextAppearance();
            vColor.setCardBackgroundColor(currentTextColor);
            vColor2.setCardBackgroundColor(currentBackgroundColor);
        }
    }
    
    private void saveDesign() {
        if (TextUtils.isEmpty(currentText)) {
            Toast.makeText(this, "يرجى إدخال النص", Toast.LENGTH_SHORT).show();
            return;
        }

        // Update design with current values
        currentDesign.setText(currentText);
        currentDesign.setTextColor(currentTextColor);
        currentDesign.setBackgroundColor(currentBackgroundColor);
        currentDesign.setTextSize(currentTextSize);
        currentDesign.setFontFamily(currentFontFamily);
        currentDesign.setShowInPrayers(cbShowInPrayers.isChecked());

        // Save current positions
        currentDesign.setLine1X(tdTvDemo1.getX());
        currentDesign.setLine1Y(tdTvDemo1.getY());
        currentDesign.setLine2X(tdTvDemo2.getX());
        currentDesign.setLine2Y(tdTvDemo2.getY());

        // Save current style and line settings
        currentDesign.setTextStyle(optionsSpinner.getSelectedItemPosition());
        currentDesign.setLineCount(linesSpinner.getSelectedItemPosition() + 1);

        if (designManager.saveTextDesign(currentDesign)) {
            Toast.makeText(this, "تم حفظ التصميم بنجاح", Toast.LENGTH_SHORT).show();
            setResult(RESULT_OK);
            finish();
        } else {
            Toast.makeText(this, "فشل في حفظ التصميم", Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_logo_design, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        } else if (item.getItemId() == R.id.action_save) {
            saveDesign();
            return true;
        } else if (item.getItemId() == R.id.action_preview) {
            showPreview();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    private void showPreview() {
        // Show preview of how the design will look in the main activity
//        Intent intent = new Intent(this, LogoPreviewActivity.class);
//        intent.putExtra(LogoPreviewActivity.EXTRA_DESIGN_ID, currentDesign.getId());
//        startActivity(intent);
    }
}
