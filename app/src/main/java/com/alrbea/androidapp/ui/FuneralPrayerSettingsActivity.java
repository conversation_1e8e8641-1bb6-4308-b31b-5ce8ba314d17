package com.alrbea.androidapp.ui;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;

import com.alrbea.androidapp.R;

public class FuneralPrayerSettingsActivity extends BaseActivity {
    
    private Toolbar toolbar;
    private SwitchCompat switchEnableFuneral;
    private LinearLayout layoutSettingsContainer;
    private Spinner spinnerTemplateMessages;
    private Spinner spinnerPrayerSelection;
    private Button btnSave;
    
    private SharedPreferences sharedPreferences;
    
    // Template messages for funeral prayers
    private String[] templateMessages = {
        "اختر رسالة جاهزة...",
        "إنا لله وإنا إليه راجعون - اللهم اغفر له وارحمه",
        "اللهم اغفر لميتنا وارحمه وعافه واعف عنه",
        "اللهم أدخله الجنة وأعذه من عذاب القبر",
        "اللهم اجعل قبره روضة من رياض الجنة",
        "اللهم نور له قبره ووسع له مدخله",
        "اللهم اغسله بالماء والثلج والبرد",
        "اللهم أبدله داراً خيراً من داره وأهلاً خيراً من أهله"
    };
    
    // Prayer names
    private String[] prayerNames = {
        "اختر الصلاة...",
        "الفجر",
        "الظهر", 
        "العصر",
        "المغرب",
        "العشاء"
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_funeral_prayer_settings);
        
        initializeViews();
        setupToolbar();
        setupSharedPreferences();
        setupSpinners();
        setupListeners();
        loadSavedSettings();
    }
    
    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        switchEnableFuneral = findViewById(R.id.switch_enable_funeral);
        layoutSettingsContainer = findViewById(R.id.layout_settings_container);
        spinnerTemplateMessages = findViewById(R.id.spinner_template_messages);
        spinnerPrayerSelection = findViewById(R.id.spinner_prayer_selection);
        btnSave = findViewById(R.id.btn_save);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }
    }
    
    private void setupSharedPreferences() {
        sharedPreferences = getSharedPreferences("funeral_prayer_prefs", MODE_PRIVATE);
    }
    
    private void setupSpinners() {
        // Setup template messages spinner
        ArrayAdapter<String> templateAdapter = new ArrayAdapter<>(
            this, 
            android.R.layout.simple_spinner_item, 
            templateMessages
        );
        templateAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerTemplateMessages.setAdapter(templateAdapter);
        
        // Setup prayer selection spinner
        ArrayAdapter<String> prayerAdapter = new ArrayAdapter<>(
            this, 
            android.R.layout.simple_spinner_item, 
            prayerNames
        );
        prayerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerPrayerSelection.setAdapter(prayerAdapter);
    }
    
    private void setupListeners() {
        switchEnableFuneral.setOnCheckedChangeListener((buttonView, isChecked) -> {
            layoutSettingsContainer.setVisibility(isChecked ? View.VISIBLE : View.GONE);
            if (!isChecked) {
                // Clear the funeral messages when disabled
                sharedPreferences.edit()
                    .putBoolean("funeral_enabled", false)
                    .apply();
            }
        });
        
        btnSave.setOnClickListener(v -> saveFuneralSettings());
    }
    
    private void saveFuneralSettings() {
        int templatePosition = spinnerTemplateMessages.getSelectedItemPosition();
        int prayerPosition = spinnerPrayerSelection.getSelectedItemPosition();
        
        // Validation
        if (templatePosition <= 0) {
            showToast(getString(R.string.please_select_funeral_template));
            return;
        }
        
        if (prayerPosition <= 0) {
            showToast(getString(R.string.please_select_funeral_prayer));
            return;
        }
        
        String selectedTemplate = templateMessages[templatePosition];
        String selectedPrayer = prayerNames[prayerPosition];
        
        // Save settings
        sharedPreferences.edit()
            .putBoolean("funeral_enabled", switchEnableFuneral.isChecked())
            .putString("selected_template", selectedTemplate)
            .putString("selected_prayer", selectedPrayer)
            .putInt("template_position", templatePosition)
            .putInt("prayer_position", prayerPosition)
            .apply();
        
        showToast(getString(R.string.funeral_prayer_saved));
        finish();
    }
    
    private void loadSavedSettings() {
        boolean isEnabled = sharedPreferences.getBoolean("funeral_enabled", false);
        switchEnableFuneral.setChecked(isEnabled);
        layoutSettingsContainer.setVisibility(isEnabled ? View.VISIBLE : View.GONE);
        
        int savedTemplatePosition = sharedPreferences.getInt("template_position", 0);
        int savedPrayerPosition = sharedPreferences.getInt("prayer_position", 0);
        
        if (savedTemplatePosition > 0 && savedTemplatePosition < templateMessages.length) {
            spinnerTemplateMessages.setSelection(savedTemplatePosition);
        }
        
        if (savedPrayerPosition > 0 && savedPrayerPosition < prayerNames.length) {
            spinnerPrayerSelection.setSelection(savedPrayerPosition);
        }
    }
    
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
