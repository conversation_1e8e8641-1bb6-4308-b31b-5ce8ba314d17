package com.alrbea.androidapp.data.model;

public class City {
    private String code;
    private String nameAr;
    private String nameEn;
    private double latitude;
    private double longitude;
    private String timezone;

    public City(String code, String nameAr, String nameEn, double latitude, double longitude, String timezone) {
        this.code = code;
        this.nameAr = nameAr;
        this.nameEn = nameEn;
        this.latitude = latitude;
        this.longitude = longitude;
        this.timezone = timezone;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public String getDisplayName(String language) {
        return "ar".equals(language) ? nameAr : nameEn;
    }
}
