package com.alrbea.androidapp.data.repository;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.alrbea.androidapp.R;
import com.alrbea.androidapp.data.model.ThemeTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ThemeRepository {
    private static final String TAG = "ThemeRepository";
    private static final String PREFS_NAME = "ThemeTemplateSettings";
    private static final String KEY_SELECTED_THEME = "selected_theme";
    private static final String DEFAULT_THEME_ID = "local_2";

    private final SharedPreferences preferences;
    private final MutableLiveData<String> errorLiveData = new MutableLiveData<>();
    private final MutableLiveData<Boolean> loadingLiveData = new MutableLiveData<>();
    private final MutableLiveData<ThemeTemplate> currentTheme = new MutableLiveData<>();
    private final MutableLiveData<List<ThemeTemplate>> themesLiveData = new MutableLiveData<>();

    public ThemeRepository(@NonNull Context context) {
        preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        initializeThemes();
        loadCurrentTheme();
    }

    private void initializeThemes() {
        List<ThemeTemplate> themes = new ArrayList<>();
        themes.add(new ThemeTemplate(
                "local_1",
                "ثيم الطبيعة",
                R.drawable.l_theme_1,
                R.layout.activity_theme_templates,
                R.drawable.p_theme_1,
                R.layout.activity_theme_templates,
                true,
                ThemeTemplate.ThemeType.LOCAL,
                true
        ));
        themes.add(new ThemeTemplate(
                "local_2",
                "ثيم كلاسيكي",
                R.drawable.l_theme_2,
                R.layout.activity_theme_templates,
                R.drawable.p_theme_2,
                R.layout.activity_theme_templates,
                true,
                ThemeTemplate.ThemeType.LOCAL,
                true
        ));
        themes.add(new ThemeTemplate(
                "local_3",
                "ثيم الأزهار",
                R.drawable.l_theme_3,
                R.layout.activity_theme_templates,
                R.drawable.p_theme_3,
                R.layout.activity_theme_templates,
                true,
                ThemeTemplate.ThemeType.LOCAL,
                true
        ));
        themes.add(new ThemeTemplate(
                "local_4",
                "ثيم عصري",
                R.drawable.l_theme_4,
                R.layout.content_main_blue,
                R.drawable.p_theme_4,
                R.layout.content_main_blue,
                true,
                ThemeTemplate.ThemeType.LOCAL,
                true
        ));
        themes.add(new ThemeTemplate(
                "local_5",
                "ثيم أنيق",
                R.drawable.l_theme_5,
                R.layout.activity_theme_templates,
                R.drawable.p_theme_5,
                R.layout.activity_theme_templates,
                true,
                ThemeTemplate.ThemeType.LOCAL,
                true
        ));
        themes.add(new ThemeTemplate(
                "local_6",
                "ثيم راقي",
                R.drawable.l_theme_6,
                R.layout.activity_theme_templates,
                R.drawable.p_theme_6,
                R.layout.activity_theme_templates,
                true,
                ThemeTemplate.ThemeType.LOCAL,
                true
        ));
        themesLiveData.setValue(Collections.unmodifiableList(themes));
    }

    public LiveData<String> getErrorLiveData() {
        return errorLiveData;
    }

    public LiveData<Boolean> getLoadingLiveData() {
        return loadingLiveData;
    }

    public LiveData<ThemeTemplate> getCurrentThemeLiveData() {
        return currentTheme;
    }

    public LiveData<List<ThemeTemplate>> getThemesLiveData() {
        return themesLiveData;
    }

    public void loadCurrentTheme() {
        loadingLiveData.setValue(true);
        try {
            String savedThemeId = preferences.getString(KEY_SELECTED_THEME, DEFAULT_THEME_ID);
            ThemeTemplate theme = findThemeById(savedThemeId);

            if (theme == null) {
                Log.w(TAG, "Saved theme not found, using default");
                theme = findThemeById(DEFAULT_THEME_ID);
                saveThemeId(DEFAULT_THEME_ID);
            }

            currentTheme.setValue(theme);
            errorLiveData.setValue(null);
        } catch (Exception e) {
            Log.e(TAG, "Error loading theme", e);
            errorLiveData.setValue(e.getMessage());
        } finally {
            loadingLiveData.setValue(false);
        }
    }

    public void setCurrentTheme(@NonNull ThemeTemplate theme) {
        try {
            saveThemeId(theme.getId());
            currentTheme.setValue(theme);
            errorLiveData.setValue(null);
        } catch (Exception e) {
            Log.e(TAG, "Error setting theme", e);
            errorLiveData.setValue(e.getMessage());
        }
    }

    private ThemeTemplate findThemeById(String themeId) {
        List<ThemeTemplate> themes = themesLiveData.getValue();
        if (themes != null) {
            for (ThemeTemplate theme : themes) {
                if (theme.getId().equals(themeId)) {
                    return theme;
                }
            }
        }
        return null;
    }

    private void saveThemeId(String themeId) {
        preferences.edit().putString(KEY_SELECTED_THEME, themeId).apply();
    }
}