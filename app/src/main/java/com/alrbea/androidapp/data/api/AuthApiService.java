package com.alrbea.androidapp.data.api;

import com.alrbea.androidapp.data.model.DeviceInfo;
import com.alrbea.androidapp.data.model.LoginRequest;
import com.alrbea.androidapp.data.model.LoginResult;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

public interface AuthApiService {
    
    @POST("api/auth/login")
    Call<LoginResult> login(@Body LoginRequest loginRequest);
    
    @POST("api/auth/device-info")
    Call<Void> sendDeviceInfo(@Body DeviceInfo deviceInfo);
    
    @FormUrlEncoded
    @POST("api/auth/logout")
    Call<Void> logout(@Field("token") String token);
}
