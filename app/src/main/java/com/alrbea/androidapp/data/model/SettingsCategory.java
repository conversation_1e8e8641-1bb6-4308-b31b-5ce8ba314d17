package com.alrbea.androidapp.data.model;

import java.util.List;
import java.util.Map;

public class SettingsCategory {
    private String id;
    private String titleAr;
    private String titleEn;
    private int iconRes;
    private List<SettingsItem> items;
    private boolean isExpanded;
    
    // New fields for sync functionality
    private String name;
    private String description;
    private int iconResource;
    private boolean enabled;
    private long size;
    private Map<String, Object> data;
    private String preferencesKey;

    // Original constructor
    public SettingsCategory(String id, String titleAr, String titleEn, int iconRes, List<SettingsItem> items) {
        this.id = id;
        this.titleAr = titleAr;
        this.titleEn = titleEn;
        this.iconRes = iconRes;
        this.items = items;
        this.isExpanded = false;
        this.enabled = true;
    }

    // New constructor for sync functionality
    public SettingsCategory(String id, String name, String description, int iconResource, String preferencesKey) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.iconResource = iconResource;
        this.preferencesKey = preferencesKey;
        this.enabled = true;
    }

    public SettingsCategory() {
        this.enabled = true;
    }

    // Original getters and setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public String getTitleEn() {
        return titleEn;
    }

    public void setTitleEn(String titleEn) {
        this.titleEn = titleEn;
    }

    public int getIconRes() {
        return iconRes;
    }

    public void setIconRes(int iconRes) {
        this.iconRes = iconRes;
    }

    public List<SettingsItem> getItems() {
        return items;
    }

    public void setItems(List<SettingsItem> items) {
        this.items = items;
    }

    public boolean isExpanded() {
        return isExpanded;
    }

    public void setExpanded(boolean expanded) {
        isExpanded = expanded;
    }

    public String getDisplayTitle(String language) {
        return "ar".equals(language) ? titleAr : titleEn;
    }

    // New getters and setters for sync functionality
    public String getName() {
        return name != null ? name : titleAr;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getIconResource() {
        return iconResource != 0 ? iconResource : iconRes;
    }

    public void setIconResource(int iconResource) {
        this.iconResource = iconResource;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public String getPreferencesKey() {
        return preferencesKey;
    }

    public void setPreferencesKey(String preferencesKey) {
        this.preferencesKey = preferencesKey;
    }

    public String getFormattedSize() {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
}
