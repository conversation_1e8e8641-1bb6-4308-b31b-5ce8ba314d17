package com.alrbea.androidapp.data.repository;

import android.content.Context;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.alrbea.androidapp.data.database.AppDatabase;
import com.alrbea.androidapp.data.dao.PrayerTimesDao;
import com.alrbea.androidapp.data.model.PrayerTimesEntity;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class PrayerTimesLocalRepository {
    private PrayerTimesDao prayerTimesDao;
    private ExecutorService executorService;
    private MutableLiveData<String> errorLiveData;

    public PrayerTimesLocalRepository(Context context) {
        AppDatabase database = AppDatabase.getInstance(context);
        prayerTimesDao = database.prayerTimesDao();
        executorService = Executors.newFixedThreadPool(4);
        errorLiveData = new MutableLiveData<>();
    }

    public void insert(PrayerTimesEntity prayerTime) {
        executorService.execute(() -> {
            try {
                prayerTimesDao.insert(prayerTime);
            } catch (Exception e) {
                errorLiveData.postValue("Database error: " + e.getMessage());
            }
        });
    }

    public void insertAll(List<PrayerTimesEntity> prayerTimes) {
        executorService.execute(() -> {
            try {
                prayerTimesDao.insertAll(prayerTimes);
            } catch (Exception e) {
                errorLiveData.postValue("Database error: " + e.getMessage());
            }
        });
    }

    public LiveData<PrayerTimesEntity> getPrayerTime(String country, String city, String method, String date) {
        return prayerTimesDao.getPrayerTime(country, city, method, date);
    }

    public LiveData<List<PrayerTimesEntity>> getAllPrayerTimes(String country, String city, String method) {
        return prayerTimesDao.getAllPrayerTimes(country, city, method);
    }

    public LiveData<List<PrayerTimesEntity>> getPrayerTimesByMonth(String country, String city, String method, String yearMonth) {
        return prayerTimesDao.getPrayerTimesByMonth(country, city, method, yearMonth);
    }

    public LiveData<PrayerTimesEntity> getTodayPrayerTime(String date) {
        return prayerTimesDao.getTodayPrayerTime(date);
    }

    public LiveData<String> getError() {
        return errorLiveData;
    }

    public void clearDatabase() {
        executorService.execute(() -> {
            try {
                prayerTimesDao.deleteAll();
            } catch (Exception e) {
                errorLiveData.postValue("Database error: " + e.getMessage());
            }
        });
    }

    public void shutdown() {
        executorService.shutdown();
    }
} 