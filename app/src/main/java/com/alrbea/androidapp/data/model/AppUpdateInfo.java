package com.alrbea.androidapp.data.model;

import org.json.JSONException;
import org.json.JSONObject;

public class AppUpdateInfo {
    private String versionName;
    private int versionCode;
    private String title;
    private String description;
    private String changelog;
    private long fileSize;
    private String fileSizeFormatted;
    private String downloadUrl;
    private String fileName;
    private String fileHash;
    private String releaseDate;
    private boolean isForced;
    private int minSupportedVersion;

    public AppUpdateInfo() {}

    public AppUpdateInfo(String versionName, int versionCode, String title, String description,
                        String changelog, long fileSize, String fileSizeFormatted, String downloadUrl,
                        String fileName, String fileHash, String releaseDate, boolean isForced,
                        int minSupportedVersion) {
        this.versionName = versionName;
        this.versionCode = versionCode;
        this.title = title;
        this.description = description;
        this.changelog = changelog;
        this.fileSize = fileSize;
        this.fileSizeFormatted = fileSizeFormatted;
        this.downloadUrl = downloadUrl;
        this.fileName = fileName;
        this.fileHash = fileHash;
        this.releaseDate = releaseDate;
        this.isForced = isForced;
        this.minSupportedVersion = minSupportedVersion;
    }

    // Static method to create from JSON
    public static AppUpdateInfo fromJson(JSONObject json) throws JSONException {
        AppUpdateInfo updateInfo = new AppUpdateInfo();
        updateInfo.versionName = json.getString("version_name");
        updateInfo.versionCode = json.getInt("version_code");
        updateInfo.title = json.getString("title");
        updateInfo.description = json.getString("description");
        updateInfo.changelog = json.optString("changelog", "");
        updateInfo.fileSize = json.getLong("file_size");
        updateInfo.fileSizeFormatted = json.getString("file_size_formatted");
        updateInfo.downloadUrl = json.getString("download_url");
        updateInfo.fileName = json.getString("file_name");
        updateInfo.fileHash = json.getString("file_hash");
        updateInfo.releaseDate = json.getString("release_date");
        updateInfo.isForced = json.getBoolean("is_forced");
        updateInfo.minSupportedVersion = json.optInt("min_supported_version", 0);
        return updateInfo;
    }

    // Convert to JSON
    public JSONObject toJson() throws JSONException {
        JSONObject json = new JSONObject();
        json.put("version_name", versionName);
        json.put("version_code", versionCode);
        json.put("title", title);
        json.put("description", description);
        json.put("changelog", changelog);
        json.put("file_size", fileSize);
        json.put("file_size_formatted", fileSizeFormatted);
        json.put("download_url", downloadUrl);
        json.put("file_name", fileName);
        json.put("file_hash", fileHash);
        json.put("release_date", releaseDate);
        json.put("is_forced", isForced);
        json.put("min_supported_version", minSupportedVersion);
        return json;
    }

    // Getters and Setters
    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public int getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(int versionCode) {
        this.versionCode = versionCode;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getChangelog() {
        return changelog;
    }

    public void setChangelog(String changelog) {
        this.changelog = changelog;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileSizeFormatted() {
        return fileSizeFormatted;
    }

    public void setFileSizeFormatted(String fileSizeFormatted) {
        this.fileSizeFormatted = fileSizeFormatted;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileHash() {
        return fileHash;
    }

    public void setFileHash(String fileHash) {
        this.fileHash = fileHash;
    }

    public String getReleaseDate() {
        return releaseDate;
    }

    public void setReleaseDate(String releaseDate) {
        this.releaseDate = releaseDate;
    }

    public boolean isForced() {
        return isForced;
    }

    public void setForced(boolean forced) {
        isForced = forced;
    }

    public int getMinSupportedVersion() {
        return minSupportedVersion;
    }

    public void setMinSupportedVersion(int minSupportedVersion) {
        this.minSupportedVersion = minSupportedVersion;
    }

    @Override
    public String toString() {
        return "AppUpdateInfo{" +
                "versionName='" + versionName + '\'' +
                ", versionCode=" + versionCode +
                ", title='" + title + '\'' +
                ", fileSize=" + fileSize +
                ", isForced=" + isForced +
                '}';
    }
}
