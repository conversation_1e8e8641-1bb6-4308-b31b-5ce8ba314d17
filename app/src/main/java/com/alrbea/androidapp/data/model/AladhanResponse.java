package com.alrbea.androidapp.data.model;

import com.google.gson.annotations.SerializedName;

public class AladhanResponse {
    @SerializedName("code")
    private int code;
    
    @SerializedName("status")
    private String status;
    
    @SerializedName("data")
    private AladhanData data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public AladhanData getData() {
        return data;
    }

    public void setData(AladhanData data) {
        this.data = data;
    }

    public static class AladhanData {
        @SerializedName("timings")
        private AladhanTimings timings;
        
        @SerializedName("date")
        private AladhanDate date;

        public AladhanTimings getTimings() {
            return timings;
        }

        public void setTimings(AladhanTimings timings) {
            this.timings = timings;
        }

        public AladhanDate getDate() {
            return date;
        }

        public void setDate(AladhanDate date) {
            this.date = date;
        }
    }

    public static class AladhanTimings {
        @SerializedName("Fajr")
        private String fajr;
        
        @SerializedName("Sunrise")
        private String sunrise;
        
        @SerializedName("Dhuhr")
        private String dhuhr;
        
        @SerializedName("Asr")
        private String asr;
        
        @SerializedName("Maghrib")
        private String maghrib;
        
        @SerializedName("Isha")
        private String isha;

        public String getFajr() {
            return fajr;
        }

        public void setFajr(String fajr) {
            this.fajr = fajr;
        }

        public String getSunrise() {
            return sunrise;
        }

        public void setSunrise(String sunrise) {
            this.sunrise = sunrise;
        }

        public String getDhuhr() {
            return dhuhr;
        }

        public void setDhuhr(String dhuhr) {
            this.dhuhr = dhuhr;
        }

        public String getAsr() {
            return asr;
        }

        public void setAsr(String asr) {
            this.asr = asr;
        }

        public String getMaghrib() {
            return maghrib;
        }

        public void setMaghrib(String maghrib) {
            this.maghrib = maghrib;
        }

        public String getIsha() {
            return isha;
        }

        public void setIsha(String isha) {
            this.isha = isha;
        }
    }

    public static class AladhanDate {
        @SerializedName("readable")
        private String readable;
        
        @SerializedName("timestamp")
        private String timestamp;

        public String getReadable() {
            return readable;
        }

        public void setReadable(String readable) {
            this.readable = readable;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }
    }
} 