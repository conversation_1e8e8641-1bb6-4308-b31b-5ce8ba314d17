package com.alrbea.androidapp.data.model;

import com.google.gson.annotations.SerializedName;

public class PrayerTimes {
    @SerializedName("fajr")
    private String fajr;
    
    @SerializedName("sunrise")
    private String sunrise;
    
    @SerializedName("dhuhr")
    private String dhuhr;
    
    @SerializedName("asr")
    private String asr;
    
    @SerializedName("maghrib")
    private String maghrib;
    
    @SerializedName("isha")
    private String isha;
    
    @SerializedName("date")
    private String date;
    
    @SerializedName("city")
    private String city;

    // Constructors
    public PrayerTimes() {}

    public PrayerTimes(String fajr, String sunrise, String dhuhr, String asr, String maghrib, String isha, String date, String city) {
        this.fajr = fajr;
        this.sunrise = sunrise;
        this.dhuhr = dhuhr;
        this.asr = asr;
        this.maghrib = maghrib;
        this.isha = isha;
        this.date = date;
        this.city = city;
    }

    // Getters and Setters
    public String getFajr() {
        return fajr;
    }

    public void setFajr(String fajr) {
        this.fajr = fajr;
    }

    public String getSunrise() {
        return sunrise;
    }

    public void setSunrise(String sunrise) {
        this.sunrise = sunrise;
    }

    public String getDhuhr() {
        return dhuhr;
    }

    public void setDhuhr(String dhuhr) {
        this.dhuhr = dhuhr;
    }

    public String getAsr() {
        return asr;
    }

    public void setAsr(String asr) {
        this.asr = asr;
    }

    public String getMaghrib() {
        return maghrib;
    }

    public void setMaghrib(String maghrib) {
        this.maghrib = maghrib;
    }

    public String getIsha() {
        return isha;
    }

    public void setIsha(String isha) {
        this.isha = isha;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }
}
