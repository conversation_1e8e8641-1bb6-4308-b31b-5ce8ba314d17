package com.alrbea.androidapp.data.model;

import com.google.gson.annotations.SerializedName;

public class DeviceInfo {
    @SerializedName("device_id")
    private String deviceId;
    
    @SerializedName("device_model")
    private String deviceModel;
    
    @SerializedName("device_brand")
    private String deviceBrand;
    
    @SerializedName("android_version")
    private String androidVersion;
    
    @SerializedName("app_version")
    private String appVersion;
    
    @SerializedName("screen_resolution")
    private String screenResolution;

    // Constructors
    public DeviceInfo() {}

    public DeviceInfo(String deviceId, String deviceModel, String deviceBrand, 
                     String androidVersion, String appVersion, String screenResolution) {
        this.deviceId = deviceId;
        this.deviceModel = deviceModel;
        this.deviceBrand = deviceBrand;
        this.androidVersion = androidVersion;
        this.appVersion = appVersion;
        this.screenResolution = screenResolution;
    }

    // Getters and Setters
    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getDeviceBrand() {
        return deviceBrand;
    }

    public void setDeviceBrand(String deviceBrand) {
        this.deviceBrand = deviceBrand;
    }

    public String getAndroidVersion() {
        return androidVersion;
    }

    public void setAndroidVersion(String androidVersion) {
        this.androidVersion = androidVersion;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getScreenResolution() {
        return screenResolution;
    }

    public void setScreenResolution(String screenResolution) {
        this.screenResolution = screenResolution;
    }
}
