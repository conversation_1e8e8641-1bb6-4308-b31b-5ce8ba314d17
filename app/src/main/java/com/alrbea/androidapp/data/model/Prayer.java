package com.alrbea.androidapp.data.model;

public class Prayer {
    private String id;
    private String nameAr;
    private String nameEn;
    private String time;
    private int iconRes;
    private boolean isEnabled;
    
    public Prayer(String id, String nameAr, String nameEn, String time, int iconRes) {
        this.id = id;
        this.nameAr = nameAr;
        this.nameEn = nameEn;
        this.time = time;
        this.iconRes = iconRes;
        this.isEnabled = true;
    }
    
    // Getters
    public String getId() {
        return id;
    }
    
    public String getNameAr() {
        return nameAr;
    }
    
    public String getNameEn() {
        return nameEn;
    }
    
    public String getTime() {
        return time;
    }
    
    public int getIconRes() {
        return iconRes;
    }
    
    public boolean isEnabled() {
        return isEnabled;
    }
    
    // Setters
    public void setId(String id) {
        this.id = id;
    }
    
    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }
    
    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }
    
    public void setTime(String time) {
        this.time = time;
    }
    
    public void setIconRes(int iconRes) {
        this.iconRes = iconRes;
    }
    
    public void setEnabled(boolean enabled) {
        isEnabled = enabled;
    }
    
    public String getDisplayName(String language) {
        return "ar".equals(language) ? nameAr : nameEn;
    }
}
