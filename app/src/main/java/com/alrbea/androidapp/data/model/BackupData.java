package com.alrbea.androidapp.data.model;

import java.util.Date;
import java.util.List;
import java.util.Map;

public class BackupData {
    private String id;
    private String clientId;
    private String name;
    private String description;
    private Date createdAt;
    private Date updatedAt;
    private String version;
    private long size;
    private Map<String, Object> settings;
    private List<String> categories;
    private String checksum;

    public BackupData() {
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.version = "1.0";
    }

    public BackupData(String clientId, String name) {
        this();
        this.clientId = clientId;
        this.name = name;
        this.id = generateId();
    }

    private String generateId() {
        return clientId + "_" + System.currentTimeMillis();
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public Map<String, Object> getSettings() {
        return settings;
    }

    public void setSettings(Map<String, Object> settings) {
        this.settings = settings;
    }

    public List<String> getCategories() {
        return categories;
    }

    public void setCategories(List<String> categories) {
        this.categories = categories;
    }

    public String getChecksum() {
        return checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }

    public String getFormattedSize() {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }

    public int getCategoryCount() {
        return categories != null ? categories.size() : 0;
    }
}
