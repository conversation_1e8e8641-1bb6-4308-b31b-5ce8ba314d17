package com.alrbea.androidapp.data.repository;

import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.alrbea.androidapp.data.api.ApiClient;
import com.alrbea.androidapp.data.api.AuthApiService;
import com.alrbea.androidapp.data.model.DeviceInfo;
import com.alrbea.androidapp.data.model.LoginRequest;
import com.alrbea.androidapp.data.model.LoginResult;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class AuthRepository {
    private AuthApiService apiService;
    private MutableLiveData<LoginResult> loginResultLiveData;
    private MutableLiveData<String> errorLiveData;
    private MutableLiveData<Boolean> loadingLiveData;

    public AuthRepository() {
        apiService = ApiClient.getAuthApiService();
        loginResultLiveData = new MutableLiveData<>();
        errorLiveData = new MutableLiveData<>();
        loadingLiveData = new MutableLiveData<>();
    }

    public LiveData<LoginResult> getLoginResultLiveData() {
        return loginResultLiveData;
    }

    public LiveData<String> getErrorLiveData() {
        return errorLiveData;
    }

    public LiveData<Boolean> getLoadingLiveData() {
        return loadingLiveData;
    }

    public void login(String activationCode, DeviceInfo deviceInfo) {
        loadingLiveData.setValue(true);

        LoginRequest loginRequest = new LoginRequest(activationCode, deviceInfo);
        Call<LoginResult> call = apiService.login(loginRequest);
        call.enqueue(new Callback<LoginResult>() {
            @Override
            public void onResponse(Call<LoginResult> call, Response<LoginResult> response) {
                Log.d("LoginResponse", response.toString());
                loadingLiveData.setValue(false);
                if (response.isSuccessful() && response.body() != null) {
                    loginResultLiveData.setValue(response.body());
                } else {
                    LoginResult errorResult = new LoginResult();
                    errorResult.setSuccess(false);
                    errorResult.setErrorMessage("كود التفعيل غير صحيح أو منتهي الصلاحية");
                    loginResultLiveData.setValue(errorResult);
                }
            }

            @Override
            public void onFailure(Call<LoginResult> call, Throwable t) {
                Log.d("LoginResponse", t.getMessage());

                loadingLiveData.setValue(false);
                errorLiveData.setValue("خطأ في الاتصال: " + t.getMessage());
            }
        });
    }
}
