package com.alrbea.androidapp.data.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;

public class AladhanCalendarResponse {
    @SerializedName("code")
    private int code;
    
    @SerializedName("status")
    private String status;
    
    @SerializedName("data")
    private List<AladhanResponse.AladhanData> data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<AladhanResponse.AladhanData> getData() {
        return data;
    }

    public void setData(List<AladhanResponse.AladhanData> data) {
        this.data = data;
    }
} 