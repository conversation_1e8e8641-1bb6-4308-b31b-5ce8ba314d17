package com.alrbea.androidapp.data.model;

public class PrayerAzkarSetting {
    private String id;
    private String name;
    private int iconResId;
    private boolean enabled;
    private int duration; // Duration in minutes
    
    public PrayerAzkarSetting(String id, String name, int iconResId, boolean enabled, int duration) {
        this.id = id;
        this.name = name;
        this.iconResId = iconResId;
        this.enabled = enabled;
        this.duration = duration;
    }
    
    // Getters
    public String getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public int getIconResId() {
        return iconResId;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public int getDuration() {
        return duration;
    }
    
    // Setters
    public void setId(String id) {
        this.id = id;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public void setIconResId(int iconResId) {
        this.iconResId = iconResId;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public void setDuration(int duration) {
        this.duration = duration;
    }
}
