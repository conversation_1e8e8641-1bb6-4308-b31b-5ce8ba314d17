package com.alrbea.androidapp.data.model;

import java.util.ArrayList;
import java.util.List;

public class Gallery {
    private String id;
    private String name;
    private boolean enabled;
    private int displayDuration; // Duration in seconds for each image
    private int galleryDuration; // Total duration for gallery display
    private boolean enableWithAnnouncement;
    private boolean enableWithAnnouncementFriday;
    private List<String> imagePaths;
    private long createdAt;
    private long updatedAt;
    
    public Gallery() {
        this.imagePaths = new ArrayList<>();
        this.enabled = true;
        this.displayDuration = 5; // Default 5 seconds per image
        this.galleryDuration = 60; // Default 1 minute total
        this.enableWithAnnouncement = true;
        this.enableWithAnnouncementFriday = true;
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }
    
    public Gallery(String id, String name) {
        this();
        this.id = id;
        this.name = name;
    }
    
    // Getters
    public String getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public int getDisplayDuration() {
        return displayDuration;
    }
    
    public int getGalleryDuration() {
        return galleryDuration;
    }
    
    public boolean isEnableWithAnnouncement() {
        return enableWithAnnouncement;
    }
    
    public boolean isEnableWithAnnouncementFriday() {
        return enableWithAnnouncementFriday;
    }
    
    public List<String> getImagePaths() {
        return imagePaths;
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public long getUpdatedAt() {
        return updatedAt;
    }
    
    // Setters
    public void setId(String id) {
        this.id = id;
        updateTimestamp();
    }
    
    public void setName(String name) {
        this.name = name;
        updateTimestamp();
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        updateTimestamp();
    }
    
    public void setDisplayDuration(int displayDuration) {
        this.displayDuration = displayDuration;
        updateTimestamp();
    }
    
    public void setGalleryDuration(int galleryDuration) {
        this.galleryDuration = galleryDuration;
        updateTimestamp();
    }
    
    public void setEnableWithAnnouncement(boolean enableWithAnnouncement) {
        this.enableWithAnnouncement = enableWithAnnouncement;
        updateTimestamp();
    }
    
    public void setEnableWithAnnouncementFriday(boolean enableWithAnnouncementFriday) {
        this.enableWithAnnouncementFriday = enableWithAnnouncementFriday;
        updateTimestamp();
    }
    
    public void setImagePaths(List<String> imagePaths) {
        this.imagePaths = imagePaths != null ? imagePaths : new ArrayList<>();
        updateTimestamp();
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Helper methods
    public void addImagePath(String imagePath) {
        if (imagePath != null && !imagePaths.contains(imagePath)) {
            imagePaths.add(imagePath);
            updateTimestamp();
        }
    }
    
    public void removeImagePath(String imagePath) {
        if (imagePaths.remove(imagePath)) {
            updateTimestamp();
        }
    }
    
    public int getImageCount() {
        return imagePaths.size();
    }
    
    public boolean hasImages() {
        return !imagePaths.isEmpty();
    }
    
    public String getFirstImagePath() {
        return hasImages() ? imagePaths.get(0) : null;
    }
    
    private void updateTimestamp() {
        this.updatedAt = System.currentTimeMillis();
    }
    
    @Override
    public String toString() {
        return "Gallery{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", enabled=" + enabled +
                ", imageCount=" + getImageCount() +
                '}';
    }
}
