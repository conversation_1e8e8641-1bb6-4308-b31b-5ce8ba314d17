package com.alrbea.androidapp.data.api;

import com.alrbea.androidapp.data.model.PrayerTimes;
import com.alrbea.androidapp.data.model.AladhanResponse;
import com.alrbea.androidapp.data.model.AladhanCalendarResponse;
import java.util.List;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

public interface PrayerApiService {
    
    // Direct Aladhan API calls
    @GET("v1/timingsByCity")
    Call<AladhanResponse> getTodayPrayerTimes(
        @Query("city") String city,
        @Query("country") String country,
        @Query("method") int method
    );
    
    @GET("v1/timingsByCity")
    Call<AladhanResponse> getPrayerTimes(
        @Query("city") String city,
        @Query("country") String country,
        @Query("method") int method,
        @Query("date") String date
    );
    
    @GET("v1/calendarByCity")
    Call<AladhanCalendarResponse> getYearPrayerTimes(
        @Query("city") String city,
        @Query("country") String country,
        @Query("method") int method,
        @Query("year") int year
    );
    
    @GET("v1/calendarByCity")
    Call<AladhanCalendarResponse> getMonthPrayerTimes(
        @Query("city") String city,
        @Query("country") String country,
        @Query("method") int method,
        @Query("month") int month,
        @Query("year") int year
    );
}
