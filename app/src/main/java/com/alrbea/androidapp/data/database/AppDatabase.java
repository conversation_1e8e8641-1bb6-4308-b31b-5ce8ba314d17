package com.alrbea.androidapp.data.database;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.alrbea.androidapp.data.dao.PrayerTimesDao;
import com.alrbea.androidapp.data.model.PrayerTimesEntity;

@Database(entities = {PrayerTimesEntity.class}, version = 1, exportSchema = false)
public abstract class AppDatabase extends RoomDatabase {
    
    private static final String DATABASE_NAME = "prayer_app_database";
    private static AppDatabase instance;
    
    public abstract PrayerTimesDao prayerTimesDao();
    
    public static synchronized AppDatabase getInstance(Context context) {
        if (instance == null) {
            instance = Room.databaseBuilder(
                    context.getApplicationContext(),
                    AppDatabase.class,
                    DATABASE_NAME)
                    .fallbackToDestructiveMigration()
                    .build();
        }
        return instance;
    }
} 