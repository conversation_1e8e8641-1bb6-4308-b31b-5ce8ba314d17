package com.alrbea.androidapp.data.model;

import com.google.gson.annotations.SerializedName;

public class LoginRequest {
    @SerializedName("activation_code")
    private String activationCode;
    
    @SerializedName("device_info")
    private DeviceInfo deviceInfo;

    public LoginRequest(String activationCode, DeviceInfo deviceInfo) {
        this.activationCode = activationCode;
        this.deviceInfo = deviceInfo;
    }

    // Getters and Setters
    public String getActivationCode() {
        return activationCode;
    }

    public void setActivationCode(String activationCode) {
        this.activationCode = activationCode;
    }

    public DeviceInfo getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(DeviceInfo deviceInfo) {
        this.deviceInfo = deviceInfo;
    }
}
