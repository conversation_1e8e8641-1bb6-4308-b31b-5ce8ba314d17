package com.alrbea.androidapp.data.model;

import java.util.List;

public class Country {
    private String code;
    private String nameAr;
    private String nameEn;
    private List<City> cities;

    public Country(String code, String nameAr, String nameEn, List<City> cities) {
        this.code = code;
        this.nameAr = nameAr;
        this.nameEn = nameEn;
        this.cities = cities;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public List<City> getCities() {
        return cities;
    }

    public void setCities(List<City> cities) {
        this.cities = cities;
    }

    public String getDisplayName(String language) {
        return "ar".equals(language) ? nameAr : nameEn;
    }
}
