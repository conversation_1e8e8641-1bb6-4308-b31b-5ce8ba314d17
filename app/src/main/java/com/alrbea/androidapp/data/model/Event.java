package com.alrbea.androidapp.data.model;

import java.util.UUID;

public class Event {
    private String id;
    private String title;
    private String message;
    private String prayer;
    private String startDate;
    private String endDate;
    private boolean isEnabled;
    private long createdAt;
    private long updatedAt;
    
    public Event() {
        this.id = UUID.randomUUID().toString();
        this.isEnabled = true;
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }
    
    public Event(String title, String message, String prayer) {
        this();
        this.title = title;
        this.message = message;
        this.prayer = prayer;
    }
    
    public Event(String title, String message, String prayer, String startDate, String endDate) {
        this(title, message, prayer);
        this.startDate = startDate;
        this.endDate = endDate;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public String getPrayer() {
        return prayer;
    }
    
    public void setPrayer(String prayer) {
        this.prayer = prayer;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public String getStartDate() {
        return startDate;
    }
    
    public void setStartDate(String startDate) {
        this.startDate = startDate;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public String getEndDate() {
        return endDate;
    }
    
    public void setEndDate(String endDate) {
        this.endDate = endDate;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public boolean isEnabled() {
        return isEnabled;
    }
    
    public void setEnabled(boolean enabled) {
        isEnabled = enabled;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public long getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "Event{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", message='" + message + '\'' +
                ", prayer='" + prayer + '\'' +
                ", isEnabled=" + isEnabled +
                '}';
    }
}
