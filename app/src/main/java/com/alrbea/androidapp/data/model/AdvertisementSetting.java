package com.alrbea.androidapp.data.model;

import java.util.List;

public class AdvertisementSetting {
    
    public enum SettingType {
        SIMPLE_TOGGLE,
        EXPANDABLE_TOGGLE,
        TEXT_INPUT,
        TIME_PICKER,
        PRAYER_SELECTOR,
        DATE_RANGE
    }
    
    private String id;
    private String titleAr;
    private String titleEn;
    private String descriptionAr;
    private String descriptionEn;
    private int iconRes;
    private SettingType type;
    private boolean isEnabled;
    private boolean isExpanded;
    private List<SubSetting> subSettings;
    
    public AdvertisementSetting(String id, String titleAr, String titleEn, 
                               String descriptionAr, String descriptionEn, 
                               int iconRes, SettingType type) {
        this.id = id;
        this.titleAr = titleAr;
        this.titleEn = titleEn;
        this.descriptionAr = descriptionAr;
        this.descriptionEn = descriptionEn;
        this.iconRes = iconRes;
        this.type = type;
        this.isEnabled = false;
        this.isExpanded = false;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getTitleAr() {
        return titleAr;
    }
    
    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }
    
    public String getTitleEn() {
        return titleEn;
    }
    
    public void setTitleEn(String titleEn) {
        this.titleEn = titleEn;
    }
    
    public String getDescriptionAr() {
        return descriptionAr;
    }
    
    public void setDescriptionAr(String descriptionAr) {
        this.descriptionAr = descriptionAr;
    }
    
    public String getDescriptionEn() {
        return descriptionEn;
    }
    
    public void setDescriptionEn(String descriptionEn) {
        this.descriptionEn = descriptionEn;
    }
    
    public int getIconRes() {
        return iconRes;
    }
    
    public void setIconRes(int iconRes) {
        this.iconRes = iconRes;
    }
    
    public SettingType getType() {
        return type;
    }
    
    public void setType(SettingType type) {
        this.type = type;
    }
    
    public boolean isEnabled() {
        return isEnabled;
    }
    
    public void setEnabled(boolean enabled) {
        isEnabled = enabled;
    }
    
    public boolean isExpanded() {
        return isExpanded;
    }
    
    public void setExpanded(boolean expanded) {
        isExpanded = expanded;
    }
    
    public List<SubSetting> getSubSettings() {
        return subSettings;
    }
    
    public void setSubSettings(List<SubSetting> subSettings) {
        this.subSettings = subSettings;
    }
    
    public String getDisplayTitle(String language) {
        return "ar".equals(language) ? titleAr : titleEn;
    }
    
    public String getDisplayDescription(String language) {
        return "ar".equals(language) ? descriptionAr : descriptionEn;
    }
    
    // SubSetting inner class
    public static class SubSetting {
        private String id;
        private String titleAr;
        private String titleEn;
        private SettingType type;
        private String value;
        private String defaultValue;
        
        public SubSetting(String id, String titleAr, String titleEn, SettingType type) {
            this.id = id;
            this.titleAr = titleAr;
            this.titleEn = titleEn;
            this.type = type;
        }
        
        // Getters and Setters for SubSetting
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getTitleAr() {
            return titleAr;
        }
        
        public void setTitleAr(String titleAr) {
            this.titleAr = titleAr;
        }
        
        public String getTitleEn() {
            return titleEn;
        }
        
        public void setTitleEn(String titleEn) {
            this.titleEn = titleEn;
        }
        
        public SettingType getType() {
            return type;
        }
        
        public void setType(SettingType type) {
            this.type = type;
        }
        
        public String getValue() {
            return value;
        }
        
        public void setValue(String value) {
            this.value = value;
        }
        
        public String getDefaultValue() {
            return defaultValue;
        }
        
        public void setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
        }
        
        public String getDisplayTitle(String language) {
            return "ar".equals(language) ? titleAr : titleEn;
        }
    }
}
