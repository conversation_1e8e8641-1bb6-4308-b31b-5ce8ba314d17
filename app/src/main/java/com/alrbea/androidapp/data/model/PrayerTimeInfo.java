package com.alrbea.androidapp.data.model;

import java.util.Date;

public class PrayerTimeInfo {
    private String prayerName;
    private String prayerTime;
    private String iqamaTime;
    private int timeUntilPrayer; // in seconds
    private int timeUntilIqama; // in seconds
    private boolean isNextPrayer;
    private boolean isCurrentPrayer;
    private String azkarAfterPrayer;
    private String azkarAtAdhan;
    private Date gregorianDate;
    private String hijriDate;
    private String dayOfWeek;
    private String currentTime;
    private String timeZone;

    public PrayerTimeInfo() {}

    public PrayerTimeInfo(String prayerName, String prayerTime) {
        this.prayerName = prayerName;
        this.prayerTime = prayerTime;
    }

    // Getters and Setters
    public String getPrayerName() {
        return prayerName;
    }

    public void setPrayerName(String prayerName) {
        this.prayerName = prayerName;
    }

    public String getPrayerTime() {
        return prayerTime;
    }

    public void setPrayerTime(String prayerTime) {
        this.prayerTime = prayerTime;
    }

    public String getIqamaTime() {
        return iqamaTime;
    }

    public void setIqamaTime(String iqamaTime) {
        this.iqamaTime = iqamaTime;
    }

    public int getTimeUntilPrayer() {
        return timeUntilPrayer;
    }

    public void setTimeUntilPrayer(int timeUntilPrayer) {
        this.timeUntilPrayer = timeUntilPrayer;
    }

    public int getTimeUntilIqama() {
        return timeUntilIqama;
    }

    public void setTimeUntilIqama(int timeUntilIqama) {
        this.timeUntilIqama = timeUntilIqama;
    }

    public boolean isNextPrayer() {
        return isNextPrayer;
    }

    public void setNextPrayer(boolean nextPrayer) {
        isNextPrayer = nextPrayer;
    }

    public boolean isCurrentPrayer() {
        return isCurrentPrayer;
    }

    public void setCurrentPrayer(boolean currentPrayer) {
        isCurrentPrayer = currentPrayer;
    }

    public String getAzkarAfterPrayer() {
        return azkarAfterPrayer;
    }

    public void setAzkarAfterPrayer(String azkarAfterPrayer) {
        this.azkarAfterPrayer = azkarAfterPrayer;
    }

    public String getAzkarAtAdhan() {
        return azkarAtAdhan;
    }

    public void setAzkarAtAdhan(String azkarAtAdhan) {
        this.azkarAtAdhan = azkarAtAdhan;
    }

    public Date getGregorianDate() {
        return gregorianDate;
    }

    public void setGregorianDate(Date gregorianDate) {
        this.gregorianDate = gregorianDate;
    }

    public String getHijriDate() {
        return hijriDate;
    }

    public void setHijriDate(String hijriDate) {
        this.hijriDate = hijriDate;
    }

    public String getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(String dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public String getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(String currentTime) {
        this.currentTime = currentTime;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    // Helper methods
    public String getFormattedTimeUntilPrayer() {
        if (timeUntilPrayer <= 0) {
            return "حان وقت الصلاة";
        }
        
        int hours = timeUntilPrayer / 3600;
        int minutes = (timeUntilPrayer % 3600) / 60;
        int seconds = timeUntilPrayer % 60;
        
        if (hours > 0) {
            return String.format("%d ساعة %d دقيقة %d ثانية", hours, minutes, seconds);
        } else if (minutes > 0) {
            return String.format("%d دقيقة %d ثانية", minutes, seconds);
        } else {
            return String.format("%d ثانية", seconds);
        }
    }

    public String getFormattedTimeUntilIqama() {
        if (timeUntilIqama <= 0) {
            return "حان وقت الإقامة";
        }
        
        int minutes = timeUntilIqama / 60;
        int seconds = timeUntilIqama % 60;
        
        return String.format("%d دقيقة %d ثانية", minutes, seconds);
    }
} 