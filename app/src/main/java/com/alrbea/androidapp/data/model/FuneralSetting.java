package com.alrbea.androidapp.data.model;

public class FuneralSetting {
    
    public enum SettingType {
        TOGGLE,
        TEXT_TEMPLATE,
        PRAYER_SELECTOR,
        NUMBER_INPUT,
        TIME_PICKER,
        SELECTION
    }
    
    private String id;
    private String titleAr;
    private String titleEn;
    private String descriptionAr;
    private String descriptionEn;
    private int iconRes;
    private SettingType type;
    private boolean isEnabled;
    private String value;
    private String defaultValue;
    private String[] options;
    
    public FuneralSetting(String id, String titleAr, String titleEn, 
                         String descriptionAr, String descriptionEn, 
                         int iconRes, SettingType type) {
        this.id = id;
        this.titleAr = titleAr;
        this.titleEn = titleEn;
        this.descriptionAr = descriptionAr;
        this.descriptionEn = descriptionEn;
        this.iconRes = iconRes;
        this.type = type;
        this.isEnabled = false;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getTitleAr() {
        return titleAr;
    }
    
    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }
    
    public String getTitleEn() {
        return titleEn;
    }
    
    public void setTitleEn(String titleEn) {
        this.titleEn = titleEn;
    }
    
    public String getDescriptionAr() {
        return descriptionAr;
    }
    
    public void setDescriptionAr(String descriptionAr) {
        this.descriptionAr = descriptionAr;
    }
    
    public String getDescriptionEn() {
        return descriptionEn;
    }
    
    public void setDescriptionEn(String descriptionEn) {
        this.descriptionEn = descriptionEn;
    }
    
    public int getIconRes() {
        return iconRes;
    }
    
    public void setIconRes(int iconRes) {
        this.iconRes = iconRes;
    }
    
    public SettingType getType() {
        return type;
    }
    
    public void setType(SettingType type) {
        this.type = type;
    }
    
    public boolean isEnabled() {
        return isEnabled;
    }
    
    public void setEnabled(boolean enabled) {
        isEnabled = enabled;
    }
    
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
    }
    
    public String getDefaultValue() {
        return defaultValue;
    }
    
    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }
    
    public String[] getOptions() {
        return options;
    }
    
    public void setOptions(String[] options) {
        this.options = options;
    }
    
    public String getDisplayTitle(String language) {
        return "ar".equals(language) ? titleAr : titleEn;
    }
    
    public String getDisplayDescription(String language) {
        return "ar".equals(language) ? descriptionAr : descriptionEn;
    }
}
