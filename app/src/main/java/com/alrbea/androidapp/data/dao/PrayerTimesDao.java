package com.alrbea.androidapp.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.alrbea.androidapp.data.model.PrayerTimesEntity;

import java.util.List;

@Dao
public interface PrayerTimesDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(PrayerTimesEntity prayerTime);
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<PrayerTimesEntity> prayerTimes);
    
    @Update
    void update(PrayerTimesEntity prayerTime);
    
    @Delete
    void delete(PrayerTimesEntity prayerTime);
    
    @Query("DELETE FROM prayer_times")
    void deleteAll();
    
    @Query("SELECT * FROM prayer_times WHERE country = :country AND city = :city AND method = :method AND date = :date LIMIT 1")
    LiveData<PrayerTimesEntity> getPrayerTime(String country, String city, String method, String date);
    
    @Query("SELECT * FROM prayer_times WHERE country = :country AND city = :city AND method = :method AND date = :date LIMIT 1")
    PrayerTimesEntity getPrayerTimeSync(String country, String city, String method, String date);
    
    @Query("SELECT * FROM prayer_times WHERE country = :country AND city = :city AND method = :method ORDER BY date ASC")
    LiveData<List<PrayerTimesEntity>> getAllPrayerTimes(String country, String city, String method);
    
    @Query("SELECT * FROM prayer_times WHERE country = :country AND city = :city AND method = :method ORDER BY date ASC")
    List<PrayerTimesEntity> getAllPrayerTimesSync(String country, String city, String method);
    
    @Query("SELECT * FROM prayer_times WHERE country = :country AND city = :city AND method = :method AND date >= :startDate AND date <= :endDate ORDER BY date ASC")
    LiveData<List<PrayerTimesEntity>> getPrayerTimesByDateRange(String country, String city, String method, String startDate, String endDate);
    
    @Query("SELECT * FROM prayer_times WHERE country = :country AND city = :city AND method = :method AND date >= :startDate AND date <= :endDate ORDER BY date ASC")
    List<PrayerTimesEntity> getPrayerTimesByDateRangeSync(String country, String city, String method, String startDate, String endDate);
    
    @Query("SELECT * FROM prayer_times WHERE country = :country AND city = :city AND method = :method AND strftime('%Y-%m', date) = :yearMonth ORDER BY date ASC")
    LiveData<List<PrayerTimesEntity>> getPrayerTimesByMonth(String country, String city, String method, String yearMonth);
    
    @Query("SELECT * FROM prayer_times WHERE country = :country AND city = :city AND method = :method AND strftime('%Y', date) = :year ORDER BY date ASC")
    LiveData<List<PrayerTimesEntity>> getPrayerTimesByYear(String country, String city, String method, String year);
    
    @Query("SELECT * FROM prayer_times WHERE date = :date ORDER BY timestamp DESC LIMIT 1")
    LiveData<PrayerTimesEntity> getTodayPrayerTime(String date);
    
    @Query("SELECT * FROM prayer_times WHERE date = :date ORDER BY timestamp DESC LIMIT 1")
    PrayerTimesEntity getTodayPrayerTimeSync(String date);
    
    @Query("SELECT COUNT(*) FROM prayer_times WHERE country = :country AND city = :city AND method = :method")
    int getPrayerTimesCount(String country, String city, String method);
    
    @Query("SELECT * FROM prayer_times ORDER BY timestamp DESC LIMIT 1")
    LiveData<PrayerTimesEntity> getLatestPrayerTime();
} 