package com.alrbea.androidapp.data.model;

public class CalculationMethod {
    private String code;
    private String nameAr;
    private String nameEn;
    private String description;
    private boolean isAutomatic;

    public CalculationMethod(String code, String nameAr, String nameEn, String description, boolean isAutomatic) {
        this.code = code;
        this.nameAr = nameAr;
        this.nameEn = nameEn;
        this.description = description;
        this.isAutomatic = isAutomatic;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isAutomatic() {
        return isAutomatic;
    }

    public void setAutomatic(boolean automatic) {
        isAutomatic = automatic;
    }

    public String getDisplayName(String language) {
        return "ar".equals(language) ? nameAr : nameEn;
    }
}
