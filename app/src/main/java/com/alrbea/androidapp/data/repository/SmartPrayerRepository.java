package com.alrbea.androidapp.data.repository;

import android.content.Context;
import android.util.Log;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.alrbea.androidapp.data.api.ApiClient;
import com.alrbea.androidapp.data.api.PrayerApiService;
import com.alrbea.androidapp.data.model.PrayerTimes;
import com.alrbea.androidapp.data.model.AladhanResponse;
import com.alrbea.androidapp.data.model.AladhanCalendarResponse;
import com.alrbea.androidapp.data.database.AppDatabase;
import com.alrbea.androidapp.data.dao.PrayerTimesDao;
import com.alrbea.androidapp.data.model.PrayerTimesEntity;
import com.alrbea.androidapp.utils.AladhanConverter;
import com.alrbea.androidapp.utils.PrayerTimesConverter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SmartPrayerRepository {
    private PrayerApiService apiService;
    private PrayerTimesDao prayerTimesDao;
    private ExecutorService executorService;
    private MutableLiveData<List<PrayerTimes>> prayerTimesLiveData;
    private MutableLiveData<PrayerTimes> todayPrayerTimesLiveData;
    private MutableLiveData<String> errorLiveData;
    private MutableLiveData<Boolean> loadingLiveData;
    private MutableLiveData<Boolean> needsRefreshLiveData;

    public SmartPrayerRepository(Context context) {
        apiService = ApiClient.getPrayerApiService();
        AppDatabase database = AppDatabase.getInstance(context);
        prayerTimesDao = database.prayerTimesDao();
        executorService = Executors.newFixedThreadPool(4);
        
        prayerTimesLiveData = new MutableLiveData<>();
        todayPrayerTimesLiveData = new MutableLiveData<>();
        errorLiveData = new MutableLiveData<>();
        loadingLiveData = new MutableLiveData<>();
        needsRefreshLiveData = new MutableLiveData<>();
    }

    public LiveData<List<PrayerTimes>> getPrayerTimesLiveData() {
        return prayerTimesLiveData;
    }

    public LiveData<PrayerTimes> getTodayPrayerTimesLiveData() {
        return todayPrayerTimesLiveData;
    }

    public LiveData<String> getErrorLiveData() {
        return errorLiveData;
    }

    public LiveData<Boolean> getLoadingLiveData() {
        return loadingLiveData;
    }

    public LiveData<Boolean> getNeedsRefreshLiveData() {
        return needsRefreshLiveData;
    }

    public void loadTodayPrayerTimes(String city) {
        loadingLiveData.setValue(true);
        
        // First check if we have data for today
        String today = getTodayDate();
        int currentYear = getCurrentYear();
        
        executorService.execute(() -> {
            // Check if we have today's data in database
            PrayerTimesEntity todayEntity = prayerTimesDao.getTodayPrayerTimeSync(today);
            
            if (todayEntity != null) {
                // We have today's data, show it immediately
                PrayerTimes todayPrayerTimes = PrayerTimesConverter.entityToApi(todayEntity);
                todayPrayerTimesLiveData.postValue(todayPrayerTimes);
                
                List<PrayerTimes> singleItemList = new ArrayList<>();
                singleItemList.add(todayPrayerTimes);
                prayerTimesLiveData.postValue(singleItemList);
                loadingLiveData.postValue(false);
                
                // Check if we need to fetch next year's data
                checkAndFetchNextYearData(city, currentYear);
            } else {
                // No data for today, check if we have data for current year
                int count = prayerTimesDao.getPrayerTimesCount("Saudi Arabia", city, "umm_al_qura");
                
                if (count > 0) {
                    // We have some data but not today's, fetch today's data
                    fetchTodayData(city);
                } else {
                    // No data at all, fetch entire year
                    fetchYearData(city, currentYear);
                }
            }
        });
    }

    private void checkAndFetchNextYearData(String city, int currentYear) {
        int nextYear = currentYear + 1;
        int nextYearCount = prayerTimesDao.getPrayerTimesCount("Saudi Arabia", city, "umm_al_qura");
        
        // If we don't have next year's data and we're in December, fetch it
        Calendar cal = Calendar.getInstance();
        int currentMonth = cal.get(Calendar.MONTH) + 1; // January is 0
        
        if (currentMonth >= 12 && nextYearCount == 0) {
            Log.d("SmartPrayerRepository", "Fetching next year's data: " + nextYear);
            fetchYearData(city, nextYear);
        }
    }

    private void fetchTodayData(String city) {
        Call<AladhanResponse> call = apiService.getTodayPrayerTimes(city, "Saudi Arabia", 4);
        call.enqueue(new Callback<AladhanResponse>() {
            @Override
            public void onResponse(Call<AladhanResponse> call, Response<AladhanResponse> response) {
                loadingLiveData.setValue(false);
                
                if (response.isSuccessful() && response.body() != null) {
                    AladhanResponse aladhanResponse = response.body();
                    
                    if (aladhanResponse.getCode() == 200 && aladhanResponse.getData() != null) {
                        PrayerTimes prayerTimes = AladhanConverter.aladhanToPrayerTimes(aladhanResponse, city);
                        if (prayerTimes != null) {
                            // Save to database
                            PrayerTimesEntity entity = PrayerTimesConverter.apiToEntity(prayerTimes, "Saudi Arabia", "umm_al_qura");
                            executorService.execute(() -> prayerTimesDao.insert(entity));
                            
                            // Update UI
                            todayPrayerTimesLiveData.setValue(prayerTimes);
                            List<PrayerTimes> singleItemList = new ArrayList<>();
                            singleItemList.add(prayerTimes);
                            prayerTimesLiveData.setValue(singleItemList);
                        }
                    } else {
                        errorLiveData.setValue("API returned error: " + aladhanResponse.getStatus());
                        needsRefreshLiveData.setValue(true);
                    }
                } else {
                    errorLiveData.setValue("Failed to fetch today's prayer times");
                    needsRefreshLiveData.setValue(true);
                }
            }

            @Override
            public void onFailure(Call<AladhanResponse> call, Throwable t) {
                loadingLiveData.setValue(false);
                errorLiveData.setValue("Network error: " + t.getMessage());
                needsRefreshLiveData.setValue(true);
            }
        });
    }

    private void fetchYearData(String city, int year) {
        Log.d("SmartPrayerRepository", "Fetching year data for: " + year);
        
        Call<AladhanCalendarResponse> call = apiService.getYearPrayerTimes(city, "Saudi Arabia", 4, year);
        call.enqueue(new Callback<AladhanCalendarResponse>() {
            @Override
            public void onResponse(Call<AladhanCalendarResponse> call, Response<AladhanCalendarResponse> response) {
                loadingLiveData.setValue(false);
                
                if (response.isSuccessful() && response.body() != null) {
                    AladhanCalendarResponse calendarResponse = response.body();
                    
                    if (calendarResponse.getCode() == 200 && calendarResponse.getData() != null) {
                        List<PrayerTimes> prayerTimesList = AladhanConverter.calendarToPrayerTimesList(calendarResponse, city);
                        
                        // Save to database
                        List<PrayerTimesEntity> entities = PrayerTimesConverter.apiListToEntityList(prayerTimesList, "Saudi Arabia", "umm_al_qura");
                        executorService.execute(() -> prayerTimesDao.insertAll(entities));
                        
                        // Find today's data and show it
                        String today = getTodayDate();
                        PrayerTimes todayPrayerTimes = findTodayPrayerTimes(prayerTimesList, today);
                        
                        if (todayPrayerTimes != null) {
                            todayPrayerTimesLiveData.setValue(todayPrayerTimes);
                            List<PrayerTimes> singleItemList = new ArrayList<>();
                            singleItemList.add(todayPrayerTimes);
                            prayerTimesLiveData.setValue(singleItemList);
                        } else {
                            // Show first available data
                            if (!prayerTimesList.isEmpty()) {
                                todayPrayerTimesLiveData.setValue(prayerTimesList.get(0));
                                prayerTimesLiveData.setValue(prayerTimesList);
                            }
                        }
                    } else {
                        errorLiveData.setValue("API returned error: " + calendarResponse.getStatus());
                        needsRefreshLiveData.setValue(true);
                    }
                } else {
                    errorLiveData.setValue("Failed to fetch year data");
                    needsRefreshLiveData.setValue(true);
                }
            }

            @Override
            public void onFailure(Call<AladhanCalendarResponse> call, Throwable t) {
                loadingLiveData.setValue(false);
                errorLiveData.setValue("Network error: " + t.getMessage());
                needsRefreshLiveData.setValue(true);
            }
        });
    }

    public void refreshData(String city) {
        // Clear database and fetch fresh data
        executorService.execute(() -> {
            prayerTimesDao.deleteAll();
            fetchYearData(city, getCurrentYear());
        });
    }

    private PrayerTimes findTodayPrayerTimes(List<PrayerTimes> prayerTimesList, String today) {
        for (PrayerTimes prayerTimes : prayerTimesList) {
            if (today.equals(prayerTimes.getDate())) {
                return prayerTimes;
            }
        }
        return null;
    }

    private String getTodayDate() {
        Calendar cal = Calendar.getInstance();
        int day = cal.get(Calendar.DAY_OF_MONTH);
        int month = cal.get(Calendar.MONTH) + 1; // January is 0
        int year = cal.get(Calendar.YEAR);
        return String.format("%02d-%02d-%04d", day, month, year);
    }

    private int getCurrentYear() {
        return Calendar.getInstance().get(Calendar.YEAR);
    }

    public void shutdown() {
        executorService.shutdown();
    }
} 