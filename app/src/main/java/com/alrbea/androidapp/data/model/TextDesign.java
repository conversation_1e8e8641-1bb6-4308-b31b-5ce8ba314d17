package com.alrbea.androidapp.data.model;

import android.graphics.Color;

public class TextDesign {
    private String id;
    private String name;
    private String text;
    private int textColor;
    private int backgroundColor;
    private float textSize;
    private String fontFamily;
    private int textStyle; // Typeface.NORMAL, BOLD, ITALIC, etc.
    private int lineCount;
    private boolean showInPrayers;
    private boolean enabled;
    
    // Position coordinates for multiple lines
    private float line1X;
    private float line1Y;
    private float line2X;
    private float line2Y;
    private float line3X;
    private float line3Y;
    
    // Timestamps
    private long createdAt;
    private long updatedAt;
    
    public TextDesign() {
        this.textColor = Color.BLACK;
        this.backgroundColor = Color.TRANSPARENT;
        this.textSize = 16f;
        this.fontFamily = "default";
        this.textStyle = 0; // Typeface.NORMAL
        this.lineCount = 1;
        this.showInPrayers = true;
        this.enabled = true;
        this.line1X = 150f;
        this.line1Y = 100f;
        this.line2X = 180f;
        this.line2Y = 170f;
        this.line3X = 200f;
        this.line3Y = 240f;
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }
    
    public TextDesign(String id, String name) {
        this();
        this.id = id;
        this.name = name;
    }
    
    public TextDesign(String id, String name, String text) {
        this(id, name);
        this.text = text;
    }
    
    // Getters
    public String getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public String getText() {
        return text;
    }
    
    public int getTextColor() {
        return textColor;
    }
    
    public int getBackgroundColor() {
        return backgroundColor;
    }
    
    public float getTextSize() {
        return textSize;
    }
    
    public String getFontFamily() {
        return fontFamily;
    }
    
    public int getTextStyle() {
        return textStyle;
    }
    
    public int getLineCount() {
        return lineCount;
    }
    
    public boolean isShowInPrayers() {
        return showInPrayers;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public float getLine1X() {
        return line1X;
    }
    
    public float getLine1Y() {
        return line1Y;
    }
    
    public float getLine2X() {
        return line2X;
    }
    
    public float getLine2Y() {
        return line2Y;
    }
    
    public float getLine3X() {
        return line3X;
    }
    
    public float getLine3Y() {
        return line3Y;
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public long getUpdatedAt() {
        return updatedAt;
    }
    
    // Setters
    public void setId(String id) {
        this.id = id;
        updateTimestamp();
    }
    
    public void setName(String name) {
        this.name = name;
        updateTimestamp();
    }
    
    public void setText(String text) {
        this.text = text;
        updateTimestamp();
    }
    
    public void setTextColor(int textColor) {
        this.textColor = textColor;
        updateTimestamp();
    }
    
    public void setBackgroundColor(int backgroundColor) {
        this.backgroundColor = backgroundColor;
        updateTimestamp();
    }
    
    public void setTextSize(float textSize) {
        this.textSize = textSize;
        updateTimestamp();
    }
    
    public void setFontFamily(String fontFamily) {
        this.fontFamily = fontFamily;
        updateTimestamp();
    }
    
    public void setTextStyle(int textStyle) {
        this.textStyle = textStyle;
        updateTimestamp();
    }
    
    public void setLineCount(int lineCount) {
        this.lineCount = lineCount;
        updateTimestamp();
    }
    
    public void setShowInPrayers(boolean showInPrayers) {
        this.showInPrayers = showInPrayers;
        updateTimestamp();
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        updateTimestamp();
    }
    
    public void setLine1X(float line1X) {
        this.line1X = line1X;
        updateTimestamp();
    }
    
    public void setLine1Y(float line1Y) {
        this.line1Y = line1Y;
        updateTimestamp();
    }
    
    public void setLine2X(float line2X) {
        this.line2X = line2X;
        updateTimestamp();
    }
    
    public void setLine2Y(float line2Y) {
        this.line2Y = line2Y;
        updateTimestamp();
    }
    
    public void setLine3X(float line3X) {
        this.line3X = line3X;
        updateTimestamp();
    }
    
    public void setLine3Y(float line3Y) {
        this.line3Y = line3Y;
        updateTimestamp();
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Helper methods
    public void setPosition(int lineNumber, float x, float y) {
        switch (lineNumber) {
            case 1:
                setLine1X(x);
                setLine1Y(y);
                break;
            case 2:
                setLine2X(x);
                setLine2Y(y);
                break;
            case 3:
                setLine3X(x);
                setLine3Y(y);
                break;
        }
    }
    
    public float[] getPosition(int lineNumber) {
        switch (lineNumber) {
            case 1:
                return new float[]{line1X, line1Y};
            case 2:
                return new float[]{line2X, line2Y};
            case 3:
                return new float[]{line3X, line3Y};
            default:
                return new float[]{0f, 0f};
        }
    }
    
    public String getTextColorHex() {
        return String.format("#%06X", (0xFFFFFF & textColor));
    }
    
    public String getBackgroundColorHex() {
        return String.format("#%06X", (0xFFFFFF & backgroundColor));
    }
    
    private void updateTimestamp() {
        this.updatedAt = System.currentTimeMillis();
    }
    
    @Override
    public String toString() {
        return "TextDesign{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", text='" + text + '\'' +
                ", enabled=" + enabled +
                '}';
    }
    
    // Create a copy of this design
    public TextDesign copy() {
        TextDesign copy = new TextDesign(this.id + "_copy", this.name + " (نسخة)");
        copy.setText(this.text);
        copy.setTextColor(this.textColor);
        copy.setBackgroundColor(this.backgroundColor);
        copy.setTextSize(this.textSize);
        copy.setFontFamily(this.fontFamily);
        copy.setTextStyle(this.textStyle);
        copy.setLineCount(this.lineCount);
        copy.setShowInPrayers(this.showInPrayers);
        copy.setEnabled(this.enabled);
        copy.setLine1X(this.line1X);
        copy.setLine1Y(this.line1Y);
        copy.setLine2X(this.line2X);
        copy.setLine2Y(this.line2Y);
        copy.setLine3X(this.line3X);
        copy.setLine3Y(this.line3Y);
        return copy;
    }
}
