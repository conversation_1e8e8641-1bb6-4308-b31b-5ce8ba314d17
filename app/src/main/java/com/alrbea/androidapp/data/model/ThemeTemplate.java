package com.alrbea.androidapp.data.model;

// Inner class for theme data
public  class ThemeTemplate {
    public enum ThemeType {
        LOCAL, API, CUSTOM
    }

    private String id;
    private String name;
    private int landscapeImage;
    private int landscapeView;
    private int portraitImage;
    private int portraitView;
    private boolean supportsIqama;
    private ThemeType type;
    private boolean isDownloaded;

    public ThemeTemplate(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public ThemeTemplate(String id, String name, int landscapeImage, int landscapeView, int portraitImage, int portraitView, boolean supportsIqama, ThemeType type, boolean isDownloaded) {
        this.id = id;
        this.name = name;
        this.landscapeImage = landscapeImage;
        this.landscapeView = landscapeView;
        this.portraitImage = portraitImage;
        this.portraitView = portraitView;
        this.supportsIqama = supportsIqama;
        this.type = type;
        this.isDownloaded = isDownloaded;
    }

// Getters and setters

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getLandscapeImage() {
        return landscapeImage;
    }

    public void setLandscapeImage(int landscapeImage) {
        this.landscapeImage = landscapeImage;
    }

    public int getLandscapeView() {
        return landscapeView;
    }

    public void setLandscapeView(int landscapeView) {
        this.landscapeView = landscapeView;
    }

    public int getPortraitImage() {
        return portraitImage;
    }

    public void setPortraitImage(int portraitImage) {
        this.portraitImage = portraitImage;
    }

    public int getPortraitView() {
        return portraitView;
    }

    public void setPortraitView(int portraitView) {
        this.portraitView = portraitView;
    }

    public boolean isSupportsIqama() {
        return supportsIqama;
    }

    public void setSupportsIqama(boolean supportsIqama) {
        this.supportsIqama = supportsIqama;
    }

    public ThemeType getType() {
        return type;
    }

    public void setType(ThemeType type) {
        this.type = type;
    }

    public boolean isDownloaded() {
        return isDownloaded;
    }

    public void setDownloaded(boolean downloaded) {
        isDownloaded = downloaded;
    }
}