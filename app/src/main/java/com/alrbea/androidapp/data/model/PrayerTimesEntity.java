package com.alrbea.androidapp.data.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "prayer_times")
public class PrayerTimesEntity {
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    private String country;
    private String city;
    private String method;
    private String date;
    private String fajr;
    private String sunrise;
    private String dhuhr;
    private String asr;
    private String maghrib;
    private String isha;
    private long timestamp;

    public PrayerTimesEntity() {}

    public PrayerTimesEntity(String country, String city, String method, String date,
                           String fajr, String sunrise, String dhuhr, String asr,
                           String maghrib, String isha) {
        this.country = country;
        this.city = city;
        this.method = method;
        this.date = date;
        this.fajr = fajr;
        this.sunrise = sunrise;
        this.dhuhr = dhuhr;
        this.asr = asr;
        this.maghrib = maghrib;
        this.isha = isha;
        this.timestamp = System.currentTimeMillis();
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }

    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }

    public String getMethod() { return method; }
    public void setMethod(String method) { this.method = method; }

    public String getDate() { return date; }
    public void setDate(String date) { this.date = date; }

    public String getFajr() { return fajr; }
    public void setFajr(String fajr) { this.fajr = fajr; }

    public String getSunrise() { return sunrise; }
    public void setSunrise(String sunrise) { this.sunrise = sunrise; }

    public String getDhuhr() { return dhuhr; }
    public void setDhuhr(String dhuhr) { this.dhuhr = dhuhr; }

    public String getAsr() { return asr; }
    public void setAsr(String asr) { this.asr = asr; }

    public String getMaghrib() { return maghrib; }
    public void setMaghrib(String maghrib) { this.maghrib = maghrib; }

    public String getIsha() { return isha; }
    public void setIsha(String isha) { this.isha = isha; }

    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
} 