package com.alrbea.androidapp;

import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.util.Log;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.alrbea.androidapp.data.model.ThemeTemplate;
import com.alrbea.androidapp.databinding.ContentMainBlueBinding;
import com.alrbea.androidapp.ui.BaseActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;

import com.alrbea.androidapp.ui.SettingsActivity;
import com.alrbea.androidapp.ui.adapter.PrayerTimesAdapter;
import com.alrbea.androidapp.ui.viewmodel.PrayerViewModel;
import com.alrbea.androidapp.ui.viewmodel.PrayerViewModelFactory;
import com.alrbea.androidapp.ui.viewmodel.ThemeViewModel;
import com.alrbea.androidapp.utils.AdhanNotificationManager;
import com.alrbea.androidapp.utils.LiveTimeManager;
import com.alrbea.androidapp.utils.NewsTickerManager;
import com.alrbea.androidapp.utils.ResponsiveLayoutManager;
import com.alrbea.androidapp.utils.ResponsiveItemDecoration;
import com.alrbea.androidapp.utils.ScreenUtils;
import com.alrbea.androidapp.data.model.PrayerTimes;
import android.view.View;
import android.widget.Button;

import java.util.ArrayList;
import java.util.List;

public class MainActivity extends BaseActivity {
    private PrayerViewModel prayerViewModel;
    private ThemeViewModel themeViewModel;
    private ImageButton btnSettings;
    private Button btnRefresh;
    private ConstraintLayout mainLayout;
    private ThemeTemplate currentTheme;
    private ViewBinding currentViewBinding;


    private LiveTimeManager liveTimeManager;
    private NewsTickerManager newsTickerManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        initViews();
        initViewModel();
        observeData();
        setupViewModels();
        initManagers();
        // Load today's prayer times for Riyadh as default
        prayerViewModel.loadTodayPrayerTimes("Makka");
    }


    private void initManagers() {
        // Initialize live time manager
        liveTimeManager = new LiveTimeManager(this);


        // Initialize news ticker manager
        newsTickerManager = new NewsTickerManager(this);

        // Initialize adhan notification manager
//        adhanNotificationManager = new AdhanNotificationManager(this);
    }


    private void initViews() {
        mainLayout = findViewById(R.id.main);
        btnSettings = findViewById(R.id.btn_settings);
        btnRefresh = findViewById(R.id.btn_refresh);

        // Setup settings button click listener
        btnSettings.setOnClickListener(v -> openSettings());

        // Setup refresh button click listener
        btnRefresh.setOnClickListener(v -> refreshPrayerTimes());

        // Apply responsive layout
        setupResponsiveLayout();

    }

    private void setupViewModels() {
        themeViewModel = new ViewModelProvider(this).get(ThemeViewModel.class);

        // Observe LiveData from ViewModel
        themeViewModel.getCurrentTheme().observe(this, this::applyTheme);
        themeViewModel.getAvailableThemes().observe(this, this::updateThemesList);
        themeViewModel.getLoadingState().observe(this, this::updateLoadingState);
        themeViewModel.getErrorMessage().observe(this, this::showError);

        // Note: No need to manually call loadCurrentTheme() as ViewModel constructor already does this
    }



    private void setData(){
        if(currentViewBinding instanceof ContentMainBlueBinding){
            currentViewBinding.getRoot().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivity(new Intent(MainActivity.this, SettingsActivity.class));
                }
            });
            ((ContentMainBlueBinding) currentViewBinding).movingMessageTextViewMainActivity.setText("Besher Qashar");
            liveTimeManager.setTimeTextView(((ContentMainBlueBinding) currentViewBinding).timeNowTextViewMainActivity);
            liveTimeManager.setDateTextView(((ContentMainBlueBinding) currentViewBinding).dateNowTextViewMainActivity);
//            liveTimeManager.setHijriDateTextView(tvHijriDate);
            liveTimeManager.startLiveTime();
            newsTickerManager.setNewsTextView(((ContentMainBlueBinding) currentViewBinding).movingMessageTextViewMainActivity);
            newsTickerManager.startTicker();

        }
    }
    private void applyTheme(ThemeTemplate theme) {
        if (theme == null) return;


        currentTheme = theme;
        Log.d("MainActivity", "Applying theme: " + theme.getName() + " (ID: " + theme.getId() + ")");

        // Apply background image based on orientation
        applyThemeBackground(theme);

        currentViewBinding = ContentMainBlueBinding.inflate(getLayoutInflater());
        // Update title to show theme name for debugging (can be removed in production)
        TextView titleView = findViewById(R.id.tv_title);
        if (titleView != null) {
            // You can customize this to show app name or keep theme name for debugging
            titleView.setText("مواقيت الصلاة - " + theme.getName());
        }

        // Apply any additional theme-specific styling
        applyThemeColors(theme);
    }

    private void applyThemeBackground(ThemeTemplate theme) {
        if (mainLayout == null || theme == null) return;

        try {

            // Determine current orientation
            boolean isLandscape = getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE;

            int layoutResource = isLandscape ? theme.getLandscapeView() : theme.getPortraitView();
            setContentView(currentViewBinding.getRoot());
            setData();

            // Get appropriate background resource
//            int backgroundResource = isLandscape ? theme.getLandscapeImage() : theme.getPortraitImage();
//
//            if (backgroundResource != 0) {
//                 Apply background image
//                Drawable backgroundDrawable = ContextCompat.getDrawable(this, backgroundResource);
//                if (backgroundDrawable != null) {
//                    mainLayout.setBackground(backgroundDrawable);
//                    Log.d("MainActivity", "Applied background: " + backgroundResource + " (landscape: " + isLandscape + ")");
//                } else {
//                    Log.w("MainActivity", "Could not load background resource: " + backgroundResource);
//                }
//            } else {
//                Log.w("MainActivity", "No background resource found for theme: " + theme.getId());
//                 Apply default background
//                mainLayout.setBackgroundColor(ContextCompat.getColor(this, R.color.background));
//            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error applying theme background", e);
            // Fallback to default background
            mainLayout.setBackgroundColor(ContextCompat.getColor(this, R.color.background));
        }
    }

    private void applyThemeColors(ThemeTemplate theme) {
        // Apply theme-specific colors to UI elements
        // This can be expanded based on your theme requirements

        try {
            // Example: Apply theme-specific tint to settings button
            if (btnSettings != null) {
                btnSettings.setColorFilter(ContextCompat.getColor(this, R.color.primary));
            }

            // You can add more theme-specific color applications here
            // For example: text colors, button colors, etc.

        } catch (Exception e) {
            Log.e("MainActivity", "Error applying theme colors", e);
        }
    }

    private void updateThemesList(List<ThemeTemplate> themes) {
//        if (themes != null && themeAdapter != null) {
//            themeAdapter.setThemes(themes);
//        }
    }

    private void updateLoadingState(Boolean isLoading) {
//        if (progressIndicator != null) {
//            progressIndicator.setVisibility(isLoading ? View.VISIBLE : View.GONE);
//        }
//        if (recyclerThemes != null) {
//            recyclerThemes.setVisibility(isLoading ? View.GONE : View.VISIBLE);
//        }
    }

    private void showError(String errorMessage) {
        if (errorMessage != null) {
            Toast.makeText(this, errorMessage, Toast.LENGTH_LONG).show();
        }
    }

    private void setupResponsiveLayout() {

        // Apply responsive padding to main container
        int padding = ScreenUtils.getScreenPadding(this);
        findViewById(R.id.main).setPadding(padding, padding, padding, padding);

        // Adjust text sizes based on screen type
        adjustTextSizes();
    }

    private void adjustTextSizes() {
        ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(this);
        float multiplier = ScreenUtils.getTextSizeMultiplier(this);

        // Apply responsive text size to title
        android.widget.TextView titleView = findViewById(R.id.tv_title);
        if (titleView != null) {
            float currentSize = titleView.getTextSize() / getResources().getDisplayMetrics().scaledDensity;
            titleView.setTextSize(currentSize * multiplier);
        }
    }

    private void initViewModel() {
        PrayerViewModelFactory factory = new PrayerViewModelFactory(this);
        prayerViewModel = new ViewModelProvider(this, factory).get(PrayerViewModel.class);
    }

    private void observeData() {
        prayerViewModel.getTodayPrayerTimes().observe(this, prayerTimes -> {
            if (prayerTimes != null) {
                // Convert single prayer times to list for adapter
                List<PrayerTimes> prayerTimesList = new ArrayList<>();
                prayerTimesList.add(prayerTimes);
//                adapter.updatePrayerTimes(prayerTimesList);
            }
        });

        prayerViewModel.getPrayerTimes().observe(this, prayerTimesList -> {
            if (prayerTimesList != null && !prayerTimesList.isEmpty()) {
//                adapter.updatePrayerTimes(prayerTimesList);
            }
        });

        prayerViewModel.getError().observe(this, error -> {
            if (error != null) {
                Log.e("PrayerTimesResponse", "Error: " + error);
                Toast.makeText(this, "Error: " + error, Toast.LENGTH_LONG).show();
            }
        });

        prayerViewModel.getLoading().observe(this, isLoading -> {
            // Handle loading state (show/hide progress bar)
            if (isLoading) {
                // Show loading indicator
                Toast.makeText(this, "Loading prayer times...", Toast.LENGTH_SHORT).show();
            }
        });

        prayerViewModel.getNeedsRefresh().observe(this, needsRefresh -> {
            if (needsRefresh) {
                btnRefresh.setVisibility(View.VISIBLE);
                Toast.makeText(this, "No data available. Tap refresh to load prayer times.", Toast.LENGTH_LONG).show();
            } else {
                btnRefresh.setVisibility(View.GONE);
            }
        });
    }

    private void openSettings() {
        Intent intent = new Intent(this, SettingsActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void refreshPrayerTimes() {
        btnRefresh.setVisibility(View.GONE);
        prayerViewModel.refreshData("Riyadh");
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        // Reapply theme when orientation changes
        if (currentTheme != null) {
            Log.d("MainActivity", "Orientation changed, reapplying theme");
            applyThemeBackground(currentTheme);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        // Reload current theme when activity resumes (in case theme was changed in settings)
        // This ensures we get the latest theme selection from SharedPreferences
        if (themeViewModel != null) {
            themeViewModel.getCurrentTheme().observe(this, this::applyTheme);

        }
    }
}