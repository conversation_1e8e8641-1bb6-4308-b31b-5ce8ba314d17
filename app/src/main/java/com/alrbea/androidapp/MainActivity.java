package com.alrbea.androidapp;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;

import com.alrbea.androidapp.data.model.ThemeTemplate;
import com.alrbea.androidapp.ui.BaseActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alrbea.androidapp.ui.SettingsActivity;
import com.alrbea.androidapp.ui.adapter.PrayerTimesAdapter;
import com.alrbea.androidapp.ui.viewmodel.PrayerViewModel;
import com.alrbea.androidapp.ui.viewmodel.PrayerViewModelFactory;
import com.alrbea.androidapp.ui.viewmodel.ThemeViewModel;
import com.alrbea.androidapp.utils.ResponsiveLayoutManager;
import com.alrbea.androidapp.utils.ResponsiveItemDecoration;
import com.alrbea.androidapp.utils.ScreenUtils;
import com.alrbea.androidapp.data.model.PrayerTimes;
import android.view.View;
import android.widget.Button;

import java.util.ArrayList;
import java.util.List;

public class MainActivity extends BaseActivity {
    private PrayerViewModel prayerViewModel;
    private ThemeViewModel themeViewModel;
    private PrayerTimesAdapter adapter;
    private RecyclerView recyclerView;
    private ImageButton btnSettings;
    private Button btnRefresh;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        initViews();
        initViewModel();
        observeData();
        setupViewModels();

        // Load today's prayer times for Riyadh as default
        prayerViewModel.loadTodayPrayerTimes("Makka");
    }


    private void initViews() {
        recyclerView = findViewById(R.id.recycler_view_prayer_times);
        btnSettings = findViewById(R.id.btn_settings);
        btnRefresh = findViewById(R.id.btn_refresh);
        adapter = new PrayerTimesAdapter();

        // Setup settings button click listener
        btnSettings.setOnClickListener(v -> openSettings());

        // Setup refresh button click listener
        btnRefresh.setOnClickListener(v -> refreshPrayerTimes());

        // Apply responsive layout
        setupResponsiveLayout();

        recyclerView.setAdapter(adapter);
    }

    private void setupViewModels() {
        themeViewModel = new ViewModelProvider(this).get(ThemeViewModel.class);

        // Observe LiveData from ViewModel
        themeViewModel.getCurrentTheme().observe(this, this::getCurrentTheme);
        themeViewModel.getAvailableThemes().observe(this, this::updateThemesList);
        themeViewModel.getLoadingState().observe(this, this::updateLoadingState);
        themeViewModel.getErrorMessage().observe(this, this::showError);


        // Trigger loading of themes
        themeViewModel.loadCurrentTheme();
    }


    private void getCurrentTheme(ThemeTemplate theme) {
        TextView textView = findViewById(R.id.tv_title);
        textView.setText(theme.getId());
    }

    private void updateThemesList(List<ThemeTemplate> themes) {
//        if (themes != null && themeAdapter != null) {
//            themeAdapter.setThemes(themes);
//        }
    }

    private void updateLoadingState(Boolean isLoading) {
//        if (progressIndicator != null) {
//            progressIndicator.setVisibility(isLoading ? View.VISIBLE : View.GONE);
//        }
//        if (recyclerThemes != null) {
//            recyclerThemes.setVisibility(isLoading ? View.GONE : View.VISIBLE);
//        }
    }

    private void showError(String errorMessage) {
        if (errorMessage != null) {
            Toast.makeText(this, errorMessage, Toast.LENGTH_LONG).show();
        }
    }

    private void setupResponsiveLayout() {
        // Apply responsive layout manager
        RecyclerView.LayoutManager layoutManager = ResponsiveLayoutManager
                .createLayoutManager(this, ResponsiveLayoutManager.LayoutType.PRAYER_TIMES_LIST);
        recyclerView.setLayoutManager(layoutManager);

        // Apply responsive item decoration
        ResponsiveItemDecoration itemDecoration = new ResponsiveItemDecoration(this, true);
        recyclerView.addItemDecoration(itemDecoration);

        // Apply responsive padding to main container
        int padding = ScreenUtils.getScreenPadding(this);
        findViewById(R.id.main).setPadding(padding, padding, padding, padding);

        // Adjust text sizes based on screen type
        adjustTextSizes();
    }

    private void adjustTextSizes() {
        ScreenUtils.ScreenType screenType = ScreenUtils.getScreenType(this);
        float multiplier = ScreenUtils.getTextSizeMultiplier(this);

        // Apply responsive text size to title
        android.widget.TextView titleView = findViewById(R.id.tv_title);
        if (titleView != null) {
            float currentSize = titleView.getTextSize() / getResources().getDisplayMetrics().scaledDensity;
            titleView.setTextSize(currentSize * multiplier);
        }
    }

    private void initViewModel() {
        PrayerViewModelFactory factory = new PrayerViewModelFactory(this);
        prayerViewModel = new ViewModelProvider(this, factory).get(PrayerViewModel.class);
    }

    private void observeData() {
        prayerViewModel.getTodayPrayerTimes().observe(this, prayerTimes -> {
            if (prayerTimes != null) {
                // Convert single prayer times to list for adapter
                List<PrayerTimes> prayerTimesList = new ArrayList<>();
                prayerTimesList.add(prayerTimes);
                adapter.updatePrayerTimes(prayerTimesList);
            }
        });

        prayerViewModel.getPrayerTimes().observe(this, prayerTimesList -> {
            if (prayerTimesList != null && !prayerTimesList.isEmpty()) {
                adapter.updatePrayerTimes(prayerTimesList);
            }
        });

        prayerViewModel.getError().observe(this, error -> {
            if (error != null) {
                Log.e("PrayerTimesResponse", "Error: " + error);
                Toast.makeText(this, "Error: " + error, Toast.LENGTH_LONG).show();
            }
        });

        prayerViewModel.getLoading().observe(this, isLoading -> {
            // Handle loading state (show/hide progress bar)
            if (isLoading) {
                // Show loading indicator
                Toast.makeText(this, "Loading prayer times...", Toast.LENGTH_SHORT).show();
            }
        });

        prayerViewModel.getNeedsRefresh().observe(this, needsRefresh -> {
            if (needsRefresh) {
                btnRefresh.setVisibility(View.VISIBLE);
                Toast.makeText(this, "No data available. Tap refresh to load prayer times.", Toast.LENGTH_LONG).show();
            } else {
                btnRefresh.setVisibility(View.GONE);
            }
        });
    }

    private void openSettings() {
        Intent intent = new Intent(this, SettingsActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private void refreshPrayerTimes() {
        btnRefresh.setVisibility(View.GONE);
        prayerViewModel.refreshData("Riyadh");
    }
}