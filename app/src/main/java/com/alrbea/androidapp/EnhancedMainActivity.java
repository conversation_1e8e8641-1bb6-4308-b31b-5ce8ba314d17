package com.alrbea.androidapp;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.alrbea.androidapp.data.model.PrayerTimeInfo;
import com.alrbea.androidapp.data.model.PrayerTimes;
import com.alrbea.androidapp.ui.SettingsActivity;
import com.alrbea.androidapp.ui.adapter.EnhancedPrayerTimesAdapter;
import com.alrbea.androidapp.ui.viewmodel.PrayerViewModel;
import com.alrbea.androidapp.ui.viewmodel.PrayerViewModelFactory;
import com.alrbea.androidapp.utils.AdhanNotificationManager;
import com.alrbea.androidapp.utils.LiveTimeManager;
import com.alrbea.androidapp.utils.NewsTickerManager;
import com.alrbea.androidapp.utils.PrayerTimeCalculator;
import java.util.ArrayList;
import java.util.List;

public class EnhancedMainActivity extends AppCompatActivity implements EnhancedPrayerTimesAdapter.OnPrayerTimeClickListener {
    
    private PrayerViewModel prayerViewModel;
    private EnhancedPrayerTimesAdapter adapter;
    private LiveTimeManager liveTimeManager;
    private NewsTickerManager newsTickerManager;
    private AdhanNotificationManager adhanNotificationManager;
    
    // Views
    private RecyclerView recyclerView;
    private ImageButton btnSettings;
    private Button btnRefresh;
    private ProgressBar progressBar;
    private TextView tvCurrentTime;
    private TextView tvGregorianDate;
    private TextView tvHijriDate;
    private TextView tvNewsTicker;
    private TextView tvNextPrayerName;
    private TextView tvNextPrayerTime;
    private TextView tvTimeUntilNextPrayer;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_enhanced_main);
        
        initViews();
        initViewModel();
        initManagers();
        setupObservers();
        setupRecyclerView();
        
        // Load prayer times
        prayerViewModel.loadTodayPrayerTimes("Riyadh");
    }
    
    private void initViews() {
        recyclerView = findViewById(R.id.recycler_view_prayer_times);
        btnSettings = findViewById(R.id.btn_settings);
        btnRefresh = findViewById(R.id.btn_refresh);
        progressBar = findViewById(R.id.progress_bar);
        tvCurrentTime = findViewById(R.id.tv_current_time);
        tvGregorianDate = findViewById(R.id.tv_gregorian_date);
        tvHijriDate = findViewById(R.id.tv_hijri_date);
        tvNewsTicker = findViewById(R.id.tv_news_ticker);
        tvNextPrayerName = findViewById(R.id.tv_next_prayer_name);
        tvNextPrayerTime = findViewById(R.id.tv_next_prayer_time);
        tvTimeUntilNextPrayer = findViewById(R.id.tv_time_until_next_prayer);
        
        btnSettings.setOnClickListener(v -> openSettings());
        btnRefresh.setOnClickListener(v -> refreshPrayerTimes());
    }
    
    private void initViewModel() {
        PrayerViewModelFactory factory = new PrayerViewModelFactory(this);
        prayerViewModel = new ViewModelProvider(this, factory).get(PrayerViewModel.class);
    }
    
    private void initManagers() {
        // Initialize live time manager
        liveTimeManager = new LiveTimeManager(this);
        liveTimeManager.setTimeTextView(tvCurrentTime);
        liveTimeManager.setDateTextView(tvGregorianDate);
        liveTimeManager.setHijriDateTextView(tvHijriDate);
        liveTimeManager.startLiveTime();
        
        // Initialize news ticker manager
        newsTickerManager = new NewsTickerManager(this);
        newsTickerManager.startTicker();
        
        // Initialize adhan notification manager
        adhanNotificationManager = new AdhanNotificationManager(this);
    }
    
    private void setupObservers() {
        // Observe prayer times
        prayerViewModel.getTodayPrayerTimes().observe(this, prayerTimes -> {
            if (prayerTimes != null) {
                updatePrayerTimesInfo(prayerTimes);
            }
        });
        
        // Observe loading state
        prayerViewModel.getLoading().observe(this, isLoading -> {
            progressBar.setVisibility(isLoading ? View.VISIBLE : View.GONE);
            if (isLoading) {
                Toast.makeText(this, "جاري تحميل مواقيت الصلاة...", Toast.LENGTH_SHORT).show();
            }
        });
        
        // Observe error state
        prayerViewModel.getError().observe(this, error -> {
            if (error != null && !error.isEmpty()) {
                Toast.makeText(this, "خطأ: " + error, Toast.LENGTH_LONG).show();
            }
        });
        
        // Observe needs refresh state
        prayerViewModel.getNeedsRefresh().observe(this, needsRefresh -> {
            if (needsRefresh) {
                btnRefresh.setVisibility(View.VISIBLE);
                Toast.makeText(this, "لا توجد بيانات متاحة. اضغط تحديث لتحميل مواقيت الصلاة.", Toast.LENGTH_LONG).show();
            } else {
                btnRefresh.setVisibility(View.GONE);
            }
        });
    }
    
    private void setupRecyclerView() {
        adapter = new EnhancedPrayerTimesAdapter(this, new ArrayList<>());
        adapter.setOnPrayerTimeClickListener(this);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }
    
    private void updatePrayerTimesInfo(PrayerTimes prayerTimes) {
        List<PrayerTimeInfo> prayerTimeInfoList = PrayerTimeCalculator.calculatePrayerTimesInfo(prayerTimes, this);
        adapter.updateData(prayerTimeInfoList);
        
        // Update next prayer info
        PrayerTimeInfo nextPrayer = PrayerTimeCalculator.getNextPrayer(prayerTimeInfoList);
        if (nextPrayer != null) {
            tvNextPrayerName.setText(nextPrayer.getPrayerName());
            tvNextPrayerTime.setText(nextPrayer.getPrayerTime());
            tvTimeUntilNextPrayer.setText(nextPrayer.getFormattedTimeUntilPrayer());
        }
        
        // Check for adhan notifications
        checkAdhanNotifications(prayerTimeInfoList);
    }
    
    private void checkAdhanNotifications(List<PrayerTimeInfo> prayerTimeInfoList) {
        for (PrayerTimeInfo prayerTimeInfo : prayerTimeInfoList) {
            // Show notification when prayer time arrives
            if (prayerTimeInfo.getTimeUntilPrayer() <= 0 && prayerTimeInfo.getTimeUntilPrayer() > -60) {
                adhanNotificationManager.showAdhanNotification(prayerTimeInfo);
            }
            
            // Show notification when iqama time arrives
            if (prayerTimeInfo.getTimeUntilIqama() <= 0 && prayerTimeInfo.getTimeUntilIqama() > -60) {
                adhanNotificationManager.showIqamaNotification(prayerTimeInfo);
            }
            
            // Show next prayer notification
            if (prayerTimeInfo.isNextPrayer() && prayerTimeInfo.getTimeUntilPrayer() <= 300) { // 5 minutes before
                adhanNotificationManager.showNextPrayerNotification(prayerTimeInfo);
            }
        }
    }
    
    private void openSettings() {
        Intent intent = new Intent(this, SettingsActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }
    
    private void refreshPrayerTimes() {
        btnRefresh.setVisibility(View.GONE);
        prayerViewModel.refreshData("Riyadh");
    }
    
    @Override
    public void onPrayerTimeClick(PrayerTimeInfo prayerTimeInfo) {
        // Handle prayer time click - could show detailed view or azkar
        Toast.makeText(this, "تم اختيار " + prayerTimeInfo.getPrayerName(), Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void onAzkarClick(PrayerTimeInfo prayerTimeInfo) {
        // Show azkar dialog or activity
        Toast.makeText(this, "أذكار " + prayerTimeInfo.getPrayerName(), Toast.LENGTH_SHORT).show();
        // You can implement a dialog or activity to show the azkar
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        liveTimeManager.onResume();
        newsTickerManager.onResume();
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        liveTimeManager.onPause();
        newsTickerManager.onPause();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        liveTimeManager.onDestroy();
        newsTickerManager.onDestroy();
        adhanNotificationManager.cancelAllNotifications();
    }
} 